import 'dotenv/config';
import { ExpoConfig, ConfigContext } from 'expo/config';

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: process.env.ENV === 'production' ? 'Conari' : 'Conari Staging',
  slug: process.env.ENV === 'production' ? 'Conari' : 'Conari Staging',
  version: '1.7.0',
  orientation: 'portrait',
  icon:
    process.env.ENV === 'production'
      ? './assets/images/icon.png'
      : './assets/images/staging-icon.png',
  scheme: process.env.ENV === 'production' ? 'conari' : 'conaristaging',
  userInterfaceStyle: 'automatic',
  newArchEnabled: true,
  ios: {
    supportsTablet: false,
    bundleIdentifier:
      process.env.ENV === 'production'
        ? 'com.conari.app'
        : 'com.conari.app.stage',
    googleServicesFile:
      process.env.ENV === 'production'
        ? './firebase/production/GoogleService-Info.plist'
        : './firebase/staging/GoogleService-Info.plist',
    usesAppleSignIn: true,
    buildNumber: '1', // Optional: Update for iOS
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
      UIBackgroundModes: ['remote-notification'],
      NSLocationWhenInUseUsageDescription:
        'Want to see how close these places are? Turn on location for a better experience.',
      NSPhotoLibraryUsageDescription:
        'Allow access to your photo library to upload reels.',
    },
    entitlements: {
      'aps-environment': process.env.APS_ENVIRONMENT,
    },
    associatedDomains:
      process.env.ENV === 'production'
        ? ['applinks:conari.app.link', 'applinks:conari-alternate.app.link']
        : [
            'applinks:conari.test-app.link',
            'applinks:conari-alternate.test-app.link',
          ],
  },
  android: {
    package:
      process.env.ENV === 'production'
        ? 'com.conari.app'
        : 'com.conari.app.stage',
    googleServicesFile:
      process.env.ENV === 'production'
        ? './firebase/production/google-services.json'
        : './firebase/staging/google-services.json',
    adaptiveIcon: {
      foregroundImage: './assets/images/adaptive-icon.png',
      backgroundColor: '#ffffff',
    },
  },
  notification: {
    icon: './assets/images/icon.png',
  },
  web: {
    bundler: 'metro',
    output: 'static',
    favicon: './assets/images/favicon.png',
  },
  plugins: [
    'expo-router',
    [
      'expo-splash-screen',
      {
        image: './assets/images/splash-icon.png',
        imageWidth: 300,
        resizeMode: 'contain',
        backgroundColor: '#ffffff',
      },
    ],
    '@react-native-firebase/app',
    '@react-native-firebase/messaging',
    [
      'expo-build-properties',
      {
        ios: {
          useFrameworks: 'static',
        },
      },
    ],
    [
      '@config-plugins/react-native-branch',
      {
        apiKey: process.env.BRANCH_KEY,
        iosAppDomain: ['conari.app.link', 'conari-alternate.app.link'],
      },
    ],
    [
      '@sentry/react-native/expo',
      {
        url: 'https://sentry.io/',
        project: 'conari-mobile',
        organization: 'tcf-sala',
      },
    ],
    ['react-native-map-link'],
  ],
  experiments: {
    typedRoutes: true,
  },
  extra: {
    GRAPHQL_URL: process.env.GRAPHQL_URL,
    BRANCH_URL: process.env.BRANCH_URL,
    BRANCH_KEY: process.env.BRANCH_KEY,
    APS_ENVIRONMENT: process.env.APS_ENVIRONMENT,
    ENV: process.env.ENV,
    ALGOLIA_APP_ID: process.env.ALGOLIA_APP_ID,
    ALGOLIA_SEARCH_KEY: process.env.ALGOLIA_SEARCH_KEY,
    TOKEN: process.env.TOKEN,
    SEGMENT_WRITE_KEY: process.env.SEGMENT_WRITE_KEY,
    JITSU_DOMAIN: process.env.JITSU_DOMAIN,
    SENTRY_DSN: process.env.SENTRY_DSN,
  },
});
