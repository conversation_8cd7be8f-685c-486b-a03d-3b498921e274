{
  "name": "conari",
  "main": "expo-router/entry",
  "version": "0.0.1",
  "engines": {
    "node": "18"
  },
  "packageManager": "npm@10.8.2",
  "scripts": {
    "start": "expo start",
    "reset-project": "node ./scripts/reset-project.js",
    "android": "expo run:android",
    "ios": "expo run:ios",
    "web": "expo start --web",
    "test": "jest --watchAll",
    "lint": "expo lint",
    "lint:fix": "expo lint && npm run format",
    "codegen": "graphql-codegen",
    "codegen:watch": "graphql-codegen --watch",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "typecheck": "tsc --noEmit",
    "postinstall": "patch-package"
  },
  "jest": {
    "preset": "jest-expo"
  },
  "dependencies": {
    "@apollo/client": "^3.13.0",
    "@config-plugins/react-native-branch": "^10.0.0",
    "@expo/vector-icons": "^14.0.2",
    "@hookform/resolvers": "^4.1.0",
    "@ptomasroos/react-native-multi-slider": "^2.2.2",
    "@react-native-async-storage/async-storage": "^1.23.1",
<<<<<<< HEAD
    "@react-native-firebase/analytics": "^21.14.0",
    "@react-native-firebase/app": "^21.14.0",
    "@react-native-firebase/messaging": "^21.14.0",
    "@react-native-firebase/remote-config": "^21.14.0",
    "@react-native-google-signin/google-signin": "^15.0.0",
=======
    "@react-native-community/netinfo": "11.4.1",
    "@react-native-firebase/analytics": "^21.13.0",
    "@react-native-firebase/app": "^21.13.0",
    "@react-native-firebase/messaging": "^21.13.0",
    "@react-native-firebase/remote-config": "^22.2.0",
>>>>>>> 6e206b3244e64228377ed631dd583893c3c26f76
    "@react-native-menu/menu": "^1.2.3",
    "@react-navigation/bottom-tabs": "^7.2.0",
    "@react-navigation/native": "^7.0.14",
    "@segment/analytics-react-native": "^2.21.1",
    "@segment/analytics-react-native-plugin-branch": "^1.1.2",
    "@segment/analytics-react-native-plugin-firebase": "^0.4.3",
    "@segment/sovran-react-native": "^1.1.3",
    "@sentry/react-native": "^6.16.0",
    "algoliasearch": "^5.20.3",
    "axios": "^1.9.0",
    "date-fns": "^4.1.0",
    "expo": "~52.0.42",
    "expo-apple-authentication": "~7.1.3",
    "expo-application": "~6.0.2",
    "expo-av": "~15.0.2",
    "expo-blur": "~14.0.3",
    "expo-build-properties": "~0.13.2",
    "expo-constants": "~17.0.6",
    "expo-device": "~7.0.3",
    "expo-file-system": "~18.0.12",
    "expo-font": "~13.0.3",
    "expo-haptics": "~14.0.1",
    "expo-image": "~2.0.7",
    "expo-image-picker": "~16.0.6",
    "expo-linear-gradient": "~14.0.2",
    "expo-linking": "~7.0.5",
    "expo-location": "~18.0.10",
    "expo-router": "~4.0.20",
    "expo-sensors": "~14.0.2",
    "expo-splash-screen": "~0.29.22",
    "expo-status-bar": "~2.0.1",
    "expo-symbols": "~0.2.2",
    "expo-system-ui": "~4.0.9",
    "expo-updates": "~0.27.4",
    "expo-web-browser": "~14.0.2",
    "geolib": "^3.3.4",
    "graphql": "^16.11.0",
    "i18next": "^24.2.2",
    "lottie-react-native": "^7.1.0",
    "react": "18.3.1",
    "react-dom": "18.3.1",
    "react-i18next": "^15.4.1",
    "react-instantsearch-core": "^7.15.3",
    "react-native": "0.76.9",
    "react-native-branch": "6.4.0",
    "react-native-gesture-handler": "~2.20.2",
    "react-native-get-random-values": "^1.11.0",
    "react-native-image-zoom-viewer": "^3.0.1",
    "react-native-keyboard-aware-scroll-view": "^0.9.5",
    "react-native-map-link": "^3.9.0",
    "react-native-modalize": "^2.1.1",
    "react-native-portalize": "^1.0.7",
    "react-native-reanimated": "~3.16.1",
    "react-native-reanimated-carousel": "^4.0.2",
    "react-native-root-siblings": "4.1.1",
    "react-native-root-toast": "^3.6.0",
    "react-native-safe-area-context": "5.5.1",
    "react-native-screens": "~4.4.0",
    "react-native-svg": "15.8.0",
    "react-native-web": "~0.19.13",
    "react-native-webview": "13.13.5",
    "rn-pdf-reader-js": "^4.1.1",
    "search-insights": "^2.17.3",
    "zod": "^3.24.2",
<<<<<<< HEAD
    "zustand": "^5.0.3"
=======
    "zustand": "^5.0.3",
    "@shopify/flash-list": "1.7.3"
>>>>>>> 6e206b3244e64228377ed631dd583893c3c26f76
  },
  "devDependencies": {
    "@babel/core": "^7.25.2",
    "@graphql-codegen/cli": "^5.0.5",
    "@graphql-codegen/typescript": "^4.1.3",
    "@graphql-codegen/typescript-operations": "^4.4.1",
    "@graphql-codegen/typescript-react-apollo": "^4.3.3",
    "@trivago/prettier-plugin-sort-imports": "^5.2.2",
    "@types/google-libphonenumber": "^7.4.30",
    "@types/jest": "^29.5.12",
    "@types/react": "~18.3.12",
    "@types/react-test-renderer": "^18.3.0",
    "dotenv": "^16.5.0",
    "eslint": "^8.57.0",
    "eslint-config-expo": "~8.0.1",
    "eslint-config-prettier": "^10.0.1",
    "eslint-plugin-prettier": "^5.2.3",
    "jest": "^29.2.1",
    "jest-expo": "~52.0.6",
    "patch-package": "^8.0.0",
    "prettier": "^3.5.1",
    "react-test-renderer": "18.3.1",
    "reactotron-core-client": "^2.9.7",
    "reactotron-react-js": "^3.3.16",
    "reactotron-react-native": "^5.1.12",
    "typescript": "^5.3.3"
  },
  "private": true
}
