import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { LoginStatusCode, MeQuery } from '@/graphql/generated/graphql';
import * as Sentry from '@sentry/react-native';
import { analyticsService } from '@/services/analytics';
import Constants from 'expo-constants';

interface AuthState {
  user: MeQuery['me'] | null;
  token: string | null;
  statusCode: LoginStatusCode | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  hasSeenOnboarding: boolean;
  isTrialModalOpened: boolean;
  pendingInviteToken: string | null;
  setUser: (user: MeQuery['me']) => void;
  setToken: (token: string) => void;
  setStatusCode: (statusCode: LoginStatusCode | null) => void;
  logout: (clearFcmToken?: () => void) => void;
  initAuth: () => Promise<void>;
  setHasSeenOnboarding: () => Promise<void>;
  checkOnboardingStatus: () => Promise<boolean>;
  setTrialModalOpened: () => void;
  shouldShowTrialModal: () => boolean;
  setPendingInviteToken: (token: string | null) => void;
  getPendingInviteToken: () => string | null;
  clearPendingInviteToken: () => void;
  // Dev mode simulation
  simulateDevLogin: () => void;
}

/**
 * Helper function for user identification with Sentry and Segment
 */
const identifyUser = (user: MeQuery['me'], context?: string) => {
  // Set user in Sentry
  Sentry.setUser({
    id: user.id,
    email: user.email,
    name: user.name,
  });

  // Identify user with Segment through analytics service
  try {
    analyticsService.identifyUser(user.id, {
      name: user.name,
      email: user.email,
      status: user.status,
      createdAt: user.created_at,
      isCreator: !!user.creator?.id,
      creatorId: user.creator?.id,
    });

    if (__DEV__ && context) {
      console.log(`User identified via ${context}:`, user.id);
    }
  } catch (error) {
    console.error(
      `Failed to identify user via ${context || 'unknown'}:`,
      error
    );
  }
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      statusCode: null,
      isLoading: true,
      isAuthenticated: false,
      hasSeenOnboarding: false,
      isTrialModalOpened: false,
      pendingInviteToken: null,

      setUser: (user) => {
        set({
          user,
          isAuthenticated: true,
        });

        // Use helper method for identification
        identifyUser(user, 'login');

        // Track login event
        try {
          if (__DEV__) {
            analyticsService.trackUserLogin('dev_simulation');
          }
        } catch (error) {
          console.error('Failed to track login event:', error);
        }
      },

      setToken: (token) =>
        set({
          token,
          isAuthenticated: true,
        }),

      setStatusCode: (statusCode) =>
        set({
          statusCode,
        }),

      // Development mode login simulation
      simulateDevLogin: () => {
        if (!__DEV__) {
          console.warn(
            'simulateDevLogin is only available in development mode'
          );
          return;
        }

        console.log('[Auth] Simulating dev login with provided token');

        // Mock user data for development
        const mockUser: MeQuery['me'] = {
          __typename: 'User',
          id: 'mamdouh-dev-123',
          name: 'Mamdouh Dev',
          email: '<EMAIL>',
          status: LoginStatusCode.Success,
          created_at: new Date().toISOString(),
          creator: {
            __typename: 'Creator',
            id: 'dev-creator-123',
          },
          invitations: {
            __typename: 'UserInvitations',
            info: {
              __typename: 'InvitationInfo',
              remaining: 5,
              total: 10,
            },
          },
        };
        // Set the provided dev token
        const devToken = Constants.expoConfig?.extra?.TOKEN;
        // Update auth state
        set({
          token: devToken,
          user: mockUser,
          statusCode: LoginStatusCode.Success,
          isAuthenticated: true,
        });

        // Use helper method for identification
        identifyUser(mockUser, 'dev-simulation');

        // Track dev login event
        try {
          analyticsService.trackUserLogin('dev_simulation');
        } catch (error) {
          console.error('Failed to track dev login event:', error);
        }

        // Navigate to main app
        router.replace('/(tabs)');
      },

      logout: async (clearFcmToken) => {
        if (clearFcmToken) {
          await clearFcmToken();
        }
        // Track logout event with analytics service
        try {
          analyticsService.trackUserLogout();

          // Reset analytics user data
          analyticsService.resetUser();
        } catch (error) {
          console.error(
            'Failed to track logout with analytics service:',
            error
          );
        }

        await AsyncStorage.removeItem('auth-storage');
        set({
          user: null,
          token: null,
          statusCode: null,
          isAuthenticated: false,
          // Clear invite-related variables
          pendingInviteToken: null,
        });

        //clear user in sentry
        Sentry.setUser(null);
        router.replace('/(auth)/onboarding');
      },

      initAuth: async () => {
        // console.log('##initAuth##');
        set({ isLoading: true });
        try {
          const storedAuth = await AsyncStorage.getItem('auth-storage');
          if (storedAuth) {
            const { state } = JSON.parse(storedAuth);
            console.log('state==>', state);
            if (state.token && state.user) {
              set({
                token: state.token,
                user: state.user,
                statusCode: state.statusCode,
                isAuthenticated: true,
              });

              // Use helper method for identification
              identifyUser(state.user, 'app-startup');

              console.log('Auth state restored for user:', state.user?.id);
            }
          }
        } catch (error) {
          console.error('Error initializing auth:', error);
        } finally {
          set({ isLoading: false });
        }
      },

      setHasSeenOnboarding: async () => {
        await AsyncStorage.setItem('hasSeenOnboarding', 'true');
        set({ hasSeenOnboarding: true });
      },

      checkOnboardingStatus: async () => {
        const hasSeenOnboarding =
          await AsyncStorage.getItem('hasSeenOnboarding');
        set({ hasSeenOnboarding: !!hasSeenOnboarding });
        return !!hasSeenOnboarding;
      },

      setTrialModalOpened: () => set({ isTrialModalOpened: true }),

      shouldShowTrialModal: () => {
        const { isAuthenticated, isTrialModalOpened } = get();
        return isAuthenticated && !isTrialModalOpened;
      },

      setPendingInviteToken: (token) => set({ pendingInviteToken: token }),
      getPendingInviteToken: () => get().pendingInviteToken,
      clearPendingInviteToken: () => set({ pendingInviteToken: null }),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        statusCode: state.statusCode,
        hasSeenOnboarding: state.hasSeenOnboarding,
        isTrialModalOpened: state.isTrialModalOpened,
        pendingInviteToken: state.pendingInviteToken,
      }),
    }
  )
);
