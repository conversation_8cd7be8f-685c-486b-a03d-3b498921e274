import { useAuthStore } from '@/store/auth';
import { router } from 'expo-router';
import { StyleSheet, View, Platform, Alert, Image } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { moderateScale, SCREEN_WIDTH, verticalScale } from '@/utils/scaling';
import { Button } from '@/components/ui/Button';
import { useTranslation } from 'react-i18next';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { colors } from '@/constants/Colors';
import * as AppleAuthentication from 'expo-apple-authentication';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {
  SocialLoginProvider,
  useSocialLoginMutation,
  LoginStatusCode,
} from '@/graphql/generated/graphql';
import LottieView from 'lottie-react-native';
import { deviceService } from '@/services';
import { useState, useEffect } from 'react';

export default function OnboardingScreen() {
  const [socialLogin] = useSocialLoginMutation();
  const [isAppleSignInLoading, setIsAppleSignInLoading] = useState(false);
  const [isGoogleSignInLoading, setIsGoogleSignInLoading] = useState(false);
  const setUser = useAuthStore((state) => state.setUser);
  const setToken = useAuthStore((state) => state.setToken);
  const setStatusCode = useAuthStore((state) => state.setStatusCode);
  const simulateDevLogin = useAuthStore((state) => state.simulateDevLogin);
  const { t } = useTranslation();

  useEffect(() => {
    // Configure Google Sign-In
    GoogleSignin.configure({
      // You'll need to get this from your Google Cloud Console
      // webClientId: 'YOUR_WEB_CLIENT_ID.googleusercontent.com',
      offlineAccess: true,
    });
  }, []);

  const handleAppleSignIn = async () => {
    try {
      setIsAppleSignInLoading(true);
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });
      //throw error if no token from apple sign in
      if (!credential.identityToken) {
        throw new Error('No token returned from Apple Sign In');
      }
      //get fullName [givenName, familyName]
      const fullName = credential.fullName;
      const firstName = fullName?.givenName;
      const lastName = fullName?.familyName;
      const { data } = await socialLogin({
        variables: {
          provider: SocialLoginProvider.Apple,
          token: credential.identityToken,
          firstName,
          lastName,
        },
      });
      if (data?.socialLogin) {
        setUser(data.socialLogin.user);
        setToken(data.socialLogin.token);

        // Check if status_code exists before setting it
        if (data.socialLogin.status_code) {
          setStatusCode(data.socialLogin.status_code);
        }

        // Redirect based on status code
        if (data.socialLogin.status_code === LoginStatusCode.Success) {
          router.replace('/(tabs)');
        } else if (data.socialLogin.status_code) {
          router.replace('/(auth)/awaiting-approval');
        } else {
          router.replace('/(tabs)');
        }
      }
    } catch (e) {
      Alert.alert(t('common.error'), t('login.appleError.message') + e);
    } finally {
      setIsAppleSignInLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setIsGoogleSignInLoading(true);
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();
      
      if (!userInfo.data?.idToken) {
        throw new Error('No token returned from Google Sign In');
      }

      const { data } = await socialLogin({
        variables: {
          provider: SocialLoginProvider.Google,
          token: userInfo.data.idToken,
          firstName: userInfo.data.user.givenName,
          lastName: userInfo.data.user.familyName,
        },
      });

      if (data?.socialLogin) {
        setUser(data.socialLogin.user);
        setToken(data.socialLogin.token);

        // Check if status_code exists before setting it
        if (data.socialLogin.status_code) {
          setStatusCode(data.socialLogin.status_code);
        }

        // Redirect based on status code
        if (data.socialLogin.status_code === LoginStatusCode.Success) {
          router.replace('/(tabs)');
        } else if (data.socialLogin.status_code) {
          router.replace('/(auth)/awaiting-approval');
        } else {
          router.replace('/(tabs)');
        }
      }
    } catch (error: any) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        // User cancelled the login flow
        return;
      } else if (error.code === statusCodes.IN_PROGRESS) {
        // Operation (e.g. sign in) is in progress already
        return;
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        Alert.alert(t('common.error'), 'Play services not available or outdated');
      } else {
        Alert.alert(t('common.error'), t('login.googleError.message') + error);
      }
    } finally {
      setIsGoogleSignInLoading(false);
    }
  };

  return (
    <Screen>
      <View style={styles.content}>
        <View style={styles.animationContainer}>
          {/* if old device or dev mode render image otw render LottieView animation */}
          {deviceService.isOldDevice() || __DEV__ ? (
            <Image
              source={require('@/assets/images/onboarding.png')}
              style={styles.image}
              resizeMode='contain'
            />
          ) : (
            <LottieView
              source={require('@/assets/lottie/onboarding.json')}
              style={styles.animation}
              autoPlay
              loop
            />
          )}
        </View>

        <View style={styles.textContainer}>
          <Text variant='h1' align='center'>
            {t('onboarding.title')}
          </Text>

          <View style={styles.textContainerSubtitle}>
            <Text variant='caption' align='center'>
              {t('onboarding.subtitle')}
            </Text>
          </View>
        </View>
        <View style={styles.buttonContainer}>
          {__DEV__ && (
            <Button
              variant='primary'
              title='🚀 Dev Login (Skip Apple)'
              onPress={simulateDevLogin}
              style={styles.devButton}
            />
          )}
          {Platform.OS === 'ios' && (
            <Button
              variant='secondary'
              title={t('login.continueApple')}
              leftIcon={
                <IconSymbol
                  name='apple.logo'
                  size={24}
                  color={colors.base[950]}
                />
              }
              onPress={handleAppleSignIn}
              disabled={isAppleSignInLoading}
              loading={isAppleSignInLoading}
            />
          )}
          {Platform.OS === 'android' && (
            <Button
              variant='secondary'
              title={t('login.continueGoogle')}
              leftIcon={
                <IconSymbol
                  name='globe'
                  size={24}
                  color={colors.base[950]}
                />
              }
              onPress={handleGoogleSignIn}
              disabled={isGoogleSignInLoading}
              loading={isGoogleSignInLoading}
            />
          )}
          <View style={styles.invitationTextContainer}>
            <Text
              variant='caption1'
              align='center'
              color={colors.primary[950]}
              weight='bold'
            >
              {t('onboarding.invitationText')}
            </Text>
          </View>
        </View>
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    padding: verticalScale(20),
    alignItems: 'center',
  },
  animationContainer: {
    position: 'relative',
    top: verticalScale(-10),
    marginLeft: moderateScale(5),
    width: SCREEN_WIDTH * 1.1,
    height: SCREEN_WIDTH * 1.1,
  },
  animation: {
    width: '120%',
    height: '120%',
    alignSelf: 'center',
  },
  image: {
    width: '100%',
    height: '110%',
    alignSelf: 'center',
    marginTop: verticalScale(30),
  },
  textContainer: {
    width: '100%',
    marginTop: verticalScale(56),
  },
  textContainerSubtitle: {
    marginTop: verticalScale(8),
    width: '85%',
    alignSelf: 'center',
  },
  invitationTextContainer: {
    marginVertical: verticalScale(12),
  },
  buttonContainer: {
    position: 'absolute',
    bottom: verticalScale(0),
    width: '100%',
  },
  devButton: {
    marginBottom: verticalScale(12),
    backgroundColor: colors.secondary[600],
  },
});
