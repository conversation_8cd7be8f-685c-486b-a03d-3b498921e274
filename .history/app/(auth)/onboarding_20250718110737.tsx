import { useAuthStore } from '@/store/auth';
import { router } from 'expo-router';
import { StyleSheet, View, Platform, Alert, Image } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { moderateScale, SCREEN_WIDTH, verticalScale } from '@/utils/scaling';
import { Button } from '@/components/ui/Button';
import { useTranslation } from 'react-i18next';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { colors } from '@/constants/Colors';
import * as AppleAuthentication from 'expo-apple-authentication';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {
  SocialLoginProvider,
  useSocialLoginMutation,
  LoginStatusCode,
} from '@/graphql/generated/graphql';
import LottieView from 'lottie-react-native';
import { deviceService } from '@/services';
import { useState, useEffect } from 'react';

export default function OnboardingScreen() {
  const [socialLogin] = useSocialLoginMutation();
  const [isAppleSignInLoading, setIsAppleSignInLoading] = useState(false);
  const [isGoogleSignInLoading, setIsGoogleSignInLoading] = useState(false);
  const setUser = useAuthStore((state) => state.setUser);
  const setToken = useAuthStore((state) => state.setToken);
  const setStatusCode = useAuthStore((state) => state.setStatusCode);
  const simulateDevLogin = useAuthStore((state) => state.simulateDevLogin);
  const { t } = useTranslation();

  useEffect(() => {
    // Configure Google Sign-In with the correct Web client ID that matches our Android client
    GoogleSignin.configure({
      webClientId:
        '630298916337-49qdkml7oe4r53f9h1c2k908248kv0q8.apps.googleusercontent.com',
      offlineAccess: true,
    });
  }, []);

  const handleAppleSignIn = async () => {
    try {
      setIsAppleSignInLoading(true);
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });
      //throw error if no token from apple sign in
      if (!credential.identityToken) {
        throw new Error('No token returned from Apple Sign In');
      }
      //get fullName [givenName, familyName]
      const fullName = credential.fullName;
      const firstName = fullName?.givenName;
      const lastName = fullName?.familyName;
      const { data } = await socialLogin({
        variables: {
          provider: SocialLoginProvider.Apple,
          token: credential.identityToken,
          firstName,
          lastName,
        },
      });
      if (data?.socialLogin) {
        setUser(data.socialLogin.user);
        setToken(data.socialLogin.token);

        // Check if status_code exists before setting it
        if (data.socialLogin.status_code) {
          setStatusCode(data.socialLogin.status_code);
        }

        // Redirect based on status code
        if (data.socialLogin.status_code === LoginStatusCode.Success) {
          router.replace('/(tabs)');
        } else if (data.socialLogin.status_code) {
          router.replace('/(auth)/awaiting-approval');
        } else {
          router.replace('/(tabs)');
        }
      }
    } catch (e) {
      Alert.alert(t('common.error'), t('login.appleError.message') + e);
    } finally {
      setIsAppleSignInLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setIsGoogleSignInLoading(true);

      // Check if device supports Google Play Services
      await GoogleSignin.hasPlayServices();

      // Sign in with Google
      const userInfo = await GoogleSignin.signIn();
      console.log(
        '🔍 FULL Google userInfo object:',
        JSON.stringify(userInfo, null, 2)
      );

      // Get the ID token for backend authentication
      const tokens = await GoogleSignin.getTokens();
      console.log(
        '🔍 FULL Google tokens object:',
        JSON.stringify(tokens, null, 2)
      );

      // Log token details
      console.log('🔑 Token Analysis:', {
        idTokenExists: !!tokens.idToken,
        idTokenLength: tokens.idToken?.length || 0,
        idTokenType: typeof tokens.idToken,
        accessTokenExists: !!tokens.accessToken,
        accessTokenLength: tokens.accessToken?.length || 0,
        idTokenStart: tokens.idToken?.substring(0, 50) + '...' || 'null',
        idTokenEnd: tokens.idToken
          ? '...' + tokens.idToken.substring(tokens.idToken.length - 50)
          : 'null',
      });

      // Try to decode the JWT token to see its contents (just for debugging)
      if (tokens.idToken) {
        try {
          const tokenParts = tokens.idToken.split('.');
          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            console.log(
              '🔍 JWT Token Payload:',
              JSON.stringify(payload, null, 2)
            );
          }
        } catch (e) {
          console.log('⚠️ Could not decode JWT token:', e);
        }
      }

      if (!tokens.idToken) {
        throw new Error('No ID token returned from Google Sign In');
      }

      // Extract user information - userInfo has the user data directly
      const firstName = (userInfo as any).user?.givenName || 'Google';
      const lastName = (userInfo as any).user?.familyName || 'User';
      const email = (userInfo as any).user?.email;
      const googleId = (userInfo as any).user?.id;

      console.log('👤 Extracted User Data:', {
        firstName,
        lastName,
        email,
        googleId,
        fullName: (userInfo as any).user?.name,
        photo: (userInfo as any).user?.photo,
      });

      // Prepare the exact payload we're sending to backend
      const socialLoginPayload = {
        provider: SocialLoginProvider.Google,
        token: tokens.idToken,
        firstName,
        lastName,
      };

      console.log(
        '📤 EXACT payload being sent to backend socialLogin mutation:',
        JSON.stringify(socialLoginPayload, null, 2)
      );
      console.log(
        '📤 Token being sent (first 100 chars):',
        tokens.idToken.substring(0, 100) + '...'
      );
      console.log(
        '📤 Token being sent (last 100 chars):',
        '...' + tokens.idToken.substring(tokens.idToken.length - 100)
      );

      // Call backend API
      console.log('🚀 Calling socialLogin mutation...');
      const { data } = await socialLogin({
        variables: socialLoginPayload,
      });

      console.log(
        '✅ Backend response received successfully:',
        JSON.stringify(data, null, 2)
      );

      if (data?.socialLogin) {
        setUser(data.socialLogin.user);
        setToken(data.socialLogin.token);

        // Check if status_code exists before setting it
        if (data.socialLogin.status_code) {
          setStatusCode(data.socialLogin.status_code);
        }

        // Redirect based on status code
        if (data.socialLogin.status_code === LoginStatusCode.Success) {
          router.replace('/(tabs)');
        } else if (data.socialLogin.status_code) {
          router.replace('/(auth)/awaiting-approval');
        } else {
          router.replace('/(tabs)');
        }
      }
    } catch (error: any) {
      console.error('❌ Google Sign-In Error:', error);

      // Log comprehensive error details for backend issues
      console.error('🔍 FULL Error Object:', JSON.stringify(error, null, 2));

      if (error.message) {
        console.error('🔍 Error Message:', error.message);
      }

      if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        console.error(
          '🔍 GraphQL Errors:',
          JSON.stringify(error.graphQLErrors, null, 2)
        );
        error.graphQLErrors.forEach((gqlError: any, index: number) => {
          console.error(`🔍 GraphQL Error ${index + 1}:`, {
            message: gqlError.message,
            locations: gqlError.locations,
            path: gqlError.path,
            extensions: gqlError.extensions,
          });

          // Extract specific validation errors
          if (gqlError.extensions?.validation) {
            console.error(
              `🔍 Validation Errors for Error ${index + 1}:`,
              gqlError.extensions.validation
            );

            // Log token-specific validation errors
            if (gqlError.extensions.validation.token) {
              console.error(
                `❌ Token Validation Errors:`,
                gqlError.extensions.validation.token
              );
            }
          }
        });
      }

      if (error.networkError) {
        console.error(
          '🔍 Network Error:',
          JSON.stringify(error.networkError, null, 2)
        );
        if (error.networkError.result) {
          console.error(
            '🔍 Network Error Result:',
            JSON.stringify(error.networkError.result, null, 2)
          );
        }
        if (error.networkError.response) {
          console.error(
            '🔍 Network Error Response:',
            JSON.stringify(error.networkError.response, null, 2)
          );
        }
      }

      // Handle specific Google Sign-In errors
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        // User cancelled the sign-in flow
        console.log('ℹ️ User cancelled Google Sign-In');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        // Sign-in is in progress already
        console.log('ℹ️ Google Sign-In already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        Alert.alert(
          t('common.error'),
          'Google Play Services not available on this device'
        );
      } else {
        // Backend/GraphQL errors - show detailed info
        let errorMessage = t('login.googleError.message') + error.message;

        // If it's a GraphQL error, try to get more specific message
        if (error.graphQLErrors && error.graphQLErrors.length > 0) {
          const firstError = error.graphQLErrors[0];
          if (firstError.message) {
            errorMessage = `Backend Error: ${firstError.message}`;
          }
        }

        Alert.alert(t('common.error'), errorMessage);
      }
    } finally {
      setIsGoogleSignInLoading(false);
    }
  };

  return (
    <Screen>
      <View style={styles.content}>
        <View style={styles.animationContainer}>
          {/* if old device or dev mode render image otw render LottieView animation */}
          {deviceService.isOldDevice() || __DEV__ ? (
            <Image
              source={require('@/assets/images/onboarding.png')}
              style={styles.image}
              resizeMode='contain'
            />
          ) : (
            <LottieView
              source={require('@/assets/lottie/onboarding.json')}
              style={styles.animation}
              autoPlay
              loop
            />
          )}
        </View>

        <View style={styles.textContainer}>
          <Text variant='h1' align='center'>
            {t('onboarding.title')}
          </Text>

          <View style={styles.textContainerSubtitle}>
            <Text variant='caption' align='center'>
              {t('onboarding.subtitle')}
            </Text>
          </View>
        </View>
        <View style={styles.buttonContainer}>
          {__DEV__ && (
            <Button
              variant='primary'
              title='🚀 Dev Login (Skip Apple)'
              onPress={simulateDevLogin}
              style={styles.devButton}
            />
          )}
          {Platform.OS === 'ios' && (
            <Button
              variant='secondary'
              title={t('login.continueApple')}
              leftIcon={
                <IconSymbol
                  name='apple.logo'
                  size={24}
                  color={colors.base[950]}
                />
              }
              onPress={handleAppleSignIn}
              disabled={isAppleSignInLoading}
              loading={isAppleSignInLoading}
            />
          )}
          {Platform.OS === 'android' && (
            <Button
              variant='secondary'
              title={t('login.continueGoogle')}
              leftIcon={
                <View
                  style={{
                    width: 24,
                    height: 24,
                    borderRadius: 12,
                    backgroundColor: colors.base[950],
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Text
                    style={{
                      color: colors.base[100],
                      fontSize: 14,
                      fontWeight: 'bold',
                    }}
                  >
                    G
                  </Text>
                </View>
              }
              onPress={handleGoogleSignIn}
              disabled={isGoogleSignInLoading}
              loading={isGoogleSignInLoading}
            />
          )}
          <View style={styles.invitationTextContainer}>
            <Text
              variant='caption1'
              align='center'
              color={colors.primary[950]}
              weight='bold'
            >
              {t('onboarding.invitationText')}
            </Text>
          </View>
        </View>
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    padding: verticalScale(20),
    alignItems: 'center',
  },
  animationContainer: {
    position: 'relative',
    top: verticalScale(-10),
    marginLeft: moderateScale(5),
    width: SCREEN_WIDTH * 1.1,
    height: SCREEN_WIDTH * 1.1,
  },
  animation: {
    width: '120%',
    height: '120%',
    alignSelf: 'center',
  },
  image: {
    width: '100%',
    height: '110%',
    alignSelf: 'center',
    marginTop: verticalScale(30),
  },
  textContainer: {
    width: '100%',
    marginTop: verticalScale(56),
  },
  textContainerSubtitle: {
    marginTop: verticalScale(8),
    width: '85%',
    alignSelf: 'center',
  },
  invitationTextContainer: {
    marginVertical: verticalScale(12),
  },
  buttonContainer: {
    position: 'absolute',
    bottom: verticalScale(0),
    width: '100%',
  },
  devButton: {
    marginBottom: verticalScale(12),
    backgroundColor: colors.secondary[600],
  },
});
