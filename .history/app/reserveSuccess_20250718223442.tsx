import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Button } from '@/components/ui/Button';
import { useTranslation } from 'react-i18next';
import { Image } from 'expo-image';
import { formatTimeRange } from '@/utils/time';
import { format } from 'date-fns';
import { PlatformIcon } from '@/components/ui/PlatformIcon';
import { MyDealStatusEnum } from '@/graphql/generated/graphql';

type ReserveSuccessParams = {
  partnerName: string;
  placeName?: string;
  placeId?: string;
  date?: string;
  timeFrom?: string;
  timeTo?: string;
  dealStatus?: string;
};

export default function ReserveSuccessScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams<ReserveSuccessParams>();

  const handleGoToDeals = () => {
    router.replace({
      pathname: '/deals',
      params: {
        activeFilter: params.dealStatus || MyDealStatusEnum.Upcoming,
      },
    });
  };

  const handleNavigateToPlace = () => {
    if (params.placeId) {
      router.push(`/places/${params.placeId}`);
    }
  };

  const renderInfoItem = (
    label: string,
    value: string,
    icon: string,
    isClickable = false,
    onPress?: () => void
  ) => {
    const itemContent = (
      <>
        <View style={styles.iconContainer}>
          <PlatformIcon name={icon} size={20} color={colors.base[100]} />
        </View>
        <View style={styles.textContainer}>
          <Text variant='caption1' style={styles.infoLabel} numberOfLines={1}>
            {label}
          </Text>
          <Text variant='caption1' style={[styles.infoValue]} numberOfLines={1}>
            {value}
          </Text>
        </View>
      </>
    );

    if (isClickable && onPress) {
      return (
        <TouchableOpacity style={styles.infoItem} onPress={onPress}>
          {itemContent}
        </TouchableOpacity>
      );
    }

    return <View style={styles.infoItem}>{itemContent}</View>;
  };

  return (
    <Screen>
      <View style={styles.container}>
        <Image
          source={require('@/assets/images/booking-successful.png')}
          style={styles.image}
          contentFit='contain'
        />

        <Text variant='h1' style={styles.title}>
          {t('deals.reserveSuccess.title')}
        </Text>

        <Text variant='caption' style={styles.subtitle} align='center'>
          {t('deals.reserveSuccess.subtitle')}
        </Text>

        <View style={styles.infoContainer}>
          <View style={styles.infoRow}>
            {renderInfoItem(
              t('deals.reserveSuccess.place'),
              params.partnerName || '',
              'storefront.fill'
            )}
            {renderInfoItem(
              t('deals.reserveSuccess.location'),
              params.placeName || '',
              'mappin.and.ellipse',
              !!params.placeId,
              handleNavigateToPlace
            )}
          </View>
          <View style={styles.infoRow}>
            {renderInfoItem(
              t('deals.reserveSuccess.date'),
              params.date ? format(new Date(params.date), 'MMM dd, yyyy') : '',
              'calendar.circle.fill'
            )}
            {renderInfoItem(
              t('deals.reserveSuccess.time'),
              formatTimeRange(params.timeFrom || '', params.timeTo || ''),
              'clock.fill'
            )}
          </View>
        </View>

        <Button
          title={t('deals.reserveSuccess.goToDeals')}
          onPress={handleGoToDeals}
          style={styles.button}
          variant='primary'
        />
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingTop: verticalScale(40),
  },
  image: {
    width: moderateScale(284.55),
    height: verticalScale(298),
    marginBottom: verticalScale(32),
  },
  title: {
    marginBottom: verticalScale(10),
  },
  subtitle: {
    marginBottom: verticalScale(22),
    paddingHorizontal: moderateScale(16),
  },
  infoContainer: {
    width: '100%',
    gap: moderateScale(16),
    marginBottom: verticalScale(32),
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: moderateScale(8),
  },
  infoItem: {
    flex: 1,
    backgroundColor: colors.softGrey[400],
    borderRadius: moderateScale(16),
    padding: moderateScale(12),
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
  },
  iconContainer: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderRadius: moderateScale(16),
    backgroundColor: colors.primary[950],
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
  },
  infoLabel: {
    color: colors.darkGrey[500],
    marginBottom: verticalScale(4),
  },
  infoValue: {
    color: colors.base[950],
    fontWeight: '600',
  },
  button: {
    marginTop: verticalScale(16),
    width: '100%',
  },
});
