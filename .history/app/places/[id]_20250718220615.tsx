import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Pressable,
  TouchableOpacity,
} from 'react-native';
import { Image } from 'expo-image';
import { Screen } from '../../components/ui/Screen';
import { Text } from '../../components/ui/Text';
import { colors } from '../../constants/Colors';
import { moderateScale, verticalScale } from '../../utils/scaling';
import { useLocalSearchParams, useRouter, useNavigation } from 'expo-router';
import { PlatformIcon } from '../../components/ui/PlatformIcon';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  interpolate,
} from 'react-native-reanimated';
import { Tag } from '../../components/ui/Tag';
import { PlaceCarousel } from '../../components/PlaceCarousel';
import {
  usePlaceQuery,
  MyDeal as GraphQLMyDeal,
  Deal as GraphQLDeal,
  useFollowPlaceMutation,
  useUserFollowedPlacesQuery,
  PartnerPlace,
  useMeQuery,
} from '../../graphql/generated/graphql';
import { Deal } from '../../components/ui/Deal';
import { Tooltip } from '../../components/ui/Tooltip';
import { useTranslation } from 'react-i18next';
import { BackButton } from '../../components/ui/BackButton';
import { RightHeaderButton } from '@/components/ui/RightHeaderButton';
import { OpeningHoursContent } from '@/components/ui/OpeningHoursContent';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { MediaModal, MediaModalRef, MediaItem } from '@/components/MediaModal';
import { showUnfollowConfirmation } from '@/utils/confirmations';
import { ReelThumbnail } from '../../components/ReelThumbnail';
import { FollowButton } from '@/components/ui/FollowButton';
import { openLocationInGoogleMaps } from '@/utils/location';
import { FEATURED_MEAL_TIMES } from '@/constants/MealTimes';
import { sharePlace } from '@/services/branch';
import { PlacesList } from '@/components/PlacesList';
import { SaveIcon } from '@/components/icons/SaveIcon';
import * as Haptics from 'expo-haptics';
import {
  renderOpeningHoursTitle,
  renderOpeningHoursSubtitle,
  formatOpeningHours,
} from '@/utils/openingHours';
import { useLocationServices } from '@/services/locationServices';
import { UploadReelModal } from '@/components/ui/UploadReelModal';
import { Modalize } from 'react-native-modalize';

export default function PlaceScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const navigation = useNavigation();
  const { id } = useLocalSearchParams();
  const contentOpacity = useSharedValue(0);
  const contentTranslateY = useSharedValue(50);
  const [showMenu, setShowMenu] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const placeImagesModalRef = useRef<MediaModalRef>(null);
  const menuItemsModalRef = useRef<MediaModalRef>(null);
  const uploadReelModalRef = useRef<Modalize>(null);

  // Add location services
  const locationServices = useLocationServices();
  // Add rotation animation value
  const menuChevronRotation = useSharedValue(0);

  // Create animated style for chevron rotation
  const chevronAnimatedStyle = useAnimatedStyle(() => {
    const rotation = interpolate(menuChevronRotation.value, [0, 1], [0, 180]);

    return {
      transform: [{ rotate: `${rotation}deg` }],
    };
  });

  // Use the generated query hook
  const { data, loading, error } = usePlaceQuery({
    variables: {
      id: id as string,
    },
    fetchPolicy: 'cache-and-network',
  });

  // Get current user data
  const { data: userData } = useMeQuery();

  // Get user's followed places
  const { data: followedPlacesData } = useUserFollowedPlacesQuery();

  // Check if this place is in the user's followed places
  useEffect(() => {
    if (
      followedPlacesData &&
      followedPlacesData.me &&
      followedPlacesData.me.following_places
    ) {
      const isFollowed = followedPlacesData.me.following_places.data.some(
        (place) => place.id === id
      );
      setIsFollowing(isFollowed);
    }
  }, [followedPlacesData, id]);

  // Follow place mutation
  const [followPlace, { loading: followLoading }] = useFollowPlaceMutation({
    onCompleted: (data) => {
      if (data.followPlace.status) {
        setIsFollowing((prev) => !prev);
      }
    },
    refetchQueries: ['UserFollowedPlaces'],
    onError: (error) => {
      console.error('Error following/unfollowing place:', error);
    },
  });

  const handleBack = () => {
    if (navigation.canGoBack()) {
      router.back();
    } else {
      // No previous screen, go to home
      router.replace('/(tabs)');
    }
  };

  React.useEffect(() => {
    if (data) {
      contentOpacity.value = withTiming(1, {
        duration: 500,
        easing: Easing.out(Easing.quad),
      });
      contentTranslateY.value = withTiming(0, {
        duration: 500,
        easing: Easing.out(Easing.quad),
      });
    }
  }, [data]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: contentOpacity.value,
      transform: [{ translateY: contentTranslateY.value }],
    };
  });

  // Calculate distance using location service
  const distance = React.useMemo(() => {
    if (
      data?.partnerPlace &&
      data.partnerPlace.location?.lat &&
      data.partnerPlace.location?.lng
    ) {
      return locationServices.calculateDistanceToPlace(
        data.partnerPlace.location.lat,
        data.partnerPlace.location.lng
      );
    }
    return null;
  }, [data?.partnerPlace?.location, locationServices.calculateDistanceToPlace]);

  if (loading) {
    return <LoadingScreen onBack={handleBack} message={t('common.loading')} />;
  }

  if (error || !data?.partnerPlace) {
    return <ErrorScreen message={t('common.notFound')} onBack={handleBack} />;
  }

  const place = data.partnerPlace;
  const places = place.partner.partner_places.data;

  const handleOpenMenu = () => {
    // If menu_url exists, open it directly in browser
    if (place.menu_url) {
      //navigate to webview screen
      router.push({
        pathname: '/webview',
        params: { url: place.menu_url },
      });
    }
    // If there's just one menu item, open it directly in the modal
    else if (place.menu && place.menu.length === 1) {
      menuItemsModalRef.current?.open(0);
    }
    // Otherwise toggle menu display for multiple items
    else {
      // Animate the chevron rotation
      menuChevronRotation.value = withTiming(showMenu ? 0 : 1, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      });
      setShowMenu(!showMenu);
    }
  };

  const handleOpenLocation = () => {
    if (place.location) {
      const { lat, lng } = place.location;
      if (lat && lng) {
        openLocationInGoogleMaps(lat, lng);
      }
    }
  };

  // Convert place images to MediaItem format for modal
  const placeImagesAsMediaItems: MediaItem[] = (place.images || [])
    .filter((img): img is NonNullable<typeof img> => !!img && !!img.full_url)
    .map((img) => ({
      full_url: img.full_url || '',
    }));

  // Convert menu items to MediaItem format
  const menuItemsAsMediaItems: MediaItem[] = (place.menu || [])
    .filter(
      (item): item is NonNullable<typeof item> => !!item && !!item.full_url
    )
    .map((item) => ({
      full_url: item.full_url || '',
    }));

  const handleOpenPlaceImage = (index: number) => {
    placeImagesModalRef.current?.open(index);
  };

  const handleOpenMenuItem = (_item: any, index: number) => {
    menuItemsModalRef.current?.open(index);
  };

  const renderDeal = (deal: GraphQLDeal) => {
    const myDealObject = deal.myDeals.data[0];

    // Prepare common props without key
    const dealProps = {
      placeId: place.id,
      isInDealsScreen: false,
    };

    // Add either myDeal or deal prop based on condition
    if (myDealObject) {
      const myDeal = {
        ...myDealObject,
        deal,
      } as GraphQLMyDeal;

      return <Deal key={deal.id} {...dealProps} myDeal={myDeal} />;
    } else {
      return <Deal key={deal.id} {...dealProps} deal={deal} />;
    }
  };

  const handleFollowPlace = () => {
    if (followLoading || !place) return;

    const placeName = place.partner.name || 'this place';

    if (isFollowing) {
      showUnfollowConfirmation(t, placeName, () => {
        followPlace({ variables: { input: { id: id as string } } });
      });
    } else {
      followPlace({ variables: { input: { id: id as string } } });
    }
  };

  return (
    <Screen withSafeArea={false}>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <BackButton onPress={handleBack} absolute transparent />

        <RightHeaderButton
          absolute
          onPress={() => {
            sharePlace(place as PartnerPlace);
          }}
          variant='dark'
        >
          <PlatformIcon
            name='square.and.arrow.up'
            size={22}
            color={colors.base[100]}
          />
        </RightHeaderButton>

        {/* Original Place Images Carousel with click handler */}
        <Pressable onPress={() => handleOpenPlaceImage(0)}>
          <PlaceCarousel
            images={
              place.images
                ?.map((img) => img?.full_url)
                .filter((url): url is string => !!url) || []
            }
          />
          <View style={styles.placeImageZoomIndicator}>
            <PlatformIcon
              name='plus.magnifyingglass'
              size={14}
              color={colors.base[100]}
            />
            <Text variant='tiny' color={colors.base[100]}>
              {t('media.tapToZoom')}
            </Text>
          </View>
        </Pressable>

        {/* Media Modal for Place Images */}
        <MediaModal ref={placeImagesModalRef} items={placeImagesAsMediaItems} />

        {/* Place Info */}
        <Animated.View style={[styles.placeInfo, animatedStyle]}>
          <Text
            align='center'
            variant='h2'
            style={styles.partnerName}
            numberOfLines={2}
          >
            {place.partner.name}
          </Text>

          <Tooltip
            content={() => (
              <PlacesList places={places} currentPlaceId={place.id} />
            )}
          >
            <View style={styles.placeContainer}>
              <PlatformIcon
                name='mappin.and.ellipse'
                size={12}
                color={colors.base[950]}
              />
              <Text align='center' variant='body' numberOfLines={1}>
                {place.name}
              </Text>
              <View style={styles.chevronContainer}>
                <PlatformIcon
                  name='chevron.down'
                  size={16}
                  color={colors.primary[950]}
                />
              </View>
            </View>
          </Tooltip>

          <View style={styles.followButtonContainer}>
            {/* Follow place button */}
            <FollowButton
              isFollowing={isFollowing}
              loading={followLoading}
              onPress={handleFollowPlace}
              style={styles.followButtonStyle}
            />
            {/* Add to collection button   */}
            <TouchableOpacity
              style={[styles.button, styles.saveButton]}
              onPress={() => {
                router.push({
                  pathname: '/(modals)/collections',
                  params: {
                    id: place?.id,
                    avatar: place?.avatar?.full_url,
                  },
                });
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <SaveIcon
                color={colors.primary[950]}
                outline={!place.is_place_in_collection}
              />
            </TouchableOpacity>
            {/* Upload reel button only for creator  */}
            {userData?.me?.creator && (
              <TouchableOpacity
                style={[styles.button, styles.uploadReelButton]}
                onPress={() => {
                  uploadReelModalRef.current?.open();
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <PlatformIcon
                  name='video.fill.badge.plus'
                  size={27}
                  color={colors.secondary[500]}
                />
              </TouchableOpacity>
            )}
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tagsScrollContainer}
          >
            {place.rates?.google && (
              <Tag
                icon='star.fill'
                label={t('place.tags.google', {
                  rating: place.rates.google,
                })}
                customStyle={styles.tag}
                iconColor={colors.info.yellow}
              />
            )}

            {/* Distance */}
            {distance && (
              <Tag
                icon='mappin.and.ellipse'
                label={t('place.tags.distance', {
                  distance: distance.toFixed(1),
                })}
                customStyle={styles.tag}
              />
            )}

            {/* Display tags in specific order */}
            {/* Cuisine types */}
            {place.cuisine_types?.map((tag, index) => (
              <Tag
                key={`cuisine-${index}`}
                label={tag?.title}
                customStyle={styles.tag}
              />
            ))}

            {/* Cravings  */}
            {place.cravings?.map((tag, index) => (
              <Tag
                key={`cravings-${index}`}
                label={tag?.title}
                customStyle={styles.tag}
              />
            ))}

            {/* Specialties */}
            {place.specialities?.map((tag, index) => (
              <Tag
                key={`speciality-${index}`}
                label={tag?.title}
                customStyle={styles.tag}
              />
            ))}

            {/* dietary  */}
            {place.dietary?.map((tag, index) => (
              <Tag
                key={`dietary-${index}`}
                label={tag?.title}
                customStyle={styles.tag}
              />
            ))}

            {/* Meal time - only specific ones ignore case*/}
            {place.meal_times
              ?.filter(
                (tag) => tag && FEATURED_MEAL_TIMES.has(tag.title.toLowerCase())
              )
              .map((tag, index) => (
                <Tag
                  key={`mealtime-${index}`}
                  label={tag?.title}
                  customStyle={styles.tag}
                />
              ))}

            {/* Ambiance */}
            {place.ambiance?.map((tag, index) => (
              <Tag
                key={`ambiance-${index}`}
                label={tag?.title}
                customStyle={styles.tag}
              />
            ))}

            {/* Parking */}
            {place.parking?.map((tag, index) => (
              <Tag
                key={`parking-${index}`}
                label={tag?.title}
                customStyle={styles.tag}
              />
            ))}
          </ScrollView>

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <Tooltip
              buttonStyle={styles.actionButton}
              content={() => (
                <OpeningHoursContent
                  openingHours={formatOpeningHours(place.opening_hours)}
                />
              )}
            >
              <View style={styles.actionContent}>
                <View style={styles.actionTexts}>
                  <Text
                    weight='bold'
                    numberOfLines={1}
                    variant='caption'
                    color={colors.base[950]}
                  >
                    {renderOpeningHoursTitle(place.opening_hours, {
                      closed: t('place.quickActions.closed'),
                      closedToday: t('place.quickActions.closedToday'),
                    })}
                  </Text>
                  <Text variant='caption1' style={styles.actionSubtitle}>
                    {renderOpeningHoursSubtitle(place.opening_hours, {
                      openNow: t('place.quickActions.openNow'),
                      closedNow: t('place.quickActions.closedNow'),
                      opensNext: ({
                        day,
                        time,
                      }: {
                        day: string;
                        time: string;
                      }) => t('place.quickActions.opensNext', { day, time }),
                      closed: t('place.quickActions.closed'),
                    })}
                  </Text>
                </View>
              </View>
            </Tooltip>

            {/* Menu Action Button */}
            {(place.menu_url || (place.menu && place.menu.length > 0)) && (
              <Pressable style={styles.actionButton} onPress={handleOpenMenu}>
                <View style={styles.actionContent}>
                  <View style={styles.actionTexts}>
                    <Text
                      weight='bold'
                      numberOfLines={1}
                      variant='caption'
                      color={colors.base[950]}
                    >
                      {t('place.quickActions.menu.title')}
                    </Text>
                    <Text variant='caption1' style={styles.actionSubtitle}>
                      {place.menu_url
                        ? t('place.quickActions.menu.viewOnline')
                        : t('place.quickActions.menu.subtitle')}
                    </Text>
                  </View>
                  <View style={styles.iconContainer}>
                    {place.menu_url ? (
                      <PlatformIcon
                        name='link'
                        size={20}
                        color={colors.primary[950]}
                      />
                    ) : place.menu && place.menu.length === 1 ? (
                      <PlatformIcon
                        name='plus.magnifyingglass'
                        size={20}
                        color={colors.primary[950]}
                      />
                    ) : (
                      <Animated.View style={chevronAnimatedStyle}>
                        <PlatformIcon
                          name='chevron.down'
                          size={20}
                          color={colors.primary[950]}
                        />
                      </Animated.View>
                    )}
                  </View>
                </View>
              </Pressable>
            )}

            <Pressable style={styles.actionButton} onPress={handleOpenLocation}>
              <View style={styles.actionContent}>
                <View style={styles.actionTexts}>
                  <Text
                    weight='bold'
                    numberOfLines={1}
                    variant='caption'
                    color={colors.base[950]}
                  >
                    {t('place.quickActions.map.title', { distance: '2.5' })}
                  </Text>
                  <Text variant='caption1' style={styles.actionSubtitle}>
                    {t('place.quickActions.map.subtitle')}
                  </Text>
                </View>

                <View style={styles.iconContainer}>
                  <PlatformIcon
                    name='location'
                    size={20}
                    color={colors.primary[950]}
                  />
                </View>
              </View>
            </Pressable>
          </View>

          {/* Menu Section - Only show this section for multiple regular menu items */}
          {showMenu &&
            !place.menu_url &&
            place.menu &&
            place.menu.length > 1 && (
              <View style={styles.section}>
                <Text variant='h2' style={styles.sectionTitle}>
                  {t('place.quickActions.menu.title')}
                </Text>

                {/* Original Menu Items Display */}
                <View style={styles.menuGrid}>
                  {menuItemsAsMediaItems.map((item, index) => (
                    <Pressable
                      key={index}
                      style={styles.menuItem}
                      onPress={() => handleOpenMenuItem(item, index)}
                    >
                      {item.full_url.toLowerCase().endsWith('.pdf') ? (
                        <View style={styles.pdfContainer}>
                          <PlatformIcon
                            name='menucard'
                            size={40}
                            color={colors.base[100]}
                          />
                        </View>
                      ) : (
                        <Image
                          source={{ uri: item.full_url }}
                          style={styles.menuImage}
                          contentFit='cover'
                        />
                      )}
                      <View style={styles.viewIndicator}>
                        <PlatformIcon
                          name='plus.magnifyingglass'
                          size={12}
                          color={colors.base[100]}
                        />
                        <Text variant='tiny' color={colors.base[100]}>
                          {t('media.tapToView')}
                        </Text>
                      </View>
                    </Pressable>
                  ))}
                </View>
              </View>
            )}

          {/* Media Modal for Menu Items - Placed outside the conditional rendering */}
          {place.menu && place.menu.length > 0 && (
            <MediaModal ref={menuItemsModalRef} items={menuItemsAsMediaItems} />
          )}

          {/* Deals Section */}
          {place.deals && place.deals.data.length > 0 && (
            <View style={styles.section}>
              <Text variant='h2' style={styles.sectionTitle}>
                {t('place.sections.bestDeals')}
              </Text>
              {place.deals.data.map((deal) => renderDeal(deal as GraphQLDeal))}
            </View>
          )}

          {/* Reels Section */}
          {place.reels && place.reels.data && place.reels.data.length > 0 && (
            <View style={styles.section}>
              <Text variant='h2' style={styles.sectionTitle}>
                {t('place.sections.reels')}
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.reelsContainer}
              >
                {place.reels.data.map((reel, index) => (
                  <ReelThumbnail
                    key={reel.id || index}
                    reel={reel}
                    placeId={place.id}
                  />
                ))}
              </ScrollView>
            </View>
          )}
        </Animated.View>
      </ScrollView>

      {/* Upload Reel Modal */}
      <UploadReelModal ref={uploadReelModalRef} partnerLocationId={place?.id} />
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.base[100],
  },

  contentContainer: {
    paddingBottom: moderateScale(40),
  },
  placeInfo: {
    paddingHorizontal: moderateScale(16),
    paddingTop: moderateScale(16),
  },
  partnerName: {
    marginBottom: verticalScale(8),
  },
  placeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
    justifyContent: 'center',
    marginBottom: verticalScale(16),
    alignSelf: 'center',
  },
  chevronContainer: {
    alignSelf: 'center',
  },
  tagsScrollContainer: {
    paddingRight: moderateScale(16),
    alignItems: 'center',
  },
  tag: {
    marginRight: moderateScale(8),
  },
  section: {
    marginTop: verticalScale(24),
  },
  sectionTitle: {
    marginBottom: verticalScale(16),
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: verticalScale(16),
    gap: moderateScale(8),
  },
  actionButton: {
    flex: 1,
    height: verticalScale(57),
    backgroundColor: colors.softGrey[400],
    borderRadius: moderateScale(16),
    justifyContent: 'center',
    paddingHorizontal: moderateScale(12),
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  actionTexts: {
    flex: 1,
  },
  actionSubtitle: {
    color: colors.darkGrey[500],
    marginTop: verticalScale(2),
  },
  iconContainer: {
    width: moderateScale(32),
    height: moderateScale(32),
    backgroundColor: colors.primary[100],
    borderRadius: moderateScale(16),
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -moderateScale(4),
  },
  menuItem: {
    width: '33.33%',
    aspectRatio: 1,
    padding: moderateScale(4),
  },
  menuImage: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(8),
  },
  pdfContainer: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(8),
    backgroundColor: colors.primary[900],
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewIndicator: {
    position: 'absolute',
    bottom: moderateScale(8),
    right: moderateScale(8),
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: moderateScale(12),
    padding: moderateScale(5),
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
  },
  placeImageZoomIndicator: {
    position: 'absolute',
    bottom: verticalScale(20),
    right: moderateScale(20),
    flexDirection: 'row',
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: moderateScale(12),
    padding: moderateScale(5),
    alignItems: 'center',
    gap: moderateScale(4),
  },
  followButtonStyle: {
    marginBottom: verticalScale(16),
  },
  reelsContainer: {
    gap: moderateScale(12),
    paddingRight: moderateScale(16),
  },
  followButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: moderateScale(12),
  },
  button: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButton: {
    borderColor: colors.primary[950],
  },
  uploadReelButton: {
    borderColor: colors.secondary[500],
  },
});
