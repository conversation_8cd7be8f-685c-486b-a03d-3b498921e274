import { HapticTab } from '@/components/HapticTab';
import { colors, ThemeColors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { moderateScale, SCREEN_WIDTH } from '@/utils/scaling';
import { Tabs } from 'expo-router';
import React from 'react';
import { DiscoverFill } from '@/components/icons/tabs/DiscoverFill';
import { HomeFill } from '@/components/icons/tabs/HomeFill';
import { DiscoverOutline } from '@/components/icons/tabs/DiscoverOutline';
import { HomeOutline } from '@/components/icons/tabs/HomeOutline';
import { DealsFill } from '@/components/icons/tabs/DealsFill';
import { DealsOutline } from '@/components/icons/tabs/DealsOutline';
import { CollectionsFill } from '@/components/icons/tabs/CollectionsFill';
import { CollectionsOutline } from '@/components/icons/tabs/CollectionsOutline';
import { ProfileOutline } from '@/components/icons/tabs/ProfileOutline';
import { ProfileFill } from '@/components/icons/tabs/ProfileFill';

export default function TabLayout() {
  const colorScheme = useColorScheme();

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarActiveTintColor: colors.base[100],
        tabBarInactiveTintColor: colors.darkGrey[300],
        tabBarStyle: {
          position: 'absolute',
          alignSelf: 'center',
          justifyContent: 'center',
          alignItems: 'center',
          width: SCREEN_WIDTH * 0.9,
          bottom: moderateScale(20),
          transform: [{ translateX: '5.5%' }],
          height: moderateScale(68),
          borderRadius: moderateScale(50),
          backgroundColor: ThemeColors[colorScheme].background,
          borderTopWidth: 0,
          shadowColor: colors.base[950],
          shadowOffset: { width: 0, height: moderateScale(4) },
          shadowOpacity: 0.1,
          shadowRadius: moderateScale(10),
          elevation: 10,
          overflow: 'hidden',
        },
        tabBarItemStyle: {
          bottom: moderateScale(2),
        },
        tabBarShowLabel: false,
      }}
    >
      <Tabs.Screen
        name='index'
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) => {
            const Icon = focused ? HomeFill : HomeOutline;
            return <Icon color={color} />;
          },
        }}
      />
      <Tabs.Screen
        name='discover'
        options={{
          title: 'Discover',
          tabBarIcon: ({ color, focused }) => {
            const Icon = focused ? DiscoverFill : DiscoverOutline;
            return <Icon color={color} />;
          },
        }}
      />
      <Tabs.Screen
        name='deals'
        options={{
          title: 'Deals',
          tabBarIcon: ({ color, focused }) => {
            const Icon = focused ? DealsFill : DealsOutline;
            return <Icon color={color} />;
          },
        }}
      />
      <Tabs.Screen
        name='collections'
        options={{
          title: 'Collections',
          tabBarIcon: ({ color, focused }) => {
            const Icon = focused ? CollectionsFill : CollectionsOutline;
            return <Icon color={color} />;
          },
        }}
      />
      <Tabs.Screen
        name='profile'
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, focused }) => {
            const Icon = focused ? ProfileFill : ProfileOutline;
            return <Icon color={color} />;
          },
          headerShown: false,
          // tabBarBadge: '2',
          tabBarBadgeStyle: {
            backgroundColor: colors.info.red,
            minWidth: moderateScale(12),
            height: moderateScale(12),
            borderRadius: moderateScale(6),
            fontSize: moderateScale(8),
            color: colors.base[100],
            paddingHorizontal: 0,
            lineHeight: moderateScale(12),
          },
        }}
      />
    </Tabs>
  );
}
