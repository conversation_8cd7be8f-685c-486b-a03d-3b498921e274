import { HapticTab } from '@/components/HapticTab';
import { colors, ThemeColors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { moderateScale, SCREEN_WIDTH } from '@/utils/scaling';
import { Tabs } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { DiscoverFill } from '@/components/icons/tabs/DiscoverFill';
import { HomeFill } from '@/components/icons/tabs/HomeFill';
import { DiscoverOutline } from '@/components/icons/tabs/DiscoverOutline';
import { HomeOutline } from '@/components/icons/tabs/HomeOutline';
import { DealsFill } from '@/components/icons/tabs/DealsFill';
import { DealsOutline } from '@/components/icons/tabs/DealsOutline';
import { CollectionsFill } from '@/components/icons/tabs/CollectionsFill';
import { CollectionsOutline } from '@/components/icons/tabs/CollectionsOutline';
import { ProfileOutline } from '@/components/icons/tabs/ProfileOutline';
import { ProfileFill } from '@/components/icons/tabs/ProfileFill';
import { OnboardingModal } from '@/components/ui/OnboardingModal';
import { useAuthStore } from '@/store/auth';
import { ONBOARDING_STEPS } from '@/constants/Onboarding';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
} from 'react-native-reanimated';
import { Platform } from 'react-native';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const onboardingModalRef = useRef<any>(null);
  const { hasSeenOnboarding, setHasSeenOnboarding } = useAuthStore();
  const [onboardingStep, setOnboardingStep] = useState(-1);
  const [isOnboardingActive, setIsOnboardingActive] = useState(false);

  // Single animated values for the active tab
  const activeTabOpacity = useSharedValue(1);
  const activeTabScale = useSharedValue(1);

  // Single animated style for the active tab
  const activeTabAnimatedStyle = useAnimatedStyle(() => ({
    opacity: activeTabOpacity.value,
    transform: [{ scale: activeTabScale.value }],
  }));

  useEffect(() => {
    // Show onboarding modal for first-time users
    if (!hasSeenOnboarding) {
      // if (true) {
      setIsOnboardingActive(true);
      onboardingModalRef.current?.open();
    }
  }, [hasSeenOnboarding]);

  // Animate the active tab when onboarding step changes
  useEffect(() => {
    if (!isOnboardingActive) {
      // Reset to normal state when not in onboarding
      activeTabOpacity.value = withTiming(1);
      activeTabScale.value = withTiming(1);
      return;
    }

    // Animate the active tab for the current step
    const animateActiveTab = () => {
      // Light sequence: subtle scale animation
      activeTabScale.value = withSequence(
        withTiming(0.95, { duration: 150 }),
        withTiming(1.05, { duration: 200 }),
        withTiming(1, { duration: 150 })
      );
    };

    // Trigger animation when step changes
    if (onboardingStep >= 0) {
      animateActiveTab();
    }
  }, [onboardingStep, isOnboardingActive]);

  const handleOnboardingComplete = () => {
    setHasSeenOnboarding();
    setIsOnboardingActive(false);
  };

  const handleOnboardingStepChange = (step: number) => {
    setOnboardingStep(step);
  };

  // Helper function to determine if a tab should be visible during onboarding
  const isTabVisible = (tabName: string) => {
    if (!isOnboardingActive) return true;

    const visibleSteps = ONBOARDING_STEPS.slice(0, onboardingStep + 1);
    const currentVisibleTabs = visibleSteps.map((step) => step.tabName);
    return currentVisibleTabs.includes(tabName);
  };

  // Helper function to determine if a tab should be active during onboarding
  const isTabActive = (tabName: string) => {
    if (!isOnboardingActive) return false;

    const currentActiveTab = ONBOARDING_STEPS[onboardingStep]?.tabName;
    return tabName === currentActiveTab;
  };

  // Custom tab button component that handles active state during onboarding
  const CustomTabButton = ({
    tabName,
    ...props
  }: { tabName: string } & any) => {
    if (!isTabVisible(tabName)) {
      return null;
    }

    // During onboarding, completely override the active state
    if (isOnboardingActive) {
      const shouldBeActive = isTabActive(tabName);

      const modifiedProps = {
        ...props,
        accessibilityState: {
          ...props.accessibilityState,
          selected: shouldBeActive,
        },
      };

      // Apply animation only to the active tab
      if (shouldBeActive) {
        return (
          <Animated.View style={activeTabAnimatedStyle}>
            <HapticTab {...modifiedProps} />
          </Animated.View>
        );
      }

      // Static view for non-active tabs
      return <HapticTab {...modifiedProps} />;
    }

    return <HapticTab {...props} />;
  };
  var tabsBottomPosition = 5;
  if (Platform.OS === 'ios') {
    var tabsBottomPosition = 20;
  }

  return (
    <>
      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarActiveTintColor: colors.base[100],
          tabBarInactiveTintColor: colors.darkGrey[300],
          tabBarStyle: {
            position: 'absolute',
            alignSelf: 'center',
            justifyContent: 'center',
            alignItems: 'center',
            width: SCREEN_WIDTH * 0.9,
            bottom: moderateScale(tabsBottomPosition),
            transform: [{ translateX: '5.5%' }],
            height: moderateScale(68),
            borderRadius: moderateScale(50),
            backgroundColor: ThemeColors[colorScheme].background,
            borderTopWidth: 0,
            shadowColor: colors.base[950],
            shadowOffset: { width: 0, height: moderateScale(4) },
            shadowOpacity: 0.1,
            shadowRadius: moderateScale(10),
            elevation: 10,
            overflow: 'hidden',
          },
          tabBarItemStyle: {
            bottom: moderateScale(2),
          },
          tabBarShowLabel: false,
        }}
      >
        <Tabs.Screen
          name='index'
          options={{
            title: 'Home',
            tabBarIcon: ({ color, focused }) => {
              // During onboarding, use onboarding active state, otherwise use focused state
              const isActive = isOnboardingActive
                ? isTabActive('index')
                : focused;
              const Icon = isActive ? HomeFill : HomeOutline;
              return <Icon color={isActive ? colors.base[100] : color} />;
            },
            tabBarButton: (props) => (
              <CustomTabButton tabName='index' {...props} />
            ),
          }}
        />
        <Tabs.Screen
          name='discover'
          options={{
            title: 'Discover',
            tabBarIcon: ({ color, focused }) => {
              // During onboarding, use onboarding active state, otherwise use focused state
              const isActive = isOnboardingActive
                ? isTabActive('discover')
                : focused;
              const Icon = isActive ? DiscoverFill : DiscoverOutline;
              return <Icon color={isActive ? colors.base[100] : color} />;
            },
            tabBarButton: (props) => (
              <CustomTabButton tabName='discover' {...props} />
            ),
          }}
        />
        <Tabs.Screen
          name='deals'
          options={{
            title: 'Deals',
            tabBarIcon: ({ color, focused }) => {
              // During onboarding, use onboarding active state, otherwise use focused state
              const isActive = isOnboardingActive
                ? isTabActive('deals')
                : focused;
              const Icon = isActive ? DealsFill : DealsOutline;
              return <Icon color={isActive ? colors.base[100] : color} />;
            },
            tabBarButton: (props) => (
              <CustomTabButton tabName='deals' {...props} />
            ),
          }}
        />
        <Tabs.Screen
          name='collections'
          options={{
            title: 'Collections',
            tabBarIcon: ({ color, focused }) => {
              // During onboarding, use onboarding active state, otherwise use focused state
              const isActive = isOnboardingActive
                ? isTabActive('collections')
                : focused;
              const Icon = isActive ? CollectionsFill : CollectionsOutline;
              return <Icon color={isActive ? colors.base[100] : color} />;
            },
            tabBarButton: (props) => (
              <CustomTabButton tabName='collections' {...props} />
            ),
          }}
        />
        <Tabs.Screen
          name='profile'
          options={{
            title: 'Profile',
            tabBarIcon: ({ color, focused }) => {
              // During onboarding, use onboarding active state, otherwise use focused state
              const isActive = isOnboardingActive
                ? isTabActive('profile')
                : focused;
              const Icon = isActive ? ProfileFill : ProfileOutline;
              return <Icon color={isActive ? colors.base[100] : color} />;
            },
            headerShown: false,
            tabBarButton: (props) => (
              <CustomTabButton tabName='profile' {...props} />
            ),
            // tabBarBadge: '2',
            tabBarBadgeStyle: {
              backgroundColor: colors.info.red,
              minWidth: moderateScale(12),
              height: moderateScale(12),
              borderRadius: moderateScale(6),
              fontSize: moderateScale(8),
              color: colors.base[100],
              paddingHorizontal: 0,
              lineHeight: moderateScale(12),
            },
          }}
        />
      </Tabs>

      <OnboardingModal
        ref={onboardingModalRef}
        onComplete={handleOnboardingComplete}
        onStepChange={handleOnboardingStepChange}
      />
    </>
  );
}
