import { colors } from '@/constants/Colors';
import { useAuthStore } from '@/store/auth';
import {
  StyleSheet,
  View,
  ScrollView,
  Alert,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Image } from 'expo-image';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { PlatformIcon } from '@/components/ui/PlatformIcon';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { SettingsItem } from '@/components/ui/SettingsItem';
import * as Application from 'expo-application';
import { useTranslation } from 'react-i18next';
import { useMeQuery } from '@/graphql/generated/graphql';
import { TextButton } from '@/components/ui/TextButton';
import { router } from 'expo-router';
import { useMemo } from 'react';
import { differenceInDays } from 'date-fns';
import { formatDate } from '@/utils/time';
import { Tooltip } from '@/components/ui/Tooltip';
import { InviteModal } from '@/components/InviteModal';
import { DevModeModal } from '@/components/ui/DevModeModal';
import { useDevMode } from '@/hooks/useDevMode';
import { useLogout } from '@/hooks/useLogout';
import { SymbolViewProps } from 'expo-symbols';

const FAQ_URL = 'https://conari.app/faq';
const TERMS_URL = 'https://conari.app/user-terms';
const PRIVACY_URL = 'https://conari.app/privacy';
const BECOME_CREATOR_URL = 'https://conari.app/creator-application';
const { width: SCREEN_WIDTH } = Dimensions.get('window');

// ActivityItem component
function ActivityItem({
  icon,
  iconColor,
  backgroundColor,
  title,
  value,
  isCurrency,
}: {
  icon: SymbolViewProps['name'];
  iconColor: string;
  backgroundColor: string;
  title: string;
  value: string | number;
  isCurrency?: boolean;
}) {
  return (
    <View style={styles.activityItem}>
      <View style={styles.activityItemContent}>
        <View style={[styles.activityIconContainer, { backgroundColor }]}>
          <PlatformIcon name={icon} size={14} color={iconColor} />
        </View>
        <Text variant='caption' color={colors.darkGrey[700]} numberOfLines={2}>
          {title}
        </Text>
      </View>
      <View style={styles.activityItemValueContainer}>
        {isCurrency && (
          <Image
            source={require('@/assets/images/dirham.avif')}
            style={styles.currencyIcon}
            tintColor={colors.darkGrey[400]}
          />
        )}
        <Text
          variant='h3'
          color={colors.darkGrey[400]}
          weight='bold'
          numberOfLines={1}
        >
          {Number(value)}
        </Text>
      </View>
    </View>
  );
}

export default function ProfileScreen() {
  const logout = useLogout();
  const { isTrialModalOpened } = useAuthStore();

  const { t } = useTranslation();

  //fetch user info
  const { data: userData, loading: userLoading } = useMeQuery();
  const user = userData?.me;
  // Dev mode hook
  const {
    currentDevUrl,
    isStaging,
    devModeModalRef,
    handleDevModeClick,
    handleDevModeSetUrl,
    handleDevModeResetUrl,
    handleDevModeCancel,
    getDisplayUrl,
  } = useDevMode();

  // Calculate trial information
  const trialInfo = useMemo(() => {
    if (!user?.expire_at || !isTrialModalOpened) {
      return null;
    }

    const trialEndDate = new Date(user.expire_at);

    const now = new Date();
    const timeRemaining = differenceInDays(trialEndDate, now);
    const daysRemaining = Math.max(0, timeRemaining);

    const isTrialActive = timeRemaining > 0;

    return {
      endDate: trialEndDate,
      daysRemaining: Math.max(0, daysRemaining),
      isActive: isTrialActive,
      formattedEndDate: formatDate(trialEndDate.toISOString()),
    };
  }, [user?.expire_at, isTrialModalOpened]);

  // Navigate to WebView for external links
  const openInWebView = (url: string, title: string) => {
    router.push({
      pathname: '/webview',
      params: { url, title },
    });
  };

  // Handle trial info click
  const handleTrialInfoClick = () => {
    if (!trialInfo) return;

    const message = trialInfo.isActive
      ? t('trial.infoMessage', { endDate: trialInfo.formattedEndDate })
      : t('trial.infoMessageExpired', { endDate: trialInfo.formattedEndDate });

    Alert.alert(t('trial.infoTitle'), message, [
      { text: t('common.ok'), style: 'default' },
    ]);
  };

  //show confirm dialog when user press logout button
  const onLogout = () => {
    Alert.alert(t('profile.logout'), t('profile.logoutConfirmation'), [
      { text: t('common.cancel'), style: 'cancel' },
      { text: t('profile.logout'), onPress: logout },
    ]);
  };

  // const user = useAuthStore((state) => state.user);
  const version = Application.nativeApplicationVersion;
  const buildNumber = Application.nativeBuildVersion;
  const totalInvites = user?.invitations?.info?.total || 0;

  return (
    <Screen style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text variant='h2' align='center'>
              {t('profile.profile')}
            </Text>
          </View>

          {/* Activity Section */}
          {userLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size='small' color={colors.primary[950]} />
            </View>
          ) : (
            <View style={styles.activitySection}>
              <View style={styles.activityGrid}>
                <ActivityItem
                  icon='checkmark.circle.fill'
                  iconColor={colors.primary[950]}
                  backgroundColor={colors.primary[200]}
                  title={t('profile.activity.dealsCompleted')}
                  value={user?.stats?.deals_completed || 0}
                />

                <ActivityItem
                  icon='bag.fill'
                  iconColor={colors.secondary[500]}
                  backgroundColor={colors.secondary[200]}
                  title={t('profile.activity.amountSaved')}
                  value={user?.stats?.amount_saved || 0}
                  isCurrency
                />

                <ActivityItem
                  icon='text.bubble.fill'
                  iconColor={colors.secondary[500]}
                  backgroundColor={colors.secondary[200]}
                  title={t('profile.activity.reviewsWritten')}
                  value={user?.stats?.reviews_written || 0}
                />

                <ActivityItem
                  icon='storefront.fill'
                  iconColor={colors.primary[950]}
                  backgroundColor={colors.primary[200]}
                  title={t('profile.activity.placesVisited')}
                  value={user?.stats?.places_visited || 0}
                />
              </View>
            </View>
          )}

          {/* send invite clickable banner  */}
          {!userLoading && (
            <Tooltip content={<InviteModal user={user} />}>
              <View style={styles.sendInviteBanner}>
                <View style={styles.sendInviteBannerContent}>
                  <Text variant='caption1' color={colors.base[100]}>
                    {totalInvites && totalInvites > 0
                      ? t('profile.invite.bannerText1', { totalInvites })
                      : t('profile.invite.emptyText1')}
                  </Text>
                  <Text variant='caption1' color={colors.base[100]}>
                    {totalInvites && totalInvites > 0
                      ? t('profile.invite.bannerText2')
                      : t('profile.invite.emptyText2')}
                  </Text>

                  {/* send an invite now  Text + arrow */}
                  <View style={styles.sendInviteBannerArrowContainer}>
                    <Text
                      variant='caption'
                      color={colors.base[100]}
                      weight='bold'
                    >
                      {totalInvites && totalInvites > 0
                        ? t('profile.invite.bannerButton')
                        : t('profile.invite.emptyButton')}
                    </Text>
                  </View>
                </View>
                <View style={styles.sendInviteBannerImageContainer}>
                  <Image
                    source={require('@/assets/images/invite.png')}
                    style={styles.sendInviteBannerImage}
                    contentFit='contain'
                  />
                </View>
              </View>
            </Tooltip>
          )}

          {/* <View style={styles.section}>
            <Text variant='h3' style={styles.sectionTitle}>
              General Settings
            </Text>
            <SettingsItem icon='person.circle' label='Personal Information' />
            <SettingsItem icon='lock' label='Password & Security' />
            <SettingsItem icon='bell.badge' label='Notification Preferences' />
            <SettingsItem icon='heart' label='Following List' />
          </View> */}

          <View style={styles.section}>
            {/* <Text variant='h3' style={styles.sectionTitle}>
              Others
            </Text> */}
            {/* terms and conditions */}
            <SettingsItem
              icon='questionmark.circle'
              label={t('profile.faq')}
              onPress={() => openInWebView(FAQ_URL, t('profile.faq'))}
            />
            <SettingsItem
              icon='plus.circle'
              label={t('profile.followingList')}
              onPress={() => router.push('/following')}
            />
            <SettingsItem
              icon='doc.text'
              label={t('profile.termsAndConditions')}
              onPress={() =>
                openInWebView(TERMS_URL, t('profile.termsAndConditions'))
              }
            />
            <SettingsItem
              icon='lock.shield'
              label={t('profile.privacyPolicy')}
              onPress={() =>
                openInWebView(PRIVACY_URL, t('profile.privacyPolicy'))
              }
            />
            <SettingsItem
              icon='person.crop.circle'
              label={t('profile.accountManagement')}
              onPress={() => router.push('/accountManagement')}
            />
            <SettingsItem
              icon='star.circle'
              label={t('profile.becomeCreator')}
              onPress={() =>
                openInWebView(BECOME_CREATOR_URL, t('profile.becomeCreator'))
              }
            />
            <SettingsItem
              icon='envelope'
              label={t('profile.feedbackSupport')}
              onPress={() => router.push('/report')}
            />

            {/* Trial Status Section */}
            {user && trialInfo && !userLoading && (
              <SettingsItem
                icon='crown'
                label={t('trial.premiumAccess')}
                rightElement={
                  <Text variant='caption1' color={colors.darkGrey[300]}>
                    {t('trial.daysLeft', { days: trialInfo.daysRemaining })}
                  </Text>
                }
                onPress={handleTrialInfoClick}
              />
            )}

            <SettingsItem
              icon='app.badge'
              label={t('profile.appVersion')}
              rightElement={
                <Text variant='caption1' color={colors.darkGrey[300]}>
                  {t('profile.v')} {version} ({buildNumber})
                </Text>
              }
            />
            {/* Dev Mode Section - Only show in staging */}
            {isStaging && (
              <SettingsItem
                icon='hammer'
                label='Dev Mode'
                rightElement={
                  <Text variant='caption' color={colors.darkGrey[300]}>
                    {currentDevUrl ? 'Custom URL' : 'Default'}
                  </Text>
                }
                onPress={handleDevModeClick}
              />
            )}
          </View>

          <View style={styles.footer}>
            {/* logout button */}
            <TextButton onPress={onLogout} variant='secondary'>
              {t('profile.logout')}
            </TextButton>
          </View>
        </View>
      </ScrollView>

      {/* Dev Mode Modal */}
      <DevModeModal
        ref={devModeModalRef}
        currentUrl={getDisplayUrl()}
        onSetUrl={handleDevModeSetUrl}
        onResetUrl={handleDevModeResetUrl}
        onCancel={handleDevModeCancel}
      />
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.softGrey[800],
  },
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(20),
    marginBottom: moderateScale(100),
  },
  header: {
    marginTop: moderateScale(24),
    marginBottom: moderateScale(16),
  },
  activitySection: {
    marginTop: moderateScale(16),
  },
  activityGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  activityItem: {
    width: '48%', // Two items per row
    height: moderateScale(52),
    borderRadius: moderateScale(16),
    backgroundColor: colors.base[100],
    marginBottom: moderateScale(16),
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(8),
    justifyContent: 'space-between',
  },
  activityItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
  },
  activityIconContainer: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderRadius: moderateScale(16),
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityItemValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
  },
  currencyIcon: {
    width: moderateScale(14),
    height: moderateScale(14),
  },
  section: {
    marginTop: moderateScale(32),
  },
  fullWidthSection: {
    marginTop: moderateScale(32),
    marginHorizontal: -moderateScale(20),
    width: SCREEN_WIDTH,
  },
  sectionTitleContainer: {
    paddingHorizontal: moderateScale(20),
    marginBottom: moderateScale(16),
  },
  sectionTitle: {
    marginVertical: moderateScale(8),
  },

  footer: {
    marginTop: moderateScale(32),
  },
  loadingContainer: {
    height: moderateScale(120),
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendInviteBanner: {
    backgroundColor: colors.primary[950],
    height: moderateScale(97),
    borderRadius: moderateScale(16),
    marginTop: moderateScale(16),
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  sendInviteBannerContent: {
    flex: 1,
    height: '100%',
    paddingVertical: verticalScale(16),
    paddingLeft: moderateScale(16),
  },
  sendInviteBannerImageContainer: {
    paddingRight: moderateScale(16),
    paddingLeft: moderateScale(2),
  },
  sendInviteBannerImage: {
    width: moderateScale(136),
    height: verticalScale(107),
  },
  sendInviteBannerArrowContainer: {
    marginTop: verticalScale(10),
  },
});
