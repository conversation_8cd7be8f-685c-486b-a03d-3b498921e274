import React from 'react';
import { View, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { colors } from '@/constants/Colors';
import { CollectionFragment } from '@/graphql/generated/graphql';
import { PlatformIcon } from './ui/PlatformIcon';
import { moderateScale } from '@/utils/scaling';
import { Text } from './ui/Text';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { useAuthStore } from '@/store/auth';

const CARD_SIZE = moderateScale(170);
const AVATAR_SIZE = moderateScale(40);
const MAX_AVATARS = 3;

type CollectionCardProps = {
  collection?: CollectionFragment;
  isNew?: boolean;
  showActions?: boolean;
  onPress?: () => void;
  isInCollection?: boolean;
  onToggle?: (isSelected: boolean) => void;
};

export const CollectionCard = ({
  collection,
  isNew,
  showActions = false,
  onPress,
  isInCollection = true,
  onToggle,
}: CollectionCardProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { shouldShowTrialModal } = useAuthStore();

  const handlePress = () => {
    if (showActions && onToggle) {
      onToggle(!isInCollection);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else if (isNew && onPress) {
      if (shouldShowTrialModal()) {
        router.push('/(modals)/trial');
        return;
      }
      onPress();
    } else if (collection) {
      router.push(`/collections/${collection.id}`);
    }
  };

  if (isNew) {
    return (
      <TouchableOpacity
        style={[styles.container, styles.newContainer]}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        <View style={styles.iconContainer}>
          <IconSymbol
            name='plus'
            size={moderateScale(18)}
            color={colors.base[100]}
          />
        </View>
        <Text variant='body'>{t('collections.newCollection')}</Text>
      </TouchableOpacity>
    );
  }

  if (!collection) return null;

  const placesItems = collection.items.filter(
    (item) => item?.collectable.__typename === 'PartnerPlace'
  );

  const restaurantCount = placesItems.length;
  const hasMorePlaces = restaurantCount > MAX_AVATARS;
  const displayedPlaces = placesItems.slice(0, MAX_AVATARS);
  const remainingCount = hasMorePlaces ? restaurantCount - MAX_AVATARS + 1 : 0;

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={styles.textContainer}>
        <Text variant='h3' numberOfLines={2}>
          {collection.title}
        </Text>
        <Text
          style={styles.restaurantCount}
          variant='caption1'
          color={colors.darkGrey[700]}
        >
          {restaurantCount}{' '}
          {t(
            restaurantCount === 1
              ? 'collections.restaurant'
              : 'collections.restaurants'
          )}
        </Text>
      </View>

      <View style={styles.bottomContainer}>
        <View style={styles.avatarsContainer}>
          {displayedPlaces.map((item, index) => {
            if (item?.collectable.__typename !== 'PartnerPlace') return null;

            const isLastWithOverlay =
              hasMorePlaces && index === MAX_AVATARS - 1;
            const marginLeft = index > 0 ? -moderateScale(15) : 0;

            return (
              <View key={item.id} style={{ zIndex: index, marginLeft }}>
                <Image
                  source={{ uri: item.collectable.avatar?.full_url || '' }}
                  style={styles.avatarContainer}
                  resizeMode='cover'
                />

                {isLastWithOverlay && (
                  <View style={styles.avatarOverlay}>
                    <Text variant='caption1' color={colors.base[100]}>
                      +{remainingCount}
                    </Text>
                  </View>
                )}
              </View>
            );
          })}
        </View>
        {/* plus or checkmark if in collection */}
        {showActions ? (
          <View
            style={[
              styles.collectionAction,
              {
                backgroundColor: isInCollection
                  ? colors.primary[950]
                  : colors.base[950],
              },
            ]}
          >
            <IconSymbol
              name={isInCollection ? 'checkmark' : 'plus'}
              size={moderateScale(12)}
              color={colors.base[100]}
            />
          </View>
        ) : null}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: CARD_SIZE,
    minHeight: CARD_SIZE - moderateScale(30),
    backgroundColor: colors.softGrey[600],
    borderRadius: moderateScale(18),
    padding: moderateScale(16),
    justifyContent: 'space-between',
  },
  newContainer: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: colors.primary[600],
    backgroundColor: colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
    gap: moderateScale(8),
  },
  textContainer: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: moderateScale(8),
  },

  avatarsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  collectionAction: {
    alignItems: 'center',
    justifyContent: 'center',
    width: moderateScale(24),
    height: moderateScale(24),
    borderRadius: moderateScale(12),
  },
  avatarContainer: {
    width: AVATAR_SIZE,
    height: AVATAR_SIZE,
    borderRadius: AVATAR_SIZE / 2,
    borderWidth: 1,
    borderColor: colors.base[100],
  },
  avatarOverlay: {
    backgroundColor: colors.blackTransparent.black06,
    position: 'absolute',
    top: 0,
    left: 0,
    width: AVATAR_SIZE,
    height: AVATAR_SIZE,
    borderRadius: AVATAR_SIZE / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },

  restaurantCount: {
    marginTop: moderateScale(4),
  },
  newCollectionText: {
    marginTop: moderateScale(8),
  },
  iconContainer: {
    width: moderateScale(40),
    height: moderateScale(40),
    backgroundColor: colors.base[950],
    borderRadius: moderateScale(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
});
