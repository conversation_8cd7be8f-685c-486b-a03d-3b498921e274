import { PlatformIcon } from '@/components/ui/PlatformIcon';
import { Text } from '@/components/ui/Text';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { AVPlaybackStatus, ResizeMode, Video } from 'expo-av';
import { Image } from 'expo-image';
import { useEffect, useRef, useState, memo, useCallback, useMemo } from 'react';
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Tag } from './ui/Tag';
import { ExpandableText } from './ui/ExpandableText';
import { ProgressBar } from './ui/ProgressBar';
import { SaveIcon } from '@/components/icons/SaveIcon';
import { LikeIcon } from '@/components/icons/LikeIcon';
import { ShareIcon } from '@/components/icons/ShareIcon';
import AntDesign from '@expo/vector-icons/AntDesign';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { DealsCarousel } from './DealsCarousel';
import * as Haptics from 'expo-haptics';
import { validateReel, type Reel as ReelType } from '@/schemas/reel';
import { useRouter } from 'expo-router';
import { PanGestureHandler } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { analyticsService, errorReportingService } from '@/services';
import { useTranslation } from 'react-i18next';
import {
  useFollowPlaceMutation,
  useLikeMutation,
  LikableType,
  useFollowCreatorMutation,
} from '@/graphql/generated/graphql';
import { showUnfollowConfirmation } from '@/utils/confirmations';
import {
  animateLike,
  animateFollow,
  AnimationPresets,
} from '@/utils/animations';
import { useAuthStore } from '@/store/auth';
import { FEATURED_MEAL_TIMES } from '@/constants/MealTimes';
import { shareReel } from '@/services/branch';
import { useLocationServices } from '@/services/locationServices';

const { height: WINDOW_HEIGHT, width: WINDOW_WIDTH } = Dimensions.get('window');

const MIN_COUNT = 10;

interface ReelStatus {
  isLiked: boolean;
  likesCount: number;
  isPlaceFollowed: boolean;
  isCreatorFollowed?: boolean;
  isPlaceInCollection?: boolean;
  savedPlacesCount: number;
}

interface ReelProps {
  reel: ReelType;
  index: number;
  isVisible: boolean;
  reelStatus?: ReelStatus;
  isStandalone?: boolean;
  isMuted?: boolean;
  toggleMute?: () => void;
  currentIndex?: string;
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

function ReelComponent({
  reel,
  isVisible,
  reelStatus,
  isStandalone,
  isMuted = false,
  toggleMute,
  currentIndex,
}: ReelProps) {
  const { t } = useTranslation();
  // Validate reel data
  const validation = validateReel(reel);
  const isReelValid = validation.success ? validation.data : null;
  const videoRef = useRef<Video>(null);
  const progressRef = useRef(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [showOverlay, setShowOverlay] = useState(false);
  const wasPlayingRef = useRef(true);
  const overlayTimeoutRef = useRef<NodeJS.Timeout>();
  const progressBar = useRef<View>(null);
  const [isLiked, setIsLiked] = useState(reelStatus?.isLiked || false);
  const [likesCount, setLikesCount] = useState(reelStatus?.likesCount || 0);
  const [savedPlacesCount, setSavedPlacesCount] = useState(
    reelStatus?.savedPlacesCount || 0
  );
  const [isRestaurantFollowed, setIsRestaurantFollowed] = useState(
    reelStatus?.isPlaceFollowed
  );
  const [followLoading, setFollowLoading] = useState(false);
  const [isCreatorFollowed, setIsCreatorFollowed] = useState(
    reelStatus?.isCreatorFollowed
  );
  const [isPlaceInCollection, setIsPlaceInCollection] = useState(
    reelStatus?.isPlaceInCollection
  );
  const [creatorFollowLoading, setCreatorFollowLoading] = useState(false);
  const router = useRouter();

  // Add location services
  const locationServices = useLocationServices();

  // Animation values for like button
  const likeScale = useSharedValue(1);
  const likeRotate = useSharedValue(0);
  const creatorFollowScale = useSharedValue(1);

  // check if reel is creator reel
  const isCreatorReel = !!reel.creator?.id;
  // Check if current user is the creator
  const currentUser = useAuthStore.getState().user;
  const isCurrentUserCreator = currentUser?.creator?.id === reel.creator?.id;

  // Update local state when reelStatus changes
  useEffect(() => {
    if (reelStatus) {
      setIsLiked(reelStatus.isLiked);
      setLikesCount(reelStatus.likesCount);
      setIsRestaurantFollowed(reelStatus.isPlaceFollowed);
      setIsCreatorFollowed(reelStatus.isCreatorFollowed);
      setIsPlaceInCollection(reelStatus.isPlaceInCollection);
      setSavedPlacesCount(reelStatus.savedPlacesCount);
    }
  }, [reelStatus]);

  // Follow place mutation
  const [followPlace] = useFollowPlaceMutation({
    // Note: refetch ReelStatus after following/un following place
    // specifically because there is many reels for the same place so we need to update the reelStatus
    refetchQueries: ['UserFollowedPlaces', 'ReelStatus'],
    onError: (error) => {
      console.error('Error following/unfollowing place:', error);
      setFollowLoading(false);
    },
  });

  // Like mutation
  const [likeMutation, { loading: likeLoading }] = useLikeMutation({
    onError: (error) => {
      console.error('Error liking/unliking reel:', error);
    },
  });

  // Follow creator mutation
  const [followCreator] = useFollowCreatorMutation({
    onError: (error) => {
      console.error('Error following/unfollowing creator:', error);
      setCreatorFollowLoading(false);
    },
    refetchQueries: ['ReelStatus'],
  });

  // Animated style for like button
  const animatedLikeStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: likeScale.value },
        { rotate: `${likeRotate.value}deg` },
      ],
    };
  });

  // Add new animated styles
  const animatedCreatorFollowStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: creatorFollowScale.value }],
    };
  });

  // Update handleLike function to use the animation utility
  const handleLike = () => {
    if (!isReelValid || likeLoading) return;

    const newLikedState = !isLiked;
    setIsLiked(newLikedState);
    setLikesCount((prevCount) =>
      newLikedState ? prevCount + 1 : prevCount - 1
    );

    // Use the animation utility from the separate file
    animateLike(likeScale, likeRotate, newLikedState);

    // Call the API
    likeMutation({
      variables: {
        id: reel.objectID,
        type: LikableType.Reel,
      },
      onCompleted: (data) => {
        if (!data.like.status) {
          // Revert the like state and likes count if the API response indicates failure.
          // This ensures the UI reflects the correct state in case the optimistic update was incorrect.
          setIsLiked(!newLikedState);
          setLikesCount((prevCount) =>
            newLikedState ? prevCount - 1 : prevCount + 1
          );
        }
      },
      onError: () => {
        setIsLiked(!newLikedState);
        setLikesCount((prevCount) =>
          newLikedState ? prevCount - 1 : prevCount + 1
        );
      },
    });

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Animation values for swipe gesture using Reanimated
  const translateX = useSharedValue(0);

  // Threshold to determine when to navigate
  const SWIPE_THRESHOLD = WINDOW_WIDTH * 0.3;

  // Add function to navigate to reels screen
  const navigateToPlaceReels = () => {
    if (!reel.places.length || !reel.places[0].id) return;
    // Track click event
    //if not standalone, track click event
    if (!isStandalone)
      analyticsService.trackReelClicked(reel, currentIndex || 'reels');

    // Navigate to the place reels screen with this reel as the starting point
    router.push({
      pathname: '/places/[id]/reels' as const,
      params: { id: reel.places[0].id, reelId: reel.objectID },
    });
  };

  const navigateToCreatorReels = () => {
    if (!reel.creator?.id) return;
    // Track click event
    //if not standalone, track click event
    if (!isStandalone)
      analyticsService.trackReelClicked(reel, currentIndex || 'reels');

    // Navigate to the creator reels screen with this reel as the starting point
    router.push({
      pathname: '/creators/[id]/reels' as const,
      params: { id: reel.creator?.id, reelId: reel.objectID },
    });
  };

  // Ref to track if this reel has already been viewed to prevent duplicate events
  const hasBeenViewedRef = useRef(false);

  // Track view events when the reel becomes visible
  useEffect(() => {
    if (isVisible && isReelValid && !hasBeenViewedRef.current) {
      // Track reel view when it becomes visible for the first time
      analyticsService.trackReelViewed(reel, currentIndex || 'reels');
      hasBeenViewedRef.current = true;
    }
  }, [isVisible, isReelValid, reel, currentIndex]);

  const navigateToPlace = () => {
    // In standalone mode and not creator reel, the navigation behavior intentionally calls router.back()
    // to return to the previous screen. This design ensures a consistent user experience
    // by avoiding navigation to a new place screen when the app is in standalone mode.
    if (isStandalone && !isCreatorReel) {
      router.back();
      return;
    }

    // Track click event using the analytics service with only the reel object
    if (!isStandalone)
      analyticsService.trackReelClicked(reel, currentIndex || 'reels');

    // Navigate to the place
    router.push(`/places/${reel.places[0].id}`);
  };

  //navigate to creator profile
  const navigateToCreatorProfile = () => {
    if (isCreatorReel) {
      router.push(`/creators/${reel.creator?.id}`);
    }
  };

  // Update the gesture handler to use animation presets
  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, ctx: any) => {
      ctx.startX = translateX.value;
    },
    onActive: (event, ctx) => {
      if (isStandalone) {
        // In standalone mode, don't allow any swipes
        return;
      }

      // Allow both left and right swipes when not in standalone mode
      translateX.value = ctx.startX + event.translationX;
    },
    onEnd: (event) => {
      // Don't handle swipe in standalone mode
      if (isStandalone) {
        translateX.value = withSpring(0, AnimationPresets.SPRING_CONFIG);
        return;
      }

      // Only handle horizontal gestures - ignore if vertical movement is greater
      const isHorizontalSwipe =
        Math.abs(event.translationX) > Math.abs(event.translationY);

      if (isHorizontalSwipe) {
        // Handle left swipe to navigate to place screen
        if (event.translationX < -SWIPE_THRESHOLD) {
          //if place reel, navigate to place screen else if creator reel, navigate to creator profile
          if (isCreatorReel) {
            runOnJS(navigateToCreatorProfile)();
          } else {
            runOnJS(navigateToPlace)();
          }
        }

        // Handle right swipe to navigate to reels screen
        else if (event.translationX > SWIPE_THRESHOLD) {
          // if place reel navigate to reels place screen else if creator reel navigate to reels creator profile
          if (isCreatorReel) {
            runOnJS(navigateToCreatorReels)();
          } else {
            runOnJS(navigateToPlaceReels)();
          }
        }
      }

      // Reset animation with preset configuration
      translateX.value = withSpring(0, AnimationPresets.SPRING_CONFIG);
    },
  });

  // Calculate bottom padding based on standalone mode
  const bottomSectionStyle = {
    ...styles.bottomSection,
    paddingBottom: isStandalone ? verticalScale(80) : verticalScale(120),
  };

  // Adjust tags container position for standalone mode
  const tagsContainerStyle = {
    ...styles.tagsContainer,
    bottom: isStandalone ? verticalScale(60) : verticalScale(100),
  };

  // Adjust right sidebar position for standalone mode
  const rightSidebarStyle = {
    ...styles.rightSidebar,
    bottom: isStandalone ? verticalScale(110) : verticalScale(150),
  };

  useEffect(() => {
    if (isVisible) {
      // Clear any existing timeout
      if (overlayTimeoutRef.current) {
        clearTimeout(overlayTimeoutRef.current);
      }
      videoRef.current?.playAsync();
      setIsPlaying(true);
      setShowOverlay(false);
      wasPlayingRef.current = true;
    } else {
      videoRef.current?.pauseAsync();
      setIsPlaying(false);
    }

    // Cleanup timeouts on unmount
    return () => {
      if (overlayTimeoutRef.current) {
        clearTimeout(overlayTimeoutRef.current);
      }
    };
  }, [isVisible]);

  const onPlaybackStatusUpdate = (status: AVPlaybackStatus) => {
    if (status.isLoaded && status.durationMillis && status.positionMillis) {
      const newProgress = status.positionMillis / status.durationMillis;
      progressRef.current = newProgress;
      // Force update progress bar
      if (progressBar.current) {
        progressBar.current.setNativeProps({
          style: { width: `${newProgress * 100}%` },
        });
      }
    }
  };

  const togglePlayPause = async () => {
    try {
      if (!videoRef.current) return;

      if (isPlaying) {
        await videoRef.current.pauseAsync();
        setShowOverlay(true);
      } else {
        await videoRef.current.playAsync();
        // Add a small delay before hiding the overlay to make the transition smoother
        overlayTimeoutRef.current = setTimeout(() => {
          setShowOverlay(false);
        }, 100);
      }

      setIsPlaying(!isPlaying);
      wasPlayingRef.current = !isPlaying;
    } catch (error) {
      console.log('Error toggling play/pause:', error);
    }
  };

  const renderRating = useCallback((rating: number, reviews_count: number) => {
    return (
      <View style={styles.ratingContainer}>
        <AntDesign
          name='google'
          size={moderateScale(8)}
          color={colors.base[100]}
        />
        <Text variant='tiny' color={colors.base[100]}>
          {rating}
        </Text>
        <AntDesign
          name='star'
          size={moderateScale(8)}
          color={colors.yellow[950]}
        />
        <Text variant='tiny' color={colors.base[100]}>
          ({reviews_count})
        </Text>
      </View>
    );
  }, []);

  // Update handleRestaurantFollow to remove animations
  const handleRestaurantFollow = () => {
    if (followLoading || !isReelValid?.places[0]) return;

    const placeId = isReelValid.places[0].id;
    const placeName = isReelValid.partner?.name || 'this place';

    const executeFollow = () => {
      setFollowLoading(true);
      followPlace({
        variables: {
          input: { id: placeId },
        },
        onCompleted: (data) => {
          if (data.followPlace.status) {
            setIsRestaurantFollowed((prev) => !prev);
          }
          setFollowLoading(false);
        },
      });
    };

    if (isRestaurantFollowed) {
      showUnfollowConfirmation(t, placeName, executeFollow);
    } else {
      executeFollow();
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Update handleCreatorFollow to use the animation utility
  const handleCreatorFollow = () => {
    if (creatorFollowLoading || !reel.creator?.id) return;

    // Use the animation utility from the separate file
    animateFollow(creatorFollowScale);

    setCreatorFollowLoading(true);
    followCreator({
      variables: {
        input: { id: reel.creator.id.toString() },
      },
      onCompleted: (data) => {
        if (data.followCreator.status) {
          setIsCreatorFollowed((prev) => !prev);
        }
        setCreatorFollowLoading(false);
      },
    });

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Add a check for places[0]
  const place = reel?.places[0];
  const partner = reel?.partner;
  if (!isReelValid || !place) {
    // Use the error reporting service
    errorReportingService.reportInvalidReel(reel.objectID, validation.error);
  }

  // Calculate distance - use geoDistance if available, otherwise calculate using locationServices
  const calculatedDistance = useMemo(() => {
    // First, try to use the geoDistance from Algolia ranking info
    if (reel?._rankingInfo?.geoDistance) {
      return (reel._rankingInfo.geoDistance / 1000).toFixed(1);
    }

    // If no geoDistance but we have user location and place coordinates, calculate distance
    if (
      locationServices.locationPermissionStatus === 'granted' &&
      reel?._geoloc?.[0]?.lat &&
      reel?._geoloc?.[0]?.lng
    ) {
      const distance = locationServices.calculateDistanceToPlace(
        reel._geoloc[0].lat,
        reel._geoloc[0].lng
      );
      return distance ? distance.toFixed(1) : null;
    }

    return null;
  }, [
    reel?._rankingInfo?.geoDistance,
    locationServices.locationPermissionStatus,
    locationServices.calculateDistanceToPlace,
    reel?._geoloc?.[0]?.lat,
    reel?._geoloc?.[0]?.lng,
  ]);

  //combine tags from place
  const tags = [
    ...(place?.cuisine_types || []),
    ...(place?.cravings || []),
    ...(place?.specialities || []),
    //meal times but only show featured meal times
    ...(place?.meal_times || []).filter((mealTime) =>
      FEATURED_MEAL_TIMES.has(mealTime.toLowerCase())
    ),
    ...(place?.ambiance || []),
    ...(place?.parking || []),
  ];

  // Update mute button handler to use the passed toggleMute function
  const handleToggleMute = () => {
    if (toggleMute) {
      toggleMute();
    }
  };

  return (
    <View style={styles.container}>
      <PanGestureHandler
        onGestureEvent={gestureHandler}
        activeOffsetX={[-20, 20]}
        failOffsetY={[-20, 20]}
        shouldCancelWhenOutside={true}
      >
        <Animated.View style={styles.animatedVideoContainer}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={togglePlayPause}
            style={styles.videoContainer}
          >
            <Video
              ref={videoRef}
              style={StyleSheet.absoluteFillObject}
              // source={{
              //   uri: 'https://storage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
              // }}
              //hide the video url in dev mode
              source={{ uri: __DEV__ ? '' : reel.url }}
              resizeMode={ResizeMode.COVER}
              isLooping
              shouldPlay={isVisible && isPlaying}
              //mute video in dev mode
              isMuted={__DEV__ ? true : isMuted}
              onPlaybackStatusUpdate={onPlaybackStatusUpdate}
            />

            {/* Overlay (if video is paused) */}
            {!isPlaying && showOverlay && (
              <TouchableOpacity
                style={styles.playPauseOverlay}
                onPress={togglePlayPause}
                activeOpacity={0.8}
              >
                <PlatformIcon
                  name='play'
                  size={moderateScale(50)}
                  color={colors.base[100]}
                />
              </TouchableOpacity>
            )}

            {/* Bottom section */}
            <LinearGradient
              colors={['transparent', colors.blackTransparent.black09]}
              style={bottomSectionStyle}
            >
              <View style={styles.bottomLeftSection}>
                <Pressable
                  style={({ pressed }) => [
                    styles.restaurantContainer,
                    pressed && { opacity: 0.8, transform: [{ scale: 0.99 }] },
                  ]}
                  onPress={navigateToPlace}
                  android_ripple={{ color: 'rgba(255, 255, 255, 0.2)' }}
                >
                  {place?.avatar ? (
                    <Image
                      source={{
                        uri: place?.avatar,
                      }}
                      style={styles.restaurantImage}
                      contentFit='cover'
                    />
                  ) : (
                    <PlatformIcon
                      name='storefront'
                      size={moderateScale(40)}
                      color={colors.base[100]}
                    />
                  )}
                  <View style={styles.restaurantInfo}>
                    <View style={styles.restaurantHeader}>
                      <Text
                        variant='caption'
                        color={colors.base[100]}
                        weight='bold'
                        numberOfLines={1}
                      >
                        {partner?.name}
                      </Text>
                      <Text
                        variant='caption1'
                        color={colors.base[100]}
                        numberOfLines={1}
                      >
                        {place?.name}
                      </Text>
                    </View>
                    <View style={styles.restaurantDetails}>
                      {renderRating(
                        place?.rates?.google || 0,
                        place?.rates?.reviews_count || 0
                      )}
                      {calculatedDistance && (
                        <View style={styles.locationContainer}>
                          <FontAwesome6
                            name='location-dot'
                            size={moderateScale(8)}
                            color={colors.base[100]}
                          />
                          <Text
                            variant='tiny'
                            color={colors.base[100]}
                            numberOfLines={1}
                          >
                            {`${calculatedDistance} ${t('common.km')} `}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>
                  <TouchableOpacity
                    style={styles.followButton}
                    onPress={handleRestaurantFollow}
                    disabled={followLoading}
                  >
                    {followLoading ? (
                      <View style={styles.followButtonContent}>
                        <ActivityIndicator size={14} color={colors.base[100]} />
                      </View>
                    ) : (
                      <Text
                        variant='caption'
                        color={colors.base[100]}
                        weight='bold'
                      >
                        {isRestaurantFollowed
                          ? t('place.following')
                          : '+ ' + t('place.follow')}
                      </Text>
                    )}
                  </TouchableOpacity>
                </Pressable>
                {place?.deals && place?.deals.length > 0 ? (
                  <DealsCarousel
                    onDealsPress={navigateToPlace}
                    deals={place?.deals}
                  />
                ) : null}
                {reel?.caption && <ExpandableText text={reel?.caption} />}
              </View>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>
      </PanGestureHandler>

      {/* Tags section - moved outside of PanGestureHandler */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={tagsContainerStyle}
        contentContainerStyle={styles.tagsContentContainer}
      >
        {tags?.map((tag, index) => (
          <Tag
            key={index}
            label={tag}
            customStyle={styles.tag}
            variant='dark'
          />
        ))}
      </ScrollView>

      <ProgressBar
        progress={progressRef.current}
        progressBarRef={progressBar}
        isStandalone={isStandalone}
      />

      {/* Right sidebar */}
      <View style={rightSidebarStyle}>
        {reel?.creator && (
          <View style={styles.creatorContainer}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={navigateToCreatorProfile}
            >
              <View style={styles.creatorImageContainer}>
                <Image
                  source={{ uri: reel.creator?.avatar }}
                  style={styles.creatorImage}
                  contentFit='cover'
                />
                {!isCurrentUserCreator && (
                  <Animated.View
                    style={[
                      styles.creatorFollowButton,
                      animatedCreatorFollowStyle,
                      {
                        backgroundColor: isCreatorFollowed
                          ? colors.primary[950]
                          : colors.base[100],
                      },
                    ]}
                  >
                    <TouchableOpacity
                      onPress={handleCreatorFollow}
                      disabled={creatorFollowLoading}
                      hitSlop={{ top: 0, bottom: 20, left: 20, right: 20 }}
                      style={styles.creatorFollowButtonTouchable}
                    >
                      {creatorFollowLoading ? (
                        <ActivityIndicator
                          size='small'
                          color={
                            isCreatorFollowed
                              ? colors.base[100]
                              : colors.primary[950]
                          }
                          style={{ transform: [{ scale: 0.5 }] }}
                        />
                      ) : isCreatorFollowed ? (
                        <FontAwesome6
                          name='check'
                          size={moderateScale(8)}
                          color={colors.base[100]}
                        />
                      ) : (
                        <FontAwesome6
                          name='plus'
                          size={moderateScale(8)}
                          color={colors.primary[950]}
                        />
                      )}
                    </TouchableOpacity>
                  </Animated.View>
                )}
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={1}
              onPress={
                isCurrentUserCreator
                  ? navigateToCreatorProfile
                  : handleCreatorFollow
              }
            >
              <Text style={styles.creatorName} numberOfLines={1} variant='tiny'>
                {reel.creator?.name}
              </Text>
            </TouchableOpacity>
          </View>
        )}
        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={() => {
            router.push({
              pathname: '/(modals)/collections',
              params: {
                id: place?.id,
                avatar: place?.avatar,
              },
            });
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
        >
          <SaveIcon
            color={isPlaceInCollection ? colors.primary[950] : colors.base[100]}
          />
          {savedPlacesCount >= MIN_COUNT && (
            <Text
              style={styles.sidebarText}
              variant='tiny'
              color={colors.base[100]}
            >
              {formatNumber(savedPlacesCount)}
            </Text>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.sidebarButton, styles.sidebarButtonLike]}
          onPress={handleLike}
          hitSlop={{ top: 10, bottom: 20, left: 20, right: 20 }}
        >
          <Animated.View style={animatedLikeStyle}>
            <LikeIcon color={isLiked ? colors.info.red : colors.base[100]} />
          </Animated.View>
          {likesCount >= MIN_COUNT && (
            <Text
              style={styles.sidebarText}
              variant='tiny'
              color={colors.base[100]}
            >
              {formatNumber(likesCount)}
            </Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={async () => {
            // Track share event
            analyticsService.trackReelShared(reel, currentIndex || 'reels');

            // Use the branch service to share the reel
            shareReel(reel);
          }}
          hitSlop={{ top: 20, bottom: 10, left: 20, right: 20 }}
        >
          <ShareIcon color={colors.base[100]} />
        </TouchableOpacity>

        {/* mute and unmute button */}
        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={handleToggleMute}
          hitSlop={{ top: 10, bottom: 20, left: 20, right: 20 }}
        >
          <IconSymbol
            name={isMuted ? 'speaker.slash.fill' : 'speaker.wave.2.fill'}
            size={moderateScale(24)}
            color={colors.base[100]}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
}

// Memoize the component with a custom comparison function
export default memo(ReelComponent, (prevProps, nextProps) => {
  // Only re-render if these props change
  return (
    prevProps.isVisible === nextProps.isVisible &&
    prevProps.index === nextProps.index &&
    prevProps.reel.objectID === nextProps.reel.objectID &&
    prevProps.reelStatus?.isLiked === nextProps.reelStatus?.isLiked &&
    prevProps.reelStatus?.likesCount === nextProps.reelStatus?.likesCount &&
    prevProps.reelStatus?.isPlaceFollowed ===
      nextProps.reelStatus?.isPlaceFollowed &&
    prevProps.reelStatus?.isCreatorFollowed ===
      nextProps.reelStatus?.isCreatorFollowed &&
    prevProps.reelStatus?.isPlaceInCollection ===
      nextProps.reelStatus?.isPlaceInCollection &&
    prevProps.reelStatus?.savedPlacesCount ===
      nextProps.reelStatus?.savedPlacesCount &&
    prevProps.isStandalone === nextProps.isStandalone &&
    prevProps.isMuted === nextProps.isMuted &&
    prevProps.currentIndex === nextProps.currentIndex
  );
});

const styles = StyleSheet.create({
  container: {
    width: WINDOW_WIDTH,
    height: WINDOW_HEIGHT,
    backgroundColor: colors.base[950],
  },
  animatedVideoContainer: {
    flex: 1,
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  rightSidebar: {
    position: 'absolute',
    right: moderateScale(12),
    bottom: verticalScale(150),
    alignItems: 'center',
    zIndex: 2,
    width: moderateScale(24),
  },
  sidebarButton: {
    alignItems: 'center',
    marginVertical: verticalScale(14),
    shadowColor: colors.base[950],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.85,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sidebarButtonLike: {
    // height: verticalScale(30),
  },
  sidebarText: {
    marginTop: moderateScale(4),
  },
  bottomSection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    paddingHorizontal: moderateScale(16),
    paddingTop: verticalScale(20),
    paddingBottom: verticalScale(120),
  },
  bottomLeftSection: {
    width: '88%',
    marginRight: moderateScale(16),
  },
  restaurantContainer: {
    flexDirection: 'row',
    marginBottom: moderateScale(12),
    gap: moderateScale(12),
    alignItems: 'center',
  },
  restaurantImage: {
    width: moderateScale(42),
    height: moderateScale(42),
    borderRadius: moderateScale(21),
  },
  restaurantInfo: {
    flex: 1,
    gap: moderateScale(4),
  },
  restaurantHeader: {
    // flexDirection: 'row',
    // alignItems: 'center',
    // justifyContent: 'space-between',
  },
  restaurantDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
  },
  followButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateScale(8),
    borderRadius: moderateScale(20),
    borderWidth: 1,
    borderColor: colors.base[100],
    minWidth: moderateScale(90),
    height: moderateScale(36),
    alignItems: 'center',
    justifyContent: 'center',
  },
  tagsContainer: {
    position: 'absolute',
    bottom: verticalScale(100),
    left: 0,
    right: 0,
    paddingHorizontal: moderateScale(16),
    zIndex: 10,
  },
  tagsContentContainer: {
    paddingRight: moderateScale(16),
  },
  tag: {
    marginRight: moderateScale(8),
  },
  playPauseOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.blackTransparent.black03,
  },
  creatorContainer: {
    alignItems: 'center',
    marginBottom: verticalScale(14),
    gap: moderateScale(4),
  },
  creatorImageContainer: {
    width: moderateScale(42),
    height: moderateScale(42),
    borderRadius: moderateScale(21),
    borderWidth: 1,
    borderColor: colors.base[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  creatorImage: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(21),
  },
  creatorFollowButton: {
    position: 'absolute',
    bottom: -moderateScale(8),
    backgroundColor: colors.primary[950],
    width: moderateScale(14),
    height: moderateScale(14),
    borderRadius: moderateScale(7),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 100,
  },
  creatorName: {
    color: colors.base[100],
    marginTop: verticalScale(6),
    minWidth: moderateScale(45),
    textAlign: 'center',
  },
  followButtonContent: {
    minWidth: moderateScale(60),
    height: moderateScale(20),
    alignItems: 'center',
    justifyContent: 'center',
  },
  followButtonTouchable: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  creatorFollowButtonTouchable: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
