import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Pressable,
  Linking,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { Text } from './ui/Text';
import { colors } from '../constants/Colors';
import { moderateScale, verticalScale } from '../utils/scaling';
import { Modalize } from 'react-native-modalize';
import ImageViewer from 'react-native-image-zoom-viewer';
import { PlatformIcon } from './ui/PlatformIcon';
import { Modal } from './ui/Modal';
import { useTranslation } from 'react-i18next';

// Try-catch to handle PDF reader import that might not be supported in current Expo version
let PDFReader: any;
try {
  PDFReader = require('rn-pdf-reader-js').default;
} catch (error) {
  // PDFReader will be undefined if import fails
  console.warn('PDF Reader module could not be loaded:', error);
}

const { width: WINDOW_WIDTH } = Dimensions.get('window');

// Define the type for media items
export interface MediaItem {
  id?: string;
  full_url: string;
  mime_type?: string;
  name?: string;
}

export interface MediaModalRef {
  open: (index: number) => void;
  close: () => void;
}

interface MediaModalProps {
  items: MediaItem[];
}

export const MediaModal = forwardRef<MediaModalRef, MediaModalProps>(
  function MediaModal({ items }, ref) {
    const { t } = useTranslation();
    const modalRef = useRef<Modalize>(null);
    const [selectedIndex, setSelectedIndex] = React.useState<number>(0);

    useImperativeHandle(ref, () => ({
      open: (index) => {
        setSelectedIndex(index);
        modalRef.current?.open();
      },
      close: () => {
        modalRef.current?.close();
      },
    }));

    const isPDF = (item: MediaItem) => {
      return (
        item.mime_type?.includes('pdf') ||
        item.full_url.toLowerCase().endsWith('.pdf')
      );
    };

    // Prepare images for the ImageViewer component
    const imageUrls = items
      .filter((item) => !isPDF(item))
      .map((item) => ({ url: item.full_url }));

    // Create a mapping from original indices to image-only indices
    const originalToImageIndex = new Map();
    items.forEach((item, index) => {
      if (!isPDF(item)) {
        const imageIndex = imageUrls.findIndex(
          (img) => img.url === item.full_url
        );
        if (imageIndex !== -1) {
          originalToImageIndex.set(index, imageIndex);
        }
      }
    });

    // Render fullscreen content based on type
    const renderFullscreenContent = () => {
      // First, check if the selected index is a PDF
      if (selectedIndex < items.length && isPDF(items[selectedIndex])) {
        const pdfItem = items[selectedIndex];

        if (PDFReader) {
          return (
            <View style={styles.fullscreenPdfContainer}>
              <PDFReader
                source={{ uri: pdfItem.full_url }}
                withPinchZoom
                withScroll
                style={styles.pdfReader}
                contentContainerStyle={styles.pdfContent}
                noLoader={false}
                customStyle={{
                  readerContainerZoomContainerButton: styles.pdfZoomButton,
                  readerContainerNavigate: styles.pdfNavigate,
                }}
              />
            </View>
          );
        } else {
          // Fallback for when PDF reader is not available
          return (
            <View style={styles.pdfFallbackContainer}>
              <Text
                variant='body'
                color={colors.base[100]}
                style={styles.pdfText}
              >
                {t('media.pdfViewer.notAvailable')}
              </Text>
              <Pressable
                style={styles.openExternalButton}
                onPress={() => Linking.openURL(pdfItem.full_url)}
              >
                <Text variant='body' color={colors.base[100]}>
                  {t('media.pdfViewer.openExternally')}
                </Text>
              </Pressable>
            </View>
          );
        }
      }

      // If not a PDF, show the image viewer
      return (
        <View style={styles.fullscreenImageContainer}>
          <ImageViewer
            imageUrls={imageUrls}
            index={originalToImageIndex.get(selectedIndex) || 0}
            enableSwipeDown={false}
            backgroundColor='transparent'
            saveToLocalByLongPress={false}
            renderIndicator={() => <View />}
            style={styles.imageViewer}
            enableImageZoom
            useNativeDriver
            onClick={() => modalRef.current?.close()}
            menus={({}) => <View />}
            menuContext={{ saveToLocal: 'Save', cancel: 'Cancel' }}
            enablePreload
            pageAnimateTime={200}
            doubleClickInterval={300}
            loadingRender={() => (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size='large' color={colors.primary[500]} />
                <Text
                  variant='body'
                  color={colors.base[100]}
                  style={styles.loaderText}
                >
                  {t('media.loading')}
                </Text>
              </View>
            )}
            renderHeader={() =>
              imageUrls.length > 1 ? (
                <View style={styles.imageCounter}>
                  <Text variant='tiny' color={colors.base[100]}>
                    {(originalToImageIndex.get(selectedIndex) || 0) + 1}/
                    {imageUrls.length}
                  </Text>
                </View>
              ) : (
                <View />
              )
            }
            onChange={(index) => {
              // Find the original index that maps to this image index
              for (const [
                origIndex,
                imgIndex,
              ] of originalToImageIndex.entries()) {
                if (imgIndex === index) {
                  setSelectedIndex(origIndex);
                  break;
                }
              }
            }}
          />
        </View>
      );
    };

    if (items.length === 0) {
      return null;
    }

    return (
      <Modal
        ref={modalRef}
        modalStyle={styles.fullscreenModal}
        handlePosition='outside'
        withHandle={false}
        modalHeight={Dimensions.get('window').height}
        closeOnOverlayTap
        overlayStyle={styles.modalOverlay}
        onOpen={() => {
          StatusBar.setHidden(true);
        }}
        onClose={() => {
          StatusBar.setHidden(false);
        }}
        contentContainerStyle={styles.fullscreenContainer}
        disableAdjustToContent={true}
        panGestureEnabled={false}
        scrollViewProps={{
          scrollEnabled: false,
          showsVerticalScrollIndicator: false,
          showsHorizontalScrollIndicator: false,
          keyboardShouldPersistTaps: 'always',
        }}
      >
        <Pressable
          style={styles.closeButton}
          onPress={() => modalRef.current?.close()}
        >
          <PlatformIcon name='xmark' size={24} color={colors.base[100]} />
        </Pressable>
        {renderFullscreenContent()}
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  pdfText: {
    marginTop: verticalScale(10),
  },
  fullscreenModal: {
    backgroundColor: colors.base[950],
    margin: 0,
    borderRadius: 0,
    height: Dimensions.get('window').height,
  },
  modalOverlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  fullscreenContainer: {
    flex: 1,
    height: Dimensions.get('window').height,
    position: 'relative',
    padding: 0,
    pointerEvents: 'box-none',
  },
  closeButton: {
    position: 'absolute',
    top: verticalScale(40),
    right: moderateScale(20),
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  fullscreenImageContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    width: '100%',
    height: '100%',
  },
  imageViewer: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  fullscreenPdfContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
    backgroundColor: colors.base[950],
  },
  pdfReader: {
    flex: 1,
    width: '100%',
    height: '100%',
    backgroundColor: 'white',
  },
  pdfContent: {
    padding: 0,
    margin: 0,
  },
  pdfZoomButton: {
    bottom: verticalScale(70),
  },
  pdfNavigate: {
    bottom: verticalScale(10),
  },
  pdfFallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(20),
  },
  openExternalButton: {
    marginTop: verticalScale(20),
    paddingHorizontal: moderateScale(20),
    paddingVertical: verticalScale(10),
    backgroundColor: colors.primary[950],
    borderRadius: moderateScale(8),
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderText: {
    marginTop: verticalScale(10),
  },
  imageCounter: {
    position: 'absolute',
    top: verticalScale(40),
    left: moderateScale(20),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: moderateScale(12),
    padding: moderateScale(8),
    zIndex: 10,
  },
});
