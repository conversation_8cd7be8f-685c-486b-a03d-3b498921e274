import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Platform } from 'react-native';
import { MockedProvider } from '@apollo/client/testing';
import OnboardingScreen from '@/app/(auth)/onboarding';
import { SocialLoginDocument, SocialLoginProvider } from '@/graphql/generated/graphql';

// Mock the Google Sign-In library
jest.mock('@react-native-google-signin/google-signin', () => ({
  GoogleSignin: {
    configure: jest.fn(),
    hasPlayServices: jest.fn().mockResolvedValue(true),
    signIn: jest.fn().mockResolvedValue({
      user: {
        givenName: 'John',
        familyName: 'Doe',
        email: '<EMAIL>',
      },
    }),
    getTokens: jest.fn().mockResolvedValue({
      idToken: 'mock-id-token',
    }),
  },
  statusCodes: {
    SIGN_IN_CANCELLED: 'SIGN_IN_CANCELLED',
    IN_PROGRESS: 'IN_PROGRESS',
    PLAY_SERVICES_NOT_AVAILABLE: 'PLAY_SERVICES_NOT_AVAILABLE',
  },
}));

// Mock other dependencies
jest.mock('expo-router', () => ({
  router: {
    replace: jest.fn(),
  },
}));

jest.mock('@/store/auth', () => ({
  useAuthStore: () => ({
    setUser: jest.fn(),
    setToken: jest.fn(),
    setStatusCode: jest.fn(),
    simulateDevLogin: jest.fn(),
  }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock('@/services', () => ({
  deviceService: {
    isOldDevice: () => true,
  },
}));

const mockSocialLoginMutation = {
  request: {
    query: SocialLoginDocument,
    variables: {
      provider: SocialLoginProvider.Google,
      token: 'mock-id-token',
      firstName: 'John',
      lastName: 'Doe',
    },
  },
  result: {
    data: {
      socialLogin: {
        token: 'mock-auth-token',
        user: {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          status: null,
          fcm_token: null,
          created_at: null,
          creator: null,
          invitations: {
            info: {
              remaining: 5,
              total: 5,
            },
          },
        },
        status_code: 'SUCCESS',
      },
    },
  },
};

describe('Google Sign-In', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should show Google Sign-In button only on Android', () => {
    // Mock Platform.OS to be Android
    Platform.OS = 'android';

    const { getByText, queryByText } = render(
      <MockedProvider mocks={[mockSocialLoginMutation]} addTypename={false}>
        <OnboardingScreen />
      </MockedProvider>
    );

    // Should show Google Sign-In button on Android
    expect(getByText('login.continueGoogle')).toBeTruthy();

    // Mock Platform.OS to be iOS
    Platform.OS = 'ios';

    const { queryByText: queryByTextIOS } = render(
      <MockedProvider mocks={[mockSocialLoginMutation]} addTypename={false}>
        <OnboardingScreen />
      </MockedProvider>
    );

    // Should not show Google Sign-In button on iOS
    expect(queryByTextIOS('login.continueGoogle')).toBeNull();
  });

  it('should handle Google Sign-In flow successfully', async () => {
    Platform.OS = 'android';

    const { getByText } = render(
      <MockedProvider mocks={[mockSocialLoginMutation]} addTypename={false}>
        <OnboardingScreen />
      </MockedProvider>
    );

    const googleSignInButton = getByText('login.continueGoogle');
    fireEvent.press(googleSignInButton);

    // Wait for the sign-in process to complete
    await waitFor(() => {
      // Verify that Google Sign-In was called
      const { GoogleSignin } = require('@react-native-google-signin/google-signin');
      expect(GoogleSignin.hasPlayServices).toHaveBeenCalled();
      expect(GoogleSignin.signIn).toHaveBeenCalled();
      expect(GoogleSignin.getTokens).toHaveBeenCalled();
    });
  });
});
