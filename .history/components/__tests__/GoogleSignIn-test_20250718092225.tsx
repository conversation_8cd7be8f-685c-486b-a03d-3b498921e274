import React from 'react';
import { Platform } from 'react-native';
import renderer from 'react-test-renderer';
import { MockedProvider } from '@apollo/client/testing';
import OnboardingScreen from '@/app/(auth)/onboarding';
import {
  SocialLoginDocument,
  SocialLoginProvider,
} from '@/graphql/generated/graphql';

// Mock the Google Sign-In library
jest.mock('@react-native-google-signin/google-signin', () => ({
  GoogleSignin: {
    configure: jest.fn(),
    hasPlayServices: jest.fn().mockResolvedValue(true),
    signIn: jest.fn().mockResolvedValue({
      user: {
        givenName: 'John',
        familyName: 'Doe',
        email: '<EMAIL>',
      },
    }),
    getTokens: jest.fn().mockResolvedValue({
      idToken: 'mock-id-token',
    }),
  },
  statusCodes: {
    SIGN_IN_CANCELLED: 'SIGN_IN_CANCELLED',
    IN_PROGRESS: 'IN_PROGRESS',
    PLAY_SERVICES_NOT_AVAILABLE: 'PLAY_SERVICES_NOT_AVAILABLE',
  },
}));

// Mock other dependencies
jest.mock('expo-router', () => ({
  router: {
    replace: jest.fn(),
  },
}));

jest.mock('@/store/auth', () => ({
  useAuthStore: () => ({
    setUser: jest.fn(),
    setToken: jest.fn(),
    setStatusCode: jest.fn(),
    simulateDevLogin: jest.fn(),
  }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock('@/services', () => ({
  deviceService: {
    isOldDevice: () => true,
  },
}));

const mockSocialLoginMutation = {
  request: {
    query: SocialLoginDocument,
    variables: {
      provider: SocialLoginProvider.Google,
      token: 'mock-id-token',
      firstName: 'John',
      lastName: 'Doe',
    },
  },
  result: {
    data: {
      socialLogin: {
        token: 'mock-auth-token',
        user: {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          status: null,
          fcm_token: null,
          created_at: null,
          creator: null,
          invitations: {
            info: {
              remaining: 5,
              total: 5,
            },
          },
        },
        status_code: 'SUCCESS',
      },
    },
  },
};

describe('Google Sign-In', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render OnboardingScreen with Google Sign-In on Android', () => {
    // Mock Platform.OS to be Android
    Platform.OS = 'android';

    const tree = renderer
      .create(
        <MockedProvider mocks={[mockSocialLoginMutation]} addTypename={false}>
          <OnboardingScreen />
        </MockedProvider>
      )
      .toJSON();

    expect(tree).toBeTruthy();
    // The component should render without errors
  });

  it('should render OnboardingScreen with Apple Sign-In on iOS', () => {
    // Mock Platform.OS to be iOS
    Platform.OS = 'ios';

    const tree = renderer
      .create(
        <MockedProvider mocks={[mockSocialLoginMutation]} addTypename={false}>
          <OnboardingScreen />
        </MockedProvider>
      )
      .toJSON();

    expect(tree).toBeTruthy();
    // The component should render without errors
  });

  it('should configure Google Sign-In on component mount', () => {
    Platform.OS = 'android';

    renderer.create(
      <MockedProvider mocks={[mockSocialLoginMutation]} addTypename={false}>
        <OnboardingScreen />
      </MockedProvider>
    );

    // Verify that Google Sign-In was configured
    const {
      GoogleSignin,
    } = require('@react-native-google-signin/google-signin');
    expect(GoogleSignin.configure).toHaveBeenCalledWith({
      webClientId:
        '630298916337-8mk1vcousdg4vj6v60bdprp9oa1vf71s.apps.googleusercontent.com',
      offlineAccess: true,
    });
  });
});
