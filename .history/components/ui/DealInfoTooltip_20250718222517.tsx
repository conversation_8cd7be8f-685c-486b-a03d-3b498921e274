import React from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { Text } from './Text';
import { PlatformIcon } from './PlatformIcon';
import { Tooltip } from './Tooltip';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import { Tag } from '@/graphql/generated/graphql';

interface DealInfoTooltipProps {
  serviceTypes?: (Tag | null)[] | null;
  maxSaving?: number | null;
  reuseLimitDays?: number | null;
  iconColor?: string;
}

export function DealInfoTooltip({
  serviceTypes,
  maxSaving,
  reuseLimitDays,
  iconColor = colors.base[100],
}: DealInfoTooltipProps) {
  const { t } = useTranslation();

  const renderTextWithBold = (text: string, boldValue: string | number) => {
    const parts = text.split('{{value}}');
    return (
      <>
        {parts[0]}
        <Text style={styles.boldText}>{boldValue}</Text>
        {parts[1]}
      </>
    );
  };

  // Get Dine-in and Takeaway service types
  const relevantServiceTypes = serviceTypes?.filter(
    (type) => type?.title === 'Dine-in' || type?.title === 'Takeaway'
  );

  // Create a combined service types display for tooltip
  const renderServiceTypeItem = () => {
    if (!relevantServiceTypes || relevantServiceTypes.length === 0) {
      return null;
    }

    // If there's only one service type, show it with its specific icon
    if (relevantServiceTypes.length === 1 && relevantServiceTypes[0]) {
      const serviceType = relevantServiceTypes[0];
      return (
        <View style={styles.tooltipItem}>
          <PlatformIcon
            name={
              serviceType.title === 'Dine-in'
                ? 'fork.knife'
                : 'takeoutbag.and.cup.and.straw'
            }
            size={moderateScale(24)}
            color={colors.primary[950]}
            style={styles.tooltipIcon}
          />
          <View style={styles.tooltipText}>
            <Text variant='body'>
              {renderTextWithBold(
                t('deals.deal.infoTooltip.serviceType').replace(
                  '{{serviceType}}',
                  '{{value}}'
                ),
                serviceType.title
              )}
            </Text>
          </View>
        </View>
      );
    }

    // If there are multiple service types, show them on one line with a star icon
    const serviceTypeNames = relevantServiceTypes
      .filter(Boolean)
      .map((type) => type!.title)
      .join(', ');

    return (
      <View style={styles.tooltipItem}>
        <PlatformIcon
          name='star'
          size={moderateScale(24)}
          color={colors.primary[950]}
          style={styles.tooltipIcon}
        />
        <View style={styles.tooltipText}>
          <Text variant='body'>
            {renderTextWithBold(
              t('deals.deal.infoTooltip.serviceTypeAll').replace(
                '{{serviceType}}',
                '{{value}}'
              ),
              serviceTypeNames
            )}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <Tooltip
      content={
        <View>
          <Text variant='h2' style={styles.tooltipTitle}>
            {t('deals.deal.infoTooltip.title')}
          </Text>
          <View style={styles.tooltipList}>
            {renderServiceTypeItem()}
            {maxSaving && (
              <View style={styles.tooltipItem}>
                <Image
                  source={require('@/assets/images/dirham.avif')}
                  style={styles.currencyIcon}
                  resizeMode='contain'
                  tintColor={colors.primary[950]}
                />
                <View style={styles.tooltipText}>
                  <Text variant='body'>
                    {renderTextWithBold(
                      t('deals.deal.infoTooltip.maxSaving').replace(
                        '{{maxSaving}}',
                        '{{value}}'
                      ),
                      maxSaving
                    )}
                  </Text>
                </View>
              </View>
            )}
            {reuseLimitDays && (
              <View style={styles.tooltipItem}>
                <PlatformIcon
                  name='hourglass'
                  size={moderateScale(24)}
                  color={colors.primary[950]}
                  style={styles.tooltipIcon}
                />
                <View style={styles.tooltipText}>
                  <Text variant='body'>
                    {renderTextWithBold(
                      t('deals.deal.infoTooltip.reuseLimitDays').replace(
                        '{{reuseLimitDays}}',
                        '{{value}}'
                      ),
                      reuseLimitDays
                    )}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>
      }
    >
      <PlatformIcon
        name='info.circle'
        size={moderateScale(20)}
        color={iconColor}
      />
    </Tooltip>
  );
}

const styles = StyleSheet.create({
  tooltipTitle: {
    marginBottom: moderateScale(20),
  },
  tooltipList: {
    gap: moderateScale(20),
  },
  tooltipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
  },
  tooltipIcon: {
    alignSelf: 'center',
  },
  tooltipText: {
    flex: 1,
  },
  boldText: {
    fontWeight: '700',
  },
  currencyIcon: {
    width: moderateScale(22),
    height: moderateScale(22),
  },
});
