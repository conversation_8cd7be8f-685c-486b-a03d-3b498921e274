import React, { forwardRef, useState, useCallback } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  TextInput,
  Alert,
  Linking,
} from 'react-native';
import { Modalize } from 'react-native-modalize';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Text } from './Text';
import { PlatformIcon } from './PlatformIcon';
import * as ImagePicker from 'expo-image-picker';
import { useTranslation } from 'react-i18next';
import { Modal } from './Modal';
import { Button } from './Button';
import { Video, ResizeMode } from 'expo-av';
import {
  useGetReelUploadUrlMutation,
  useCreateReelMutation,
} from '@/graphql/generated/graphql';
import { showErrorToast } from '@/utils/Toast';
import * as FileSystem from 'expo-file-system';
import { Image } from 'expo-image';
import * as Haptics from 'expo-haptics';

interface Props {
  partnerLocationId?: string;
}

interface UploadProgress {
  percentage: number;
  timeRemaining: number; // in seconds
  status: 'uploading' | 'processing';
}

export const UploadReelModal = forwardRef<Modalize, Props>(
  function UploadReelModal({ partnerLocationId }, ref) {
    const { t } = useTranslation();
    const [videoUri, setVideoUri] = useState<string | null>(null);
    const [caption, setCaption] = useState('');
    const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(
      null
    );
    const [isUploading, setIsUploading] = useState(false);
    const [showSuccess, setShowSuccess] = useState(false);

    const [getReelUploadUrl] = useGetReelUploadUrlMutation();
    const [createReel] = useCreateReelMutation();

    const showPermissionAlert = useCallback(() => {
      Alert.alert(
        t('uploadReel.permissionAlert.title'),
        t('uploadReel.permissionAlert.message'),
        [
          {
            text: t('uploadReel.permissionAlert.cancel'),
            style: 'cancel',
          },
          {
            text: t('uploadReel.permissionAlert.openSettings'),
            onPress: () => {
              Linking.openSettings();
            },
          },
        ]
      );
    }, [t]);

    const handleBrowseVideo = async () => {
      try {
        const { status } =
          await ImagePicker.requestMediaLibraryPermissionsAsync();

        if (status !== 'granted') {
          showPermissionAlert();
          return;
        }

        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Videos,
          allowsEditing: true,
          aspect: [9, 16], // Standard Reels aspect ratio
          quality: 1,
        });

        if (!result.canceled) {
          // Light haptic feedback when video is selected
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          setVideoUri(result.assets[0].uri);
          // Don't initialize progress here - only when upload starts
        }
      } catch (error) {
        console.error('Error picking video:', error);
        showErrorToast(t('common.error'));
      }
    };

    const handleRemoveVideo = () => {
      // Light haptic feedback when removing video
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setVideoUri(null);
      setUploadProgress(null);
    };

    const uploadVideoToS3 = async (
      presignedUrl: string,
      videoUri: string
    ): Promise<void> => {
      const startTime = Date.now();

      // Initialize progress when upload actually starts
      setUploadProgress({
        percentage: 0,
        timeRemaining: 0,
        status: 'uploading',
      });

      try {
        const uploadTask = FileSystem.createUploadTask(
          presignedUrl,
          videoUri,
          {
            httpMethod: 'PUT',
            headers: {
              'Content-Type': 'video/mp4',
            },
            uploadType: FileSystem.FileSystemUploadType.BINARY_CONTENT,
          },
          (progress) => {
            const progressPercent =
              (progress.totalBytesSent / progress.totalBytesExpectedToSend) *
              100;
            const elapsed = (Date.now() - startTime) / 1000;
            const estimatedTotal = elapsed / (progressPercent / 100);
            const timeRemaining = Math.max(0, estimatedTotal - elapsed);

            setUploadProgress((prev) =>
              prev
                ? {
                    ...prev,
                    percentage: Math.round(progressPercent),
                    timeRemaining,
                    status: 'uploading',
                  }
                : null
            );
          }
        );

        const result = await uploadTask.uploadAsync();

        if (result && result.status === 200) {
          setUploadProgress((prev) =>
            prev
              ? {
                  ...prev,
                  percentage: 100,
                  timeRemaining: 0,
                  status: 'processing',
                }
              : null
          );
        } else {
          throw new Error(
            `Upload failed with status ${result?.status || 'unknown'}`
          );
        }
      } catch (error) {
        console.error('Upload error:', error);
        throw error;
      }
    };

    const handlePostReel = async () => {
      if (!videoUri) return;

      // Medium haptic feedback when starting upload
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      setIsUploading(true);

      try {
        // Step 1: Get presigned URL
        const filename = `reel_${Date.now()}.mp4`;
        const { data: uploadUrlData } = await getReelUploadUrl({
          variables: {
            input: {
              filename,
              contentType: 'video/mp4',
            },
          },
        });

        if (!uploadUrlData?.getReelUploadUrl) {
          throw new Error('Failed to get upload URL');
        }

        const { url: presignedUrl, path } = uploadUrlData.getReelUploadUrl;

        // Step 2: Upload video to S3
        await uploadVideoToS3(presignedUrl, videoUri);

        // Step 3: Create reel
        await createReel({
          variables: {
            input: {
              url: path,
              caption: caption || undefined,
              partner_location_id: partnerLocationId || undefined,
            },
          },
        });

        // Success haptic feedback when upload completes
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // Show success screen when everything is complete
        setShowSuccess(true);

        // Don't call callback here - wait for user to close manually
      } catch (error) {
        console.error('Error uploading reel:', error);
        // Error haptic feedback when upload fails
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        showErrorToast(t('uploadReel.error.message'));
      } finally {
        setIsUploading(false);
      }
    };

    const handleClose = () => {
      // Light haptic feedback when closing modal
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Reset all states when closing
      setVideoUri(null);
      setCaption('');
      setUploadProgress(null);
      setShowSuccess(false);

      if (ref && 'current' in ref && ref.current) {
        ref.current.close();
      }
    };

    const getStatusText = () => {
      if (!uploadProgress) return '';

      switch (uploadProgress.status) {
        case 'uploading':
          return t('uploadReel.progress.uploading');
        case 'processing':
          return t('uploadReel.progress.processing');
        default:
          return '';
      }
    };

    const getProgressText = () => {
      if (!uploadProgress) return '';

      if (uploadProgress.status === 'processing') {
        return t('uploadReel.progress.finalizing');
      }

      const timeText =
        uploadProgress.timeRemaining > 0
          ? t('uploadReel.progress.timeRemaining', {
              seconds: Math.round(uploadProgress.timeRemaining),
            })
          : '';

      return `${uploadProgress.percentage}%${timeText ? ` • ${timeText}` : ''}`;
    };

    return (
      <Modal ref={ref}>
        <View style={styles.content}>
          {showSuccess ? (
            // Success Screen
            <View style={styles.successContainer}>
              <Image
                source={require('@/assets/images/upload-reel-success.png')}
                style={styles.successImage}
                contentFit='contain'
              />
              <Text variant='h1' style={styles.successTitle} align='center'>
                {t('uploadReel.success.title')}
              </Text>
              <Text
                variant='caption'
                style={styles.successSubtitle}
                align='center'
                color={colors.darkGrey[700]}
              >
                {t('uploadReel.success.subtitle')}
              </Text>
              <Button
                variant='primary'
                title={t('common.done')}
                onPress={handleClose}
              />
            </View>
          ) : (
            // Upload Form
            <>
              <Text variant='h2' style={styles.title} align='center'>
                {t('uploadReel.title')}
              </Text>
              <Text
                variant='caption1'
                style={styles.subtitle}
                align='center'
                color={colors.darkGrey[700]}
              >
                {t('uploadReel.subtitle')}
              </Text>

              <TouchableOpacity
                style={styles.browseContainer}
                onPress={handleBrowseVideo}
                disabled={isUploading}
              >
                {videoUri ? (
                  <View style={styles.videoPreview}>
                    <Video
                      source={{ uri: videoUri }}
                      style={styles.video}
                      resizeMode={ResizeMode.COVER}
                    />
                    <View style={styles.playButtonOverlay}>
                      <PlatformIcon
                        name='play.fill'
                        size={moderateScale(40)}
                        color={colors.base[100]}
                      />
                    </View>
                    <TouchableOpacity
                      style={styles.removeButtonVideo}
                      onPress={handleRemoveVideo}
                      disabled={isUploading}
                    >
                      <PlatformIcon
                        name='xmark'
                        size={12}
                        color={colors.base[100]}
                      />
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View style={styles.uploadPlaceholder}>
                    <PlatformIcon
                      name='document.badge.arrow.up'
                      size={moderateScale(40)}
                      color={colors.primary[950]}
                    />
                    <Text variant='caption' color={colors.darkGrey[700]}>
                      {t('uploadReel.browseText')}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>

              {uploadProgress && (
                <View style={styles.progressContainer}>
                  <View style={styles.progressHeader}>
                    <Text
                      variant='caption1'
                      color={colors.darkGrey[700]}
                      weight='bold'
                    >
                      {getStatusText()}
                    </Text>
                  </View>
                  <Text
                    variant='caption'
                    color={colors.darkGrey[500]}
                    style={styles.progressText}
                  >
                    {getProgressText()}
                  </Text>
                  <View style={styles.progressBarContainer}>
                    <View
                      style={[
                        styles.progressBar,
                        { width: `${uploadProgress.percentage}%` },
                      ]}
                    />
                  </View>
                </View>
              )}

              <TextInput
                style={styles.captionInput}
                placeholder={t('uploadReel.captionPlaceholder')}
                placeholderTextColor={colors.darkGrey[500]}
                multiline
                value={caption}
                onChangeText={setCaption}
                editable={!isUploading}
              />

              <View style={styles.buttonContainer}>
                <Button
                  variant='primary'
                  title={t('uploadReel.postButton')}
                  onPress={handlePostReel}
                  disabled={!videoUri || isUploading}
                  loading={isUploading}
                  style={styles.button}
                />
                <Button
                  variant='secondary'
                  title={t('common.cancel')}
                  onPress={handleClose}
                  disabled={isUploading}
                  style={styles.button}
                />
              </View>
            </>
          )}
        </View>
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  content: {
    paddingHorizontal: moderateScale(16),
    alignItems: 'center',
    marginTop: verticalScale(16),
  },
  title: {
    marginBottom: verticalScale(8),
  },
  subtitle: {
    marginBottom: verticalScale(24),
  },
  browseContainer: {
    width: '100%',
    height: verticalScale(175),
    borderWidth: 1,
    borderColor: colors.primary[950],
    borderStyle: 'dashed',
    borderRadius: moderateScale(16),
    marginBottom: verticalScale(16),
  },
  uploadPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: verticalScale(8),
  },
  videoPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: moderateScale(16),
    overflow: 'hidden',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  playButtonOverlay: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.blackTransparent.black04,
    width: '100%',
    height: '100%',
  },
  progressContainer: {
    width: moderateScale(345),
    height: verticalScale(82),
    borderRadius: moderateScale(16),
    padding: moderateScale(16),
    marginBottom: verticalScale(16),
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.softGrey[950],
  },
  progressHeader: {
    flexDirection: 'row',
    // justifyContent: 'flex-end',
    alignItems: 'center',
  },
  progressText: {
    marginTop: verticalScale(4),
    // textAlign: 'right',
  },
  progressBarContainer: {
    height: verticalScale(8),
    backgroundColor: colors.softGrey[900],
    borderRadius: moderateScale(4),
    overflow: 'hidden',
    marginTop: verticalScale(8),
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.primary[950],
    borderRadius: moderateScale(2),
  },
  captionInput: {
    width: '100%',
    height: verticalScale(150),
    backgroundColor: colors.softGrey[400],
    borderRadius: moderateScale(16),
    padding: moderateScale(16),
    marginBottom: verticalScale(24),
    textAlignVertical: 'top',
    color: colors.darkGrey[950],
  },
  buttonContainer: {
    width: '100%',
    gap: verticalScale(12),
  },
  button: {
    width: '100%',
  },
  removeButtonVideo: {
    position: 'absolute',
    top: verticalScale(10),
    right: verticalScale(10),
    width: moderateScale(20),
    height: moderateScale(20),
    borderRadius: moderateScale(10),
    backgroundColor: colors.info.red,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  successImage: {
    width: moderateScale(350),
    height: moderateScale(360),
    marginBottom: verticalScale(16),
  },
  successTitle: {
    marginBottom: verticalScale(16),
    paddingHorizontal: verticalScale(40),
  },
  successSubtitle: {
    paddingHorizontal: verticalScale(20),
    marginBottom: verticalScale(64),
  },
});
