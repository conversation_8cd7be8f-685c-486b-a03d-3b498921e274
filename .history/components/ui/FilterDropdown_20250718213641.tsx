import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { PlatformIcon } from './PlatformIcon';

type FilterType = 'Restaurants' | 'Creators';

interface Props {
  selectedFilter: FilterType;
  isOpen: boolean;
  onToggle: () => void;
  onSelect: (filter: FilterType) => void;
}

export function FilterDropdown({
  selectedFilter,
  isOpen,
  onToggle,
  onSelect,
}: Props) {
  const handleSelect = (filter: FilterType) => {
    onSelect(filter);
  };

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          onToggle();
        }}
        style={styles.filterButton}
      >
        <Text variant='h2' color={colors.base[100]}>
          {selectedFilter}
        </Text>
        <PlatformIcon
          name={isOpen ? 'chevron.up' : 'chevron.down'}
          size={moderateScale(20)}
          color={colors.base[100]}
          style={styles.filterIcon}
        />
      </TouchableOpacity>

      {isOpen && (
        <View style={styles.dropdown}>
          {(['Restaurants', 'Creators'] as FilterType[]).map(
            (filter, index) => (
              <View key={index}>
                <TouchableOpacity
                  style={[
                    styles.dropdownItem,
                    selectedFilter === filter && styles.selectedItem,
                  ]}
                  activeOpacity={0.7}
                  onPress={() => {
                    handleSelect(filter);
                  }}
                >
                  <PlatformIcon
                    name={
                      filter === 'Restaurants'
                        ? 'storefront.fill'
                        : 'person.2.fill'
                    }
                    size={moderateScale(filter === 'Restaurants' ? 22 : 18)}
                    color={colors.base[100]}
                  />
                  <Text
                    variant={selectedFilter === filter ? 'h3' : 'body'}
                    color={colors.base[100]}
                  >
                    {filter}
                  </Text>
                </TouchableOpacity>
                {index === 0 && <View style={styles.separator}></View>}
              </View>
            )
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: moderateScale(8),
    paddingHorizontal: moderateScale(12),
  },
  filterIcon: {
    marginLeft: moderateScale(10),
  },
  dropdown: {
    position: 'relative',
    left: moderateScale(8),
    top: moderateScale(-5),
    backgroundColor: colors.blackTransparent.black05,
    borderRadius: moderateScale(16),
    minWidth: moderateScale(150),
    zIndex: 1000,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
    paddingVertical: moderateScale(12),
    paddingHorizontal: moderateScale(12),
  },
  selectedItem: {
    // Add any styles for the selected item if needed
  },
  separator: {
    height: 1,
    backgroundColor: colors.base[100],
    width: '85%',
    alignSelf: 'center',
    opacity: 0.3,
  },
});
