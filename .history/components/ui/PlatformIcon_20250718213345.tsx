import React from 'react';
import { Platform } from 'react-native';
import { IconSymbol } from './IconSymbol';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';

// Mapping of iOS IconSymbol names to Android equivalent icons
const iconMapping: Record<string, { library: 'MaterialIcons' | 'Ionicons'; name: string }> = {
  // Search icons
  'magnifyingglass': { library: 'MaterialIcons', name: 'search' },
  
  // Navigation icons
  'xmark': { library: 'MaterialIcons', name: 'close' },
  'chevron.left': { library: 'MaterialIcons', name: 'chevron-left' },
  'chevron.right': { library: 'MaterialIcons', name: 'chevron-right' },
  'chevron.up': { library: 'MaterialIcons', name: 'keyboard-arrow-up' },
  'chevron.down': { library: 'MaterialIcons', name: 'keyboard-arrow-down' },
  
  // Business/Place icons
  'storefront': { library: 'MaterialIcons', name: 'store' },
  'storefront.fill': { library: 'MaterialIcons', name: 'store' },
  'building.2': { library: 'MaterialIcons', name: 'business' },
  'mappin': { library: 'MaterialIcons', name: 'location-on' },
  'mappin.and.ellipse': { library: 'MaterialIcons', name: 'location-on' },
  'map': { library: 'MaterialIcons', name: 'map' },
  
  // People icons
  'person': { library: 'MaterialIcons', name: 'person' },
  'person.circle': { library: 'MaterialIcons', name: 'account-circle' },
  'person.2': { library: 'MaterialIcons', name: 'people' },
  
  // Media icons
  'play': { library: 'MaterialIcons', name: 'play-arrow' },
  'play.fill': { library: 'MaterialIcons', name: 'play-arrow' },
  'pause': { library: 'MaterialIcons', name: 'pause' },
  'pause.fill': { library: 'MaterialIcons', name: 'pause' },
  'speaker.wave.2.fill': { library: 'MaterialIcons', name: 'volume-up' },
  'speaker.slash.fill': { library: 'MaterialIcons', name: 'volume-off' },
  'camera': { library: 'MaterialIcons', name: 'camera-alt' },
  'photo': { library: 'MaterialIcons', name: 'photo' },
  'video': { library: 'MaterialIcons', name: 'videocam' },
  
  // Communication icons
  'message': { library: 'MaterialIcons', name: 'message' },
  'phone': { library: 'MaterialIcons', name: 'phone' },
  'envelope': { library: 'MaterialIcons', name: 'email' },
  
  // Action icons
  'plus': { library: 'MaterialIcons', name: 'add' },
  'minus': { library: 'MaterialIcons', name: 'remove' },
  'heart': { library: 'MaterialIcons', name: 'favorite-border' },
  'heart.fill': { library: 'MaterialIcons', name: 'favorite' },
  'star': { library: 'MaterialIcons', name: 'star-border' },
  'star.fill': { library: 'MaterialIcons', name: 'star' },
  'bookmark': { library: 'MaterialIcons', name: 'bookmark-border' },
  'bookmark.fill': { library: 'MaterialIcons', name: 'bookmark' },
  'share': { library: 'MaterialIcons', name: 'share' },
  
  // Settings/Control icons
  'gearshape': { library: 'MaterialIcons', name: 'settings' },
  'slider.horizontal.3': { library: 'MaterialIcons', name: 'tune' },
  'ellipsis': { library: 'MaterialIcons', name: 'more-horiz' },
  'ellipsis.vertical': { library: 'MaterialIcons', name: 'more-vert' },
  
  // Status icons
  'checkmark': { library: 'MaterialIcons', name: 'check' },
  'checkmark.circle': { library: 'MaterialIcons', name: 'check-circle' },
  'exclamationmark.triangle': { library: 'MaterialIcons', name: 'warning' },
  'info.circle': { library: 'MaterialIcons', name: 'info' },
  
  // Time/Calendar icons
  'clock': { library: 'MaterialIcons', name: 'access-time' },
  'calendar': { library: 'MaterialIcons', name: 'event' },
  
  // Food/Restaurant icons
  'fork.knife': { library: 'MaterialIcons', name: 'restaurant' },
  'cup.and.saucer': { library: 'MaterialIcons', name: 'local-cafe' },
  
  // Filter/Sort icons
  'line.3.horizontal.decrease': { library: 'MaterialIcons', name: 'filter-list' },
  'arrow.up.arrow.down': { library: 'MaterialIcons', name: 'sort' },
  
  // Common UI icons
  'house': { library: 'MaterialIcons', name: 'home' },
  'list.bullet': { library: 'MaterialIcons', name: 'list' },
  'grid': { library: 'MaterialIcons', name: 'grid-view' },
  'eye': { library: 'MaterialIcons', name: 'visibility' },
  'eye.slash': { library: 'MaterialIcons', name: 'visibility-off' },
  
  // Ionicons alternatives for some icons
  'arrow.left': { library: 'Ionicons', name: 'arrow-back' },
  'arrow.right': { library: 'Ionicons', name: 'arrow-forward' },
  'arrow.up': { library: 'Ionicons', name: 'arrow-up' },
  'arrow.down': { library: 'Ionicons', name: 'arrow-down' },
};

interface PlatformIconProps {
  name: string;
  color: string;
  size: number;
  style?: any;
}

export const PlatformIcon: React.FC<PlatformIconProps> = ({
  name,
  color,
  size,
  style,
}) => {
  if (Platform.OS === 'ios') {
    // On iOS, use the original IconSymbol
    return (
      <IconSymbol
        name={name as any}
        color={color}
        size={size}
        style={style}
      />
    );
  }

  // On Android, use the mapped icon
  const mapping = iconMapping[name];
  
  if (!mapping) {
    console.warn(`PlatformIcon: No Android mapping found for "${name}". Using MaterialIcons search as fallback.`);
    return (
      <MaterialIcons
        name="help"
        color={color}
        size={size}
        style={style}
      />
    );
  }

  // Render the appropriate Android icon
  if (mapping.library === 'MaterialIcons') {
    return (
      <MaterialIcons
        name={mapping.name as any}
        color={color}
        size={size}
        style={style}
      />
    );
  } else {
    return (
      <Ionicons
        name={mapping.name as any}
        color={color}
        size={size}
        style={style}
      />
    );
  }
};
