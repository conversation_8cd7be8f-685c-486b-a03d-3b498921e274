import { colors } from '@/constants/Colors';
import {
  StyleSheet,
  View,
  StyleProp,
  ViewStyle,
  Image,
  ImageSourcePropType,
} from 'react-native';
import { Text } from './Text';
import { moderateScale } from '@/utils/scaling';
import { PlatformIcon } from './PlatformIcon';
interface Props {
  icon?: IconSymbolName;
  iconColor?: string;
  label?: string;
  customStyle?: StyleProp<ViewStyle>;
  variant?: 'light' | 'dark' | 'primary' | 'secondary' | 'transparent';
  image?: ImageSourcePropType;
}

export function Tag({
  icon,
  label,
  customStyle,
  variant = 'light',
  iconColor = variant === 'light' ? colors.base[950] : colors.base[100],
  image,
}: Props) {
  if (!label) return null;
  return (
    <View style={[styles.container, customStyle, styles[variant]]}>
      {icon && <IconSymbol name={icon} size={16} color={iconColor} />}
      {image && (
        <Image
          source={image}
          style={[styles.image]}
          resizeMode='contain'
          tintColor={iconColor}
        />
      )}
      <Text
        variant='caption'
        color={variant === 'light' ? colors.base[950] : colors.base[100]}
      >
        {label}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateScale(4),
    borderRadius: moderateScale(20),
    gap: moderateScale(4),
  },
  light: {
    backgroundColor: colors.softGrey[400],
  },
  dark: {
    backgroundColor: colors.darkGrey[900],
  },
  primary: {
    backgroundColor: colors.primary[950],
  },
  secondary: {
    backgroundColor: colors.secondary[500],
  },
  transparent: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
  image: {
    width: moderateScale(14),
    height: moderateScale(14),
  },
});
