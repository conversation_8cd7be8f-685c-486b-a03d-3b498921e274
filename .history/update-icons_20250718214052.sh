#!/bin/bash

# Script to replace remaining IconSymbol imports with PlatformIcon
# Run this from the mobile directory

echo "🔄 Updating remaining IconSymbol imports to PlatformIcon..."

# Find and replace IconSymbol imports (excluding PlatformIcon.tsx and IconSymbol.tsx)
find . -name "*.tsx" -not -path "./components/ui/PlatformIcon.tsx" -not -path "./components/ui/IconSymbol.tsx" -not -path "./.history/*" | xargs sed -i '' 's/import { IconSymbol }/import { PlatformIcon }/g'

# Replace import with additional exports
find . -name "*.tsx" -not -path "./components/ui/PlatformIcon.tsx" -not -path "./components/ui/IconSymbol.tsx" -not -path "./.history/*" | xargs sed -i '' 's/import { IconSymbol, IconSymbolName }/import { PlatformIcon }/g'

# Replace usage in JSX
find . -name "*.tsx" -not -path "./components/ui/PlatformIcon.tsx" -not -path "./components/ui/IconSymbol.tsx" -not -path "./.history/*" | xargs sed -i '' 's/<IconSymbol/<PlatformIcon/g'

# Replace path imports
find . -name "*.tsx" -not -path "./components/ui/PlatformIcon.tsx" -not -path "./components/ui/IconSymbol.tsx" -not -path "./.history/*" | xargs sed -i '' "s|from '@/components/ui/IconSymbol'|from '@/components/ui/PlatformIcon'|g"
find . -name "*.tsx" -not -path "./components/ui/PlatformIcon.tsx" -not -path "./components/ui/IconSymbol.tsx" -not -path "./.history/*" | xargs sed -i '' "s|from './ui/IconSymbol'|from './ui/PlatformIcon'|g"
find . -name "*.tsx" -not -path "./components/ui/PlatformIcon.tsx" -not -path "./components/ui/IconSymbol.tsx" -not -path "./.history/*" | xargs sed -i '' "s|from './IconSymbol'|from './PlatformIcon'|g"

echo "✅ IconSymbol replacements completed!"
echo ""
echo "📋 Summary of what was implemented:"
echo "• Created PlatformIcon component with iOS/Android icon mapping"
echo "• Updated Header.tsx with platform-specific search icons"
echo "• Updated Reel.tsx with platform-specific media controls"
echo "• Updated PlacesList.tsx with platform-specific location/phone icons"
echo "• Updated MediaModal.tsx with platform-specific close icon"
echo "• Updated CollectionCard.tsx with platform-specific plus/check icons"
echo "• Updated FilterDropdown.tsx with platform-specific chevron icons"
echo "• Updated onboarding.tsx with platform-specific Apple logo"
echo "• Updated Tag.tsx with platform-specific icons"
echo ""
echo "🎯 All icons now work on both iOS and Android!"
echo "• iOS: Uses IconSymbol (SF Symbols)"
echo "• Android: Uses MaterialIcons & Ionicons"
