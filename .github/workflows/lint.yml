name: Linting

on:
  pull_request:

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Type check
        run: npm run typecheck

      - name: Lint check
        run: npm run lint

      - name: Prettier check
        run: npm run format:check

      - name: Run tests
        run: npm test -- --watchAll=false
