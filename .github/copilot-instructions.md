# Conari Mobile App - Repository Custom Instructions

## Project Context

Conari is a React Native mobile app that connects users with restaurants and venues to discover, book, and redeem special deals. The app features a social-like discovery experience with reels, place profiles, and deal management capabilities.

## Core Development Guidelines

When working on this codebase, always follow these principles:

- Use TypeScript for all new code with proper type definitions
- Follow React Native and Expo best practices for mobile development
- Implement responsive design using our scaling utilities (`moderateScale`, `verticalScale`)
- Maintain multilingual support with English and Arabic translations for all user-facing text
- Use our established component library and design system from `/components/ui`
- Follow our GraphQL-first approach for data fetching with Apollo Client
- Write unit tests for all new functionality before submitting PRs

## Technology Stack & Conventions

Use these technologies and follow these conventions when generating code:

- **React Native with Expo SDK** - Use Expo Router for file-based navigation
- **TypeScript** - Always use TypeScript with proper type definitions
- **Apollo Client** - Use for all GraphQL operations and data fetching
- **Zustand** - Use for global state management (auth, settings)
- **React Native Reanimated** - Use for animations, not Animated API
- **i18next** - Always add both English and Arabic translations
- **Sentry** - Use for error reporting, never console.log in production
- **Algolia** - Use for search functionality with InstantSearch components
- **Branch.io** - Use for deep linking and attribution
- **Jest** - Write unit tests for all new functionality

## Code Organization & File Structure

Follow these patterns when creating or modifying files:

- **Components**: Place UI components in `/components/ui`, feature components in respective directories
- **Screens**: Use Expo Router file-based routing in `/app` directory
- **GraphQL**: Define operations in `/graphql` directory with proper TypeScript types
- **Translations**: Add keys to both `/i18n/locales/en.ts` and `/i18n/locales/ar.ts`
- **Services**: Create reusable services in `/services` directory and export from index
- **Styling**: Use `/constants/Colors.ts` for colors and scaling utilities from `/utils/scaling.ts`

## Coding Standards & Best Practices

When writing code, always:

- Use optional chaining (`?.`) for accessing nested object properties
- Implement memoization with `useMemo` and `useCallback` for performance
- Design components with flexible props and standalone mode support
- Ensure proper error handling with try-catch blocks and error reporting
- Validate data with proper schema validation (Zod schemas in `/schemas`)
- Test gesture sensitivity and animations across different devices

## Domain Models & Data Structures

When working with data, understand these key models:

- **Places**: Restaurants/venues with properties like name, address, ratings, images, deals, reels
- **Deals**: Special offers with types (TwoForOne, PercentOff) and booking/redemption flows
- **Users**: Authentication state, saved places, booked/redeemed deals
- **Reels**: Video content associated with places or creators

## Common Tasks & Patterns

For frequent development tasks, follow these patterns:

- **Adding new screens**: Use Expo Router conventions in `/app` directory
- **Creating components**: Start with `/components/ui` for reusable UI, feature directories for specific use
- **API integration**: Define GraphQL operations, generate types, use Apollo Client hooks
- **Styling**: Use StyleSheet.create at component bottom, apply responsive scaling
- **Error handling**: Use errorReportingService, set user context, include relevant error details
- **Testing**: Write unit tests for components, hooks, and services before PR submission

## Naming Conventions

Follow these naming patterns:

- **Files**: PascalCase for components (`Button.tsx`), camelCase for utilities (`scaling.ts`)
- **Components**: PascalCase (`<Button />`)
- **Functions**: camelCase (`handleSubmit`)
- **GraphQL Operations**: Noun-based queries (`placeQuery`), verb-based mutations (`loginMutation`)
- **Translation Keys**: Descriptive, hierarchical keys (`screens.home.title`)

## Quality Standards & Review Requirements

Before submitting code, ensure:

- **Documentation**: Add JSDoc comments for new functions, components, and schema fields
- **Testing**: Write unit tests for all new functionality and verify coverage
- **Translations**: Add both English and Arabic translations for new user-facing text
- **Performance**: Use memoization for expensive operations and verify animation performance
- **Error Handling**: Implement proper error reporting with user context
- **Code Organization**: Update service exports, follow file structure, clean imports
- **Responsive Design**: Ensure components work across screen sizes and platforms

## Common Pitfalls to Avoid

Never do these things:

- Hardcode values that should be configurable
- Break existing animations when refactoring
- Ignore test coverage for new functionality
- Submit PRs without addressing automated review feedback
- Forget to update translations for new features
- Add accessibility properties like accessibilityLabel, accessible, or accessibilityRole unless explicitly requested

## Git Conventions

Follow Conventional Commits specification (https://www.conventionalcommits.org/) for all commits and PRs:

### Commit Message Format

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Required Elements

- **Type**: Use these standard types:

  - `feat`: New feature for the user
  - `fix`: Bug fix for the user
  - `docs`: Documentation changes
  - `style`: Code style changes (formatting, missing semicolons, etc.)
  - `refactor`: Code refactoring without changing functionality
  - `perf`: Performance improvements
  - `test`: Adding or updating tests
  - `build`: Changes to build system or dependencies
  - `ci`: Changes to CI configuration
  - `chore`: Other changes that don't modify src or test files

- **Scope**: Use Jira ticket key (e.g., `CN-200`) or component name
- **Description**: Imperative mood, present tense, lowercase, no period at end

### Examples

- `feat(CN-200): add cravings filter to places search`
- `fix(CN-273): resolve null reference in reel component`
- `docs(CN-285): update API documentation for deals endpoint`
- `refactor(components): extract reusable modal component`
- `test(CN-201): add unit tests for filter hooks`

### Breaking Changes

- Add `!` after type/scope: `feat(CN-200)!: remove deprecated API endpoint`
- Include `BREAKING CHANGE:` in footer with description

### Branch Names

- Use Jira ticket keys: `CN-200`, `CN-273`
- For features without tickets: `feat/component-name` or `fix/issue-description`

### PR Requirements

- Title must follow conventional commit format
- Include ticket number in title and description
- Address all automated review feedback before requesting review
- Self-review code and test changes before submitting

Our team uses Jira for work tracking with CN- prefixed ticket numbers.
