---
mode: 'agent'
tools: ['codebase-retrieval']
---

# Add Translations

Your goal is to add internationalization support for new text content in both English and Arabic.

## Requirements

Ask for translation details if not provided:

- Text content to be translated
- Context where the text will be used (screen, component, error message)
- Any specific tone or style requirements
- Whether text includes variables or formatting

## Implementation Standards

### File Locations

- English translations: `/i18n/locales/en.ts`
- Arabic translations: `/i18n/locales/ar.ts`
- Follow existing hierarchical structure

### Key Naming Conventions

- Use descriptive, hierarchical keys: `screens.home.title`
- Group related translations: `errors.network.timeout`
- Use camelCase for nested objects: `buttons.saveAndContinue`
- Be specific about context: `modals.confirmDelete.title`

### Translation Guidelines

#### English

- Use clear, concise language
- Follow app's tone (friendly, professional)
- Use proper capitalization for titles and buttons
- Consider character limits for UI elements

#### Arabic

- Provide accurate, contextually appropriate translations
- Consider RTL (Right-to-Left) text flow
- Use appropriate Arabic typography
- Maintain consistent terminology across the app

### Example Structure

```typescript
// en.ts
export default {
  screens: {
    home: {
      title: 'Discover Places',
      searchPlaceholder: 'Search restaurants and venues...',
      filters: {
        cuisine: 'Cuisine',
        distance: 'Distance',
        rating: 'Rating',
      },
    },
    deals: {
      title: 'Special Deals',
      bookButton: 'Book Now',
      redeemButton: 'Redeem Deal',
    },
  },
  errors: {
    network: {
      timeout: 'Connection timeout. Please try again.',
      offline: 'No internet connection',
    },
    validation: {
      required: 'This field is required',
      email: 'Please enter a valid email',
    },
  },
};

// ar.ts
export default {
  screens: {
    home: {
      title: 'اكتشف الأماكن',
      searchPlaceholder: 'ابحث عن المطاعم والأماكن...',
      filters: {
        cuisine: 'المطبخ',
        distance: 'المسافة',
        rating: 'التقييم',
      },
    },
    deals: {
      title: 'عروض خاصة',
      bookButton: 'احجز الآن',
      redeemButton: 'استخدم العرض',
    },
  },
  errors: {
    network: {
      timeout: 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.',
      offline: 'لا يوجد اتصال بالإنترنت',
    },
    validation: {
      required: 'هذا الحقل مطلوب',
      email: 'يرجى إدخال بريد إلكتروني صحيح',
    },
  },
};
```

### Usage in Components

```typescript
import { useTranslation } from 'react-i18next';

const HomeScreen = () => {
  const { t } = useTranslation();

  return (
    <View>
      <Text>{t('screens.home.title')}</Text>
      <TextInput
        placeholder={t('screens.home.searchPlaceholder')}
      />
    </View>
  );
};
```

### Variables and Interpolation

```typescript
// Translation with variables
const translations = {
  welcome: 'Welcome back, {{name}}!',
  itemCount: 'You have {{count}} items',
  timeRemaining: 'Deal expires in {{hours}} hours',
};

// Usage
t('welcome', { name: user.name });
t('itemCount', { count: items.length });
```

## Quality Standards

- Ensure translations are contextually accurate
- Test with actual UI to verify text fits properly
- Consider cultural appropriateness for Arabic audience
- Maintain consistency with existing translations
- Verify RTL layout works correctly with Arabic text

## Testing Requirements

- Test both English and Arabic versions
- Verify text doesn't overflow UI elements
- Check RTL layout and text direction
- Test with different text lengths
- Ensure proper font rendering for Arabic text
