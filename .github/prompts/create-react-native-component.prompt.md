---
mode: 'agent'
tools: ['codebase-retrieval']
---

# Create React Native Component

Your goal is to create a new React Native component following the project's established patterns and conventions.

## Requirements

Ask for the component details if not provided:

- Component name and purpose
- Props interface requirements
- Whether it's a UI component or feature-specific component
- Any specific styling or animation requirements

## Implementation Standards

### Component Structure

- Use TypeScript with proper interface definitions
- Follow PascalCase naming convention
- Place UI components in `/components/ui`, feature components in respective directories
- Export component as default and include named export for props interface

### Styling Requirements

- Use `StyleSheet.create` at the bottom of the component
- Import colors from `/constants/Colors.ts`
- Use scaling utilities from `/utils/scaling.ts` (`moderateScale`, `verticalScale`, `horizontalScale`)
- Implement responsive design for different screen sizes
- Follow consistent spacing patterns

### Code Quality Standards

- Add JSDoc comments for the component and its props
- Use optional chaining (`?.`) for nested object access
- Implement proper TypeScript types for all props
- Use `useMemo` and `useCallback` for performance optimization when needed
- Handle loading and error states appropriately

### Integration Requirements

- Use existing design system components when possible
- Follow established animation patterns with React Native Reanimated
- Implement proper error handling with Sentry
- Add internationalization support if component includes text
- Ensure component works on both iOS and Android
- Consider haptic feedback for interactive elements
- Test with different screen sizes and orientations

## Example Structure

```typescript
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Colors } from '@/constants/Colors';

interface ComponentNameProps {
  // Define props here
}

/**
 * Component description
 * @param props - Component props
 */
const ComponentName: React.FC<ComponentNameProps> = ({ ...props }) => {
  // Component implementation

  return (
    <View style={styles.container}>
      {/* Component content */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // Styles here
  },
});

export default ComponentName;
export type { ComponentNameProps };
```

## Testing Requirements

- Create unit tests for the component
- Test different prop combinations
- Verify accessibility features
- Test on both platforms if platform-specific code is used
