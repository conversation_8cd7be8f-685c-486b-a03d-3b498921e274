---
mode: 'agent'
tools: ['github']
---

# Fix PR Review Comments

Your goal is to address all review comments and feedback on a pull request.

## Process

1. **Fetch PR Details**: Get the pull request comments, review feedback, and CI/CD status from GitHub MCP
2. **Analyze Feedback**: Review all comments including:
   - Human reviewer comments and change requests
   - Automated review feedback (Copilot, CI/CD)
   - Failed checks or tests
3. **Implement Fixes**: Address each comment systematically:
   - Code changes for functionality issues
   - Add missing tests for new features
   - Update documentation and JSDoc comments
   - Fix linting and formatting issues
   - Add missing translations (English and Arabic)
   - Implement proper error handling with Sentry
4. **Verify Changes**: Ensure all fixes align with project standards:
   - Follow TypeScript best practices
   - Use proper React Native patterns
   - Maintain responsive design with scaling utilities
   - Follow conventional commit format
5. **Commit and Push**: Create commits following conventional commit format with appropriate scope (CN-XXX ticket numbers)

## Quality Checklist

- [ ] All reviewer comments addressed
- [ ] Tests added for new functionality
- [ ] Translations added for new text
- [ ] Error handling implemented properly
- [ ] Code follows project conventions
- [ ] No console.log statements in production code
- [ ] Proper memoization for performance
- [ ] Responsive design considerations included
