---
mode: 'agent'
tools: ['codebase-retrieval']
---

# Add GraphQL Operation

Your goal is to create a new GraphQL query, mutation, or subscription following the project's GraphQL patterns.

## Requirements

Ask for operation details if not provided:

- Operation type (query, mutation, subscription)
- Operation name and purpose
- Required fields and parameters
- Related data models (Place, Deal, User, Reel)

## Implementation Standards

### File Organization

- Place queries in `/graphql/queries/`
- Place mutations in `/graphql/mutations/`
- Place fragments in `/graphql/fragments/`
- Use descriptive filenames (e.g., `place.graphql`, `deal.graphql`)

### Naming Conventions

- **Queries**: Noun-based names (`placeQuery`, `dealsQuery`)
- **Mutations**: Verb-based names (`loginMutation`, `bookDealMutation`)
- **Fragments**: Descriptive names (`PlaceFragment`, `DealDetailsFragment`)

### GraphQL Best Practices

- Use fragments for reusable field sets
- Include proper error handling fields
- Request only necessary fields to optimize performance
- Use variables for dynamic parameters
- Include pagination fields when applicable

### TypeScript Integration

- Ensure GraphQL operations generate proper TypeScript types
- Use generated types in React components and hooks
- Follow Apollo Client patterns for data fetching

## Example Structures

### Query Example

```graphql
query GetPlaceDetails($id: ID!) {
  place(id: $id) {
    ...PlaceFragment
    deals {
      ...DealFragment
    }
    reels {
      ...ReelFragment
    }
  }
}
```

### Mutation Example

```graphql
mutation BookDeal($dealId: ID!, $userId: ID!) {
  bookDeal(dealId: $dealId, userId: $userId) {
    success
    message
    booking {
      id
      status
      bookedAt
    }
    errors {
      field
      message
    }
  }
}
```

### Fragment Example

```graphql
fragment PlaceFragment on Place {
  id
  name
  address
  rating
  images {
    url
    alt
  }
  openingHours {
    day
    open
    close
  }
}
```

## Apollo Client Integration

### Using in Components

```typescript
import { useQuery, useMutation } from '@apollo/client';
import { GET_PLACE_DETAILS } from '@/graphql/queries/place';

const PlaceScreen = ({ placeId }: { placeId: string }) => {
  const { data, loading, error } = useQuery(GET_PLACE_DETAILS, {
    variables: { id: placeId },
  });

  if (loading) return <LoadingComponent />;
  if (error) return <ErrorComponent error={error} />;

  return <PlaceDetails place={data.place} />;
};
```

## Error Handling Requirements

- Include error fields in mutation responses
- Handle network errors with proper user feedback
- Use Sentry for error reporting
- Implement retry logic for failed operations

## Testing Requirements

- Test GraphQL operations with mock data
- Verify error handling scenarios
- Test loading states and edge cases
- Ensure proper TypeScript type safety
