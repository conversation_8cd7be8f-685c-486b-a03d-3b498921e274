---
mode: 'agent'
tools: ['codebase-retrieval']
---

# Create Expo Router Screen

Your goal is to create a new screen using Expo Router file-based routing following the project's navigation patterns.

## Requirements

Ask for screen details if not provided:

- Screen name and purpose
- Route parameters (if dynamic route)
- Navigation structure (tab, stack, modal)
- Required data fetching or state management
- Authentication requirements

## Implementation Standards

### File Structure & Routing

- Place screens in `/app` directory following Expo Router conventions
- Use `(tabs)` for tab navigation screens
- Use `(auth)` for authentication screens
- Use `[id]` for dynamic routes with parameters
- Use `_layout.tsx` for nested navigation layouts

### Screen Component Structure

```typescript
import React from 'react';
import { StyleSheet } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Screen } from '@/components/ui/Screen';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Colors } from '@/constants/Colors';

export default function ScreenName() {
  const router = useRouter();
  const params = useLocalSearchParams();

  return (
    <Screen style={styles.container}>
      {/* Screen content */}
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
});
```

### Navigation Patterns

#### Tab Screen Example (`/app/(tabs)/home.tsx`)

```typescript
import { Tabs } from 'expo-router';

export default function TabLayout() {
  return (
    <Tabs>
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => <HomeIcon color={color} />,
        }}
      />
    </Tabs>
  );
}
```

#### Dynamic Route Example (`/app/places/[id].tsx`)

```typescript
import { useLocalSearchParams } from 'expo-router';

export default function PlaceDetails() {
  const { id } = useLocalSearchParams<{ id: string }>();

  // Use the id parameter for data fetching
  const { data, loading } = useQuery(GET_PLACE_DETAILS, {
    variables: { id },
  });

  return (
    <Screen>
      {/* Place details content */}
    </Screen>
  );
}
```

### Data Fetching Integration

- Use Apollo Client hooks for GraphQL operations
- Implement proper loading and error states
- Handle authentication requirements
- Use Zustand for global state when needed

### Screen Standards

- Use the `Screen` component wrapper for consistent layout
- Implement proper header configuration
- Add loading states for async operations
- Handle error states with user-friendly messages
- Include proper TypeScript types for route parameters

### Authentication Integration

```typescript
import { useAuthStore } from '@/store/auth';
import { Redirect } from 'expo-router';

export default function ProtectedScreen() {
  const { isAuthenticated } = useAuthStore();

  if (!isAuthenticated) {
    return <Redirect href="/login" />;
  }

  return (
    <Screen>
      {/* Protected content */}
    </Screen>
  );
}
```

### Internationalization

- Use `useTranslation` hook for all text content
- Add translation keys for screen titles and content
- Support RTL layout for Arabic language

### Performance Considerations

- Use `useMemo` for expensive calculations
- Implement proper list virtualization for long lists
- Optimize images and media loading
- Use React Native Reanimated for smooth animations

## Common Screen Types

### List Screen Pattern

```typescript
export default function PlacesList() {
  const { data, loading, fetchMore } = useQuery(GET_PLACES);

  const renderPlace = useCallback(({ item }) => (
    <PlaceCard place={item} />
  ), []);

  return (
    <Screen>
      <FlatList
        data={data?.places}
        renderItem={renderPlace}
        onEndReached={fetchMore}
        // ... other props
      />
    </Screen>
  );
}
```

### Form Screen Pattern

```typescript
export default function CreateDeal() {
  const [formData, setFormData] = useState({});
  const [createDeal] = useMutation(CREATE_DEAL);

  const handleSubmit = async () => {
    try {
      await createDeal({ variables: formData });
      router.back();
    } catch (error) {
      // Handle error
    }
  };

  return (
    <Screen>
      {/* Form components */}
    </Screen>
  );
}
```

## Testing Requirements

- Test navigation flows
- Verify route parameters handling
- Test authentication redirects
- Ensure proper error handling
- Test on both iOS and Android
