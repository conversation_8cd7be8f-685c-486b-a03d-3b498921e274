---
mode: 'agent'
tools: ['codebase-retrieval']
---

# Write Unit Tests

Your goal is to create comprehensive unit tests for React Native components, hooks, and utility functions.

## Requirements

Ask for testing details if not provided:

- Component, hook, or function to test
- Specific functionality to cover
- Edge cases and error scenarios
- Mock requirements for external dependencies

## Testing Standards

### Test File Organization

- Place test files adjacent to source files with `.test.ts` or `.test.tsx` extension
- Use descriptive test file names matching the source file
- Group related tests in `describe` blocks
- Use clear, descriptive test names

### Testing Framework Setup

```typescript
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { MockedProvider } from '@apollo/client/testing';
import { jest } from '@jest/globals';

// Import component under test
import ComponentName from './ComponentName';
```

### Component Testing Patterns

#### Basic Component Test

```typescript
describe('ComponentName', () => {
  it('renders correctly with required props', () => {
    const { getByText } = render(
      <ComponentName title="Test Title" />
    );

    expect(getByText('Test Title')).toBeTruthy();
  });

  it('handles user interactions', () => {
    const mockOnPress = jest.fn();
    const { getByRole } = render(
      <ComponentName onPress={mockOnPress} />
    );

    fireEvent.press(getByRole('button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
});
```

#### Testing with Apollo Client

```typescript
const mocks = [
  {
    request: {
      query: GET_PLACES,
      variables: { limit: 10 },
    },
    result: {
      data: {
        places: [
          { id: '1', name: 'Test Place' }
        ],
      },
    },
  },
];

describe('PlacesList', () => {
  it('displays places from GraphQL query', async () => {
    const { getByText } = render(
      <MockedProvider mocks={mocks}>
        <PlacesList />
      </MockedProvider>
    );

    await waitFor(() => {
      expect(getByText('Test Place')).toBeTruthy();
    });
  });
});
```

### Hook Testing Patterns

```typescript
import { renderHook, act } from '@testing-library/react-native';
import { useCustomHook } from './useCustomHook';

describe('useCustomHook', () => {
  it('returns initial state correctly', () => {
    const { result } = renderHook(() => useCustomHook());

    expect(result.current.value).toBe(initialValue);
    expect(result.current.loading).toBe(false);
  });

  it('updates state when action is called', () => {
    const { result } = renderHook(() => useCustomHook());

    act(() => {
      result.current.updateValue('new value');
    });

    expect(result.current.value).toBe('new value');
  });
});
```

### Utility Function Testing

```typescript
import { formatCurrency, validateEmail } from './utils';

describe('formatCurrency', () => {
  it('formats positive numbers correctly', () => {
    expect(formatCurrency(1234.56)).toBe('$1,234.56');
  });

  it('handles zero values', () => {
    expect(formatCurrency(0)).toBe('$0.00');
  });

  it('handles negative values', () => {
    expect(formatCurrency(-100)).toBe('-$100.00');
  });
});

describe('validateEmail', () => {
  it('validates correct email formats', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('<EMAIL>')).toBe(true);
  });

  it('rejects invalid email formats', () => {
    expect(validateEmail('invalid-email')).toBe(false);
    expect(validateEmail('test@')).toBe(false);
    expect(validateEmail('')).toBe(false);
  });
});
```

### Mocking External Dependencies

#### Expo Router

```typescript
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  }),
  useLocalSearchParams: () => ({ id: 'test-id' }),
}));
```

#### Zustand Store

```typescript
import { useAuthStore } from '@/store/auth';

jest.mock('@/store/auth');
const mockUseAuthStore = useAuthStore as jest.MockedFunction<
  typeof useAuthStore
>;

beforeEach(() => {
  mockUseAuthStore.mockReturnValue({
    isAuthenticated: true,
    user: { id: '1', name: 'Test User' },
    login: jest.fn(),
    logout: jest.fn(),
  });
});
```

### Testing Error Scenarios

```typescript
describe('ErrorHandling', () => {
  it('handles network errors gracefully', async () => {
    const errorMock = {
      request: { query: GET_PLACES },
      error: new Error('Network error'),
    };

    const { getByText } = render(
      <MockedProvider mocks={[errorMock]}>
        <PlacesList />
      </MockedProvider>
    );

    await waitFor(() => {
      expect(getByText(/error/i)).toBeTruthy();
    });
  });
});
```

### Async Testing Patterns

```typescript
describe('AsyncComponent', () => {
  it('shows loading state initially', () => {
    const { getByTestId } = render(<AsyncComponent />);
    expect(getByTestId('loading-indicator')).toBeTruthy();
  });

  it('shows content after loading', async () => {
    const { getByText, queryByTestId } = render(<AsyncComponent />);

    await waitFor(() => {
      expect(queryByTestId('loading-indicator')).toBeNull();
      expect(getByText('Content loaded')).toBeTruthy();
    });
  });
});
```

## Coverage Requirements

- Aim for 80%+ code coverage
- Test all public methods and props
- Cover error scenarios and edge cases
- Test user interactions and state changes
- Verify responsive design and cross-platform compatibility

## Best Practices

- Write tests before or alongside implementation
- Use descriptive test names that explain the expected behavior
- Keep tests focused and independent
- Mock external dependencies appropriately
- Test behavior, not implementation details
- Use proper cleanup in tests with side effects
