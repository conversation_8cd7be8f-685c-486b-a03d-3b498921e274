module.exports = {
  semi: true, // Use semicolons at the end of statements
  singleQuote: true, // Use single quotes instead of double quotes
  tabWidth: 2, // Use 2 spaces for indentation
  trailingComma: 'es5', // Add trailing commas where valid in ES5 (objects, arrays, etc.)
  bracketSpacing: true, // Add spaces between brackets and objects: { foo: bar }
  arrowParens: 'always', // Always include parentheses around arrow function arguments: (x) => x
  printWidth: 80, // Wrap code at 80 characters for better readability
  endOfLine: 'lf', // Use Unix-style line endings (LF)
  jsxSingleQuote: true, // Use single quotes in JSX
  quoteProps: 'as-needed', // Only add quotes around object properties when required
  useTabs: false, // Use spaces instead of tabs
};
