{"workbench.colorCustomizations": {"activityBar.activeBackground": "#a098f7", "activityBar.background": "#a098f7", "activityBar.foreground": "#15202b", "activityBar.inactiveForeground": "#15202b99", "activityBarBadge.background": "#fde4e1", "activityBarBadge.foreground": "#15202b", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#a098f7", "statusBar.background": "#7569f3", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#a098f7", "statusBarItem.remoteBackground": "#7569f3", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#7569f3", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#7569f399", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.color": "#7569F3", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always", "source.fixAll.format": "always"}, "editor.trimAutoWhitespace": true, "prettier.configPath": ".prettierrc.js", "prettier.requireConfig": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "i18n-ally.localesPaths": ["i18n", "i18n/locales"], "cSpell.words": ["algoliasearch", "codegen", "<PERSON><PERSON>", "instantsearch", "libphonenumber", "modalize", "portalize", "ptom<PERSON><PERSON>s", "reactotron", "trivago"]}