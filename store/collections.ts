import { create } from 'zustand';

interface CollectionsState {
  // Track selected collections for the current item being managed
  selectedCollections: { [collectionId: string]: boolean };
  // Track newly created collection ID for auto-selection
  newlyCreatedCollectionId: string | null;

  // Actions
  setSelectedCollections: (selections: {
    [collectionId: string]: boolean;
  }) => void;
  toggleCollection: (collectionId: string, isSelected: boolean) => void;
  setNewlyCreatedCollection: (collectionId: string) => void;
  clearNewlyCreatedCollection: () => void;
  resetSelections: () => void;
}

export const useCollectionsStore = create<CollectionsState>((set, get) => ({
  selectedCollections: {},
  newlyCreatedCollectionId: null,

  setSelectedCollections: (selections) => {
    set({ selectedCollections: selections });
  },

  toggleCollection: (collectionId, isSelected) => {
    set((state) => ({
      selectedCollections: {
        ...state.selectedCollections,
        [collectionId]: isSelected,
      },
    }));
  },

  setNewlyCreatedCollection: (collectionId) => {
    set({ newlyCreatedCollectionId: collectionId });
  },

  clearNewlyCreatedCollection: () => {
    set({ newlyCreatedCollectionId: null });
  },

  resetSelections: () => {
    set({ selectedCollections: {}, newlyCreatedCollectionId: null });
  },
}));
