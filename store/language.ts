import { create } from 'zustand';
import i18n from '@/i18n';

interface LanguageState {
  language: 'en' | 'ar';
  setLanguage: (language: 'en' | 'ar') => void;
  toggleLanguage: () => void;
}

export const useLanguageStore = create<LanguageState>((set) => ({
  language: 'en',
  setLanguage: (language) => {
    i18n.changeLanguage(language);
    set({ language });
  },
  toggleLanguage: () => {
    set((state) => {
      const newLanguage = state.language === 'en' ? 'ar' : 'en';
      i18n.changeLanguage(newLanguage);
      return { language: newLanguage };
    });
  },
}));
