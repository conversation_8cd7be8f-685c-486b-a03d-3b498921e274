import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
    };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
  Date: { input: any; output: any };
  DateTime: { input: any; output: any };
};

export type AcceptInvitationResponse = {
  __typename?: 'AcceptInvitationResponse';
  statusCode: Scalars['Int']['output'];
  user: User;
};

export type AddItemToCollectionInput = {
  collectable_id: Scalars['ID']['input'];
  collectable_type?: InputMaybe<CollectableType>;
  collection_ids?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type Admin = {
  __typename?: 'Admin';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type CanUploadReel = Admin | Creator;

export type CancelDealInput = {
  myDealId: Scalars['ID']['input'];
};

export type CancelDealResponse = {
  __typename?: 'CancelDealResponse';
  message: Scalars['String']['output'];
};

export type Collectable = Creator | PartnerPlace | Reel;

export enum CollectableType {
  Creator = 'CREATOR',
  PartnerLocation = 'PARTNER_LOCATION',
  Reel = 'REEL',
}

export type Collection = {
  __typename?: 'Collection';
  created_at: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  is_item_exists?: Maybe<Scalars['Boolean']['output']>;
  items: Array<Maybe<CollectionItem>>;
  myRole: CollectionUserRoleEnum;
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  user: User;
};

export type CollectionIs_Item_ExistsArgs = {
  collectable_id?: InputMaybe<Scalars['ID']['input']>;
  collectable_type?: InputMaybe<CollectableType>;
};

export type CollectionItem = {
  __typename?: 'CollectionItem';
  collectable: Collectable;
  collectable_id: Scalars['ID']['output'];
  collectable_type: Scalars['String']['output'];
  collection: Collection;
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export enum CollectionUserRoleEnum {
  Bookmark = 'BOOKMARK',
  Collaborator = 'COLLABORATOR',
  False = 'FALSE',
  Owner = 'OWNER',
}

export type CreateCollectionInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  title: Scalars['String']['input'];
};

export type CreateReelInput = {
  caption?: InputMaybe<Scalars['String']['input']>;
  partner_location_id?: InputMaybe<Scalars['ID']['input']>;
  thumbnail?: InputMaybe<Scalars['String']['input']>;
  url: Scalars['String']['input'];
};

export type CreateReelResponse = {
  __typename?: 'CreateReelResponse';
  reel: Reel;
};

export type Creator = {
  __typename?: 'Creator';
  avatar?: Maybe<Media>;
  bio?: Maybe<Scalars['String']['output']>;
  collaborations?: Maybe<Scalars['Int']['output']>;
  facebook_url?: Maybe<Scalars['String']['output']>;
  first_name: Scalars['String']['output'];
  followers: UserPaginator;
  formatted_name: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  instagram_url?: Maybe<Scalars['String']['output']>;
  is_followed?: Maybe<Scalars['Boolean']['output']>;
  last_name?: Maybe<Scalars['String']['output']>;
  likes: LikePaginator;
  /** @deprecated Use formattedName instead */
  name: Scalars['String']['output'];
  places: PartnerPlacePaginator;
  places_count?: Maybe<Scalars['Int']['output']>;
  reels: ReelPaginator;
  tiktok_url?: Maybe<Scalars['String']['output']>;
  twitter_url?: Maybe<Scalars['String']['output']>;
  user: User;
  username: Scalars['String']['output'];
  website?: Maybe<Scalars['String']['output']>;
  website_url?: Maybe<Scalars['String']['output']>;
};

export type CreatorFollowersArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type CreatorLikesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type CreatorPlacesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type CreatorReelsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type CreatorFollowInput = {
  id: Scalars['ID']['input'];
};

export type CreatorFollowPayload = {
  __typename?: 'CreatorFollowPayload';
  creator: Creator;
  message: Scalars['String']['output'];
  status: Scalars['Boolean']['output'];
};

/** A paginated list of Creator items. */
export type CreatorPaginator = {
  __typename?: 'CreatorPaginator';
  /** A list of Creator items. */
  data: Array<Creator>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type Deal = {
  __typename?: 'Deal';
  available_slots: Array<DealDateTimeSlot>;
  deal_type: DealType;
  description: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  max_saving?: Maybe<Scalars['Float']['output']>;
  max_usage_per_day: Scalars['Int']['output'];
  myDeals: MyDealPaginator;
  partner_place: PartnerPlace;
  reuse_limit_days: Scalars['Int']['output'];
  service_type?: Maybe<Tag>;
  service_types: Array<Maybe<Tag>>;
  title: Scalars['String']['output'];
};

export type DealMyDealsArgs = {
  first: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<OrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type DealDateTimeSlot = {
  __typename?: 'DealDateTimeSlot';
  /** allowed per day - reserved */
  available_seats: Scalars['Int']['output'];
  /** Next 7 calender days */
  date: Scalars['String']['output'];
  slots: Array<DealTimeSlot>;
};

/** A paginated list of Deal items. */
export type DealPaginator = {
  __typename?: 'DealPaginator';
  /** A list of Deal items. */
  data: Array<Deal>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type DealSnapshot = {
  __typename?: 'DealSnapshot';
  deal_type: DealType;
  description: Scalars['String']['output'];
  max_saving?: Maybe<Scalars['Float']['output']>;
  max_usage_per_day: Scalars['Int']['output'];
  reuse_limit_days: Scalars['Int']['output'];
  service_options: Array<Tag>;
  title: Scalars['String']['output'];
};

export type DealTimeSlot = {
  __typename?: 'DealTimeSlot';
  from: Scalars['String']['output'];
  to: Scalars['String']['output'];
};

export type DealTimeSlotInput = {
  from: Scalars['String']['input'];
  to: Scalars['String']['input'];
};

export enum DealType {
  Aed_40Discount = 'AED_40_DISCOUNT',
  FreeItemWithPurchase = 'FREE_ITEM_WITH_PURCHASE',
  OneAedSpecial = 'ONE_AED_SPECIAL',
  TwoForOne = 'TWO_FOR_ONE',
}

export type EditCollectionInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  title?: InputMaybe<Scalars['String']['input']>;
};

export type GeoLocation = {
  __typename?: 'GeoLocation';
  lat?: Maybe<Scalars['Float']['output']>;
  lng?: Maybe<Scalars['Float']['output']>;
};

export type GetReelUploadUrlInput = {
  contentType: Scalars['String']['input'];
  filename: Scalars['String']['input'];
};

export type GetReelUploadUrlResponse = {
  __typename?: 'GetReelUploadUrlResponse';
  headers: Array<Header>;
  path: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type Header = {
  __typename?: 'Header';
  key: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type Invitation = {
  __typename?: 'Invitation';
  created_at: Scalars['DateTime']['output'];
  email: Scalars['String']['output'];
  expires_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  invitee?: Maybe<User>;
  invitee_user_id?: Maybe<Scalars['ID']['output']>;
  inviter?: Maybe<User>;
  inviter_user_id?: Maybe<Scalars['ID']['output']>;
  is_used: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  provider: Scalars['String']['output'];
  provider_id: Scalars['String']['output'];
  status: InvitationStatus;
  token?: Maybe<Scalars['String']['output']>;
  token_expires_at?: Maybe<Scalars['DateTime']['output']>;
  updated_at: Scalars['DateTime']['output'];
};

export type InvitationInfo = {
  __typename?: 'InvitationInfo';
  remaining: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export enum InvitationStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED',
}

export type Likable = Creator | PartnerPlace | Reel;

export enum LikableType {
  Creator = 'CREATOR',
  PartnerPlace = 'PARTNER_PLACE',
  Reel = 'REEL',
}

export type Like = {
  __typename?: 'Like';
  id: Scalars['ID']['output'];
  likeable: Likable;
  user: User;
};

export type LikeInput = {
  id: Scalars['ID']['input'];
  type: LikableType;
};

/** A paginated list of Like items. */
export type LikePaginator = {
  __typename?: 'LikePaginator';
  /** A list of Like items. */
  data: Array<Like>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type LikePayload = {
  __typename?: 'LikePayload';
  likeable?: Maybe<Likable>;
  message: Scalars['String']['output'];
  status: Scalars['Boolean']['output'];
};

export enum LoginStatusCode {
  AwaitingApproval = 'AWAITING_APPROVAL',
  ExpiredInvitation = 'EXPIRED_INVITATION',
  NewUser = 'NEW_USER',
  PendingInvitation = 'PENDING_INVITATION',
  Success = 'SUCCESS',
}

export type LogoutResponse = {
  __typename?: 'LogoutResponse';
  status: Scalars['Boolean']['output'];
};

export type Media = {
  __typename?: 'Media';
  collection_name?: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  full_url?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  order?: Maybe<Scalars['Int']['output']>;
  updated_at: Scalars['DateTime']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  acceptInvitation: AcceptInvitationResponse;
  addFCMToken: User;
  addItemToCollection: Array<Collection>;
  cancelDeal: CancelDealResponse;
  clearFCMToken: User;
  createCollection: Collection;
  createReel: CreateReelResponse;
  deleteCollection: Scalars['Boolean']['output'];
  editCollection: Collection;
  followCreator: CreatorFollowPayload;
  followPlace: PlaceFollowPayload;
  getReelUploadUrl: GetReelUploadUrlResponse;
  inviteUser: Scalars['String']['output'];
  like: LikePayload;
  logout: LogoutResponse;
  redeemDeal: RedeemDealResponse;
  removeItemFromAllCollections: Scalars['Boolean']['output'];
  removeItemFromCollection: Scalars['Boolean']['output'];
  reserveDeal: ReserveDealResponse;
  socialLogin: SocialLoginResponse;
  updateProfile?: Maybe<UpdateProfileResponse>;
};

export type MutationAcceptInvitationArgs = {
  token: Scalars['String']['input'];
};

export type MutationAddFcmTokenArgs = {
  token: Scalars['String']['input'];
};

export type MutationAddItemToCollectionArgs = {
  input: AddItemToCollectionInput;
};

export type MutationCancelDealArgs = {
  input: CancelDealInput;
};

export type MutationCreateCollectionArgs = {
  input: CreateCollectionInput;
};

export type MutationCreateReelArgs = {
  input: CreateReelInput;
};

export type MutationDeleteCollectionArgs = {
  id: Scalars['ID']['input'];
};

export type MutationEditCollectionArgs = {
  input: EditCollectionInput;
};

export type MutationFollowCreatorArgs = {
  input: CreatorFollowInput;
};

export type MutationFollowPlaceArgs = {
  input: PlaceFollowInput;
};

export type MutationGetReelUploadUrlArgs = {
  input: GetReelUploadUrlInput;
};

export type MutationLikeArgs = {
  input: LikeInput;
};

export type MutationRedeemDealArgs = {
  input: RedeemDealInput;
};

export type MutationRemoveItemFromAllCollectionsArgs = {
  input: RemoveItemFromAllCollectionsInput;
};

export type MutationRemoveItemFromCollectionArgs = {
  input: RemoveItemFromCollectionInput;
};

export type MutationReserveDealArgs = {
  input: ReserveDealInput;
};

export type MutationSocialLoginArgs = {
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  provider: SocialLoginProvider;
  token: Scalars['String']['input'];
};

export type MutationUpdateProfileArgs = {
  input: UpdateProfileInput;
};

export type MyDeal = {
  __typename?: 'MyDeal';
  deal: Deal;
  deal_snapshot?: Maybe<DealSnapshot>;
  id: Scalars['ID']['output'];
  redeemed_at?: Maybe<Scalars['DateTime']['output']>;
  reserve_slot: MyDealDateTimeSlot;
  reserved_at?: Maybe<Scalars['DateTime']['output']>;
  reuse_after?: Maybe<Scalars['DateTime']['output']>;
  status: MyDealStatusEnum;
};

export type MyDealDateTimeSlot = {
  __typename?: 'MyDealDateTimeSlot';
  date: Scalars['String']['output'];
  slot: DealTimeSlot;
};

export type MyDealDateTimeSlotInput = {
  date: Scalars['Date']['input'];
  slot: DealTimeSlotInput;
};

/** A paginated list of MyDeal items. */
export type MyDealPaginator = {
  __typename?: 'MyDealPaginator';
  /** A list of MyDeal items. */
  data: Array<MyDeal>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export enum MyDealStatusEnum {
  NoShow = 'NO_SHOW',
  Redeemable = 'REDEEMABLE',
  Redeemed = 'REDEEMED',
  Upcoming = 'UPCOMING',
}

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT',
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM',
}

/** Information about pagination using a fully featured paginator. */
export type PaginatorInfo = {
  __typename?: 'PaginatorInfo';
  /** Number of items in the current page. */
  count: Scalars['Int']['output'];
  /** Index of the current page. */
  currentPage: Scalars['Int']['output'];
  /** Index of the first item in the current page. */
  firstItem?: Maybe<Scalars['Int']['output']>;
  /** Are there more pages after this one? */
  hasMorePages: Scalars['Boolean']['output'];
  /** Index of the last item in the current page. */
  lastItem?: Maybe<Scalars['Int']['output']>;
  /** Index of the last available page. */
  lastPage: Scalars['Int']['output'];
  /** Number of items per page. */
  perPage: Scalars['Int']['output'];
  /** Number of total available items. */
  total: Scalars['Int']['output'];
};

export type Partner = {
  __typename?: 'Partner';
  description?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  facebook?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  instagram?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  partner_places: PartnerPlacePaginator;
  twitter?: Maybe<Scalars['String']['output']>;
  website?: Maybe<Scalars['String']['output']>;
};

export type PartnerPartner_PlacesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

/** A paginated list of Partner items. */
export type PartnerPaginator = {
  __typename?: 'PartnerPaginator';
  /** A list of Partner items. */
  data: Array<Partner>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type PartnerPlace = {
  __typename?: 'PartnerPlace';
  address_line_1?: Maybe<Scalars['String']['output']>;
  address_line_2?: Maybe<Scalars['String']['output']>;
  ambiance: Array<Maybe<Tag>>;
  area?: Maybe<Tag>;
  avatar: Media;
  city?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  cravings: Array<Maybe<Tag>>;
  cuisine_types: Array<Maybe<Tag>>;
  deals: DealPaginator;
  dietary: Array<Maybe<Tag>>;
  id: Scalars['ID']['output'];
  images: Array<Media>;
  is_place_in_collection: Scalars['Boolean']['output'];
  likes: LikePaginator;
  location: GeoLocation;
  meal_times: Array<Maybe<Tag>>;
  menu?: Maybe<Array<Maybe<Media>>>;
  menu_url?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  opening_hours: Array<PartnerPlaceOpeningHours>;
  parking: Array<Maybe<Tag>>;
  partner: Partner;
  phone?: Maybe<Scalars['String']['output']>;
  postal_code?: Maybe<Scalars['String']['output']>;
  price_per_person?: Maybe<Scalars['Float']['output']>;
  rates: PartnerPlaceRate;
  reels: ReelPaginator;
  retail_destination?: Maybe<Tag>;
  saved_places_count: Scalars['Int']['output'];
  service_options: Array<Maybe<Tag>>;
  specialities: Array<Maybe<Tag>>;
  state?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Tag>>;
};

export type PartnerPlaceDealsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type PartnerPlaceLikesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type PartnerPlaceReelsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type PartnerPlaceOpeningHours = {
  __typename?: 'PartnerPlaceOpeningHours';
  day?: Maybe<Scalars['Int']['output']>;
  from?: Maybe<Scalars['DateTime']['output']>;
  to?: Maybe<Scalars['DateTime']['output']>;
};

/** A paginated list of PartnerPlace items. */
export type PartnerPlacePaginator = {
  __typename?: 'PartnerPlacePaginator';
  /** A list of PartnerPlace items. */
  data: Array<PartnerPlace>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type PartnerPlaceRate = {
  __typename?: 'PartnerPlaceRate';
  google?: Maybe<Scalars['String']['output']>;
  reviews_count?: Maybe<Scalars['Int']['output']>;
};

export type PlaceFollowInput = {
  id: Scalars['ID']['input'];
};

export type PlaceFollowPayload = {
  __typename?: 'PlaceFollowPayload';
  message: Scalars['String']['output'];
  place: PartnerPlace;
  status: Scalars['Boolean']['output'];
};

export type Query = {
  __typename?: 'Query';
  collection?: Maybe<Collection>;
  collections: Array<Collection>;
  creator: Creator;
  creators: CreatorPaginator;
  deal: Deal;
  deals: DealPaginator;
  likes: LikePaginator;
  me: User;
  /**
   * MyDeal will be cached until it updates from
   * Your side
   */
  myDeal: MyDeal;
  myDeals: MyDealPaginator;
  partner: Partner;
  partnerPlace: PartnerPlace;
  partnerPlaces: PartnerPlacePaginator;
  partners: PartnerPaginator;
  /** Get a reel by its ID. */
  reel?: Maybe<Reel>;
  /** Get a list of reels. */
  reels: ReelPaginator;
  /** Get reels By Place */
  reelsByPlace: ReelPaginator;
  /** Get reels meta statuses for multiple reels. */
  reelsMetaStatuses: Array<Reel>;
};

export type QueryCollectionArgs = {
  id: Scalars['ID']['input'];
};

export type QueryCreatorArgs = {
  id: Scalars['ID']['input'];
};

export type QueryCreatorsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type QueryDealArgs = {
  id: Scalars['ID']['input'];
};

export type QueryDealsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type QueryLikesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type QueryMyDealArgs = {
  id: Scalars['ID']['input'];
};

export type QueryMyDealsArgs = {
  first: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<OrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type QueryPartnerArgs = {
  id: Scalars['ID']['input'];
};

export type QueryPartnerPlaceArgs = {
  id: Scalars['ID']['input'];
};

export type QueryPartnerPlacesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type QueryPartnersArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type QueryReelArgs = {
  id: Scalars['ID']['input'];
};

export type QueryReelsArgs = {
  first: Scalars['Int']['input'];
  input?: InputMaybe<ReelInput>;
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type QueryReelsByPlaceArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  placeId: Scalars['ID']['input'];
};

export type QueryReelsMetaStatusesArgs = {
  ids: Array<Scalars['ID']['input']>;
};

export type RedeemDealInput = {
  myDealId: Scalars['ID']['input'];
};

export type RedeemDealResponse = {
  __typename?: 'RedeemDealResponse';
  myDeal: MyDeal;
};

export type Reel = {
  __typename?: 'Reel';
  approval_status: Scalars['String']['output'];
  caption?: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  creator?: Maybe<CanUploadReel>;
  full_url: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  is_creator_followed: Scalars['Boolean']['output'];
  is_liked: Scalars['Boolean']['output'];
  is_place_followed: Scalars['Boolean']['output'];
  is_place_in_collection: Scalars['Boolean']['output'];
  likes: LikePaginator;
  likes_count: Scalars['Int']['output'];
  places: PartnerPlacePaginator;
  saved_places_count: Scalars['Int']['output'];
  thumbnail?: Maybe<Scalars['String']['output']>;
  updated_at: Scalars['DateTime']['output'];
};

export type ReelLikesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type ReelPlacesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type ReelInput = {
  all?: InputMaybe<Scalars['Boolean']['input']>;
  by_creator?: InputMaybe<Scalars['Boolean']['input']>;
  by_place?: InputMaybe<Scalars['Boolean']['input']>;
};

/** A paginated list of Reel items. */
export type ReelPaginator = {
  __typename?: 'ReelPaginator';
  /** A list of Reel items. */
  data: Array<Reel>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type RemoveItemFromAllCollectionsInput = {
  collectable_id: Scalars['ID']['input'];
  collectable_type: CollectableType;
};

export type RemoveItemFromCollectionInput = {
  collectable_id: Scalars['ID']['input'];
  collectable_type: CollectableType;
  collection_ids: Array<Scalars['ID']['input']>;
};

export type ReserveDealInput = {
  id: Scalars['ID']['input'];
  myDealIdToRenew?: InputMaybe<Scalars['ID']['input']>;
  reserve_slot: MyDealDateTimeSlotInput;
};

export type ReserveDealResponse = {
  __typename?: 'ReserveDealResponse';
  myDeal: MyDeal;
};

export enum SocialLoginProvider {
  Apple = 'APPLE',
  Google = 'GOOGLE',
}

export type SocialLoginResponse = {
  __typename?: 'SocialLoginResponse';
  message: Scalars['String']['output'];
  status_code?: Maybe<LoginStatusCode>;
  token: Scalars['String']['output'];
  user: User;
};

/** Directions for ordering a list of records. */
export enum SortOrder {
  /** Sort records in ascending order. */
  Asc = 'ASC',
  /** Sort records in descending order. */
  Desc = 'DESC',
}

export type Tag = {
  __typename?: 'Tag';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
};

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT',
}

export type UpdateProfileInput = {
  name: Scalars['String']['input'];
};

export type UpdateProfileResponse = {
  __typename?: 'UpdateProfileResponse';
  me: User;
  message: Scalars['String']['output'];
  status: Scalars['Boolean']['output'];
};

export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at?: Maybe<Scalars['DateTime']['output']>;
  /** The creator profile associated with this user, if any */
  creator?: Maybe<Creator>;
  /** Unique email address. */
  email: Scalars['String']['output'];
  /** When the email was verified. */
  email_verified_at?: Maybe<Scalars['DateTime']['output']>;
  fcm_token?: Maybe<Scalars['String']['output']>;
  following_creators: CreatorPaginator;
  following_places: PartnerPlacePaginator;
  /** Unique primary key. */
  id: Scalars['ID']['output'];
  invitations: UserInvitations;
  myDeals: MyDealPaginator;
  /** Non-unique name. */
  name: Scalars['String']['output'];
  status?: Maybe<LoginStatusCode>;
  /** When the account was last updated. */
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type UserFollowing_CreatorsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type UserFollowing_PlacesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type UserMyDealsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type UserInvitations = {
  __typename?: 'UserInvitations';
  data: Array<Invitation>;
  info: InvitationInfo;
};

/** A paginated list of User items. */
export type UserPaginator = {
  __typename?: 'UserPaginator';
  /** A list of User items. */
  data: Array<User>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type CollectionFragment = {
  __typename?: 'Collection';
  id: string;
  title: string;
  description?: string | null;
  myRole: CollectionUserRoleEnum;
  items: Array<{
    __typename?: 'CollectionItem';
    id: string;
    collectable:
      | { __typename?: 'Creator' }
      | {
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          avatar: { __typename?: 'Media'; full_url?: string | null };
          partner: { __typename?: 'Partner'; name: string };
        }
      | { __typename?: 'Reel' };
  } | null>;
};

export type FullCollectionFragment = {
  __typename?: 'Collection';
  id: string;
  title: string;
  description?: string | null;
  myRole: CollectionUserRoleEnum;
  items: Array<{
    __typename?: 'CollectionItem';
    id: string;
    collectable:
      | { __typename?: 'Creator' }
      | {
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          price_per_person?: number | null;
          menu_url?: string | null;
          deals: {
            __typename?: 'DealPaginator';
            data: Array<{
              __typename?: 'Deal';
              id: string;
              title: string;
              description: string;
              deal_type: DealType;
              max_saving?: number | null;
              reuse_limit_days: number;
              service_types: Array<{
                __typename?: 'Tag';
                title: string;
              } | null>;
            }>;
          };
          rates: {
            __typename?: 'PartnerPlaceRate';
            google?: string | null;
            reviews_count?: number | null;
          };
          opening_hours: Array<{
            __typename?: 'PartnerPlaceOpeningHours';
            day?: number | null;
            from?: any | null;
            to?: any | null;
          }>;
          partner: {
            __typename?: 'Partner';
            id: string;
            name: string;
            description?: string | null;
            email?: string | null;
            instagram?: string | null;
            facebook?: string | null;
            twitter?: string | null;
            website?: string | null;
            partner_places: {
              __typename?: 'PartnerPlacePaginator';
              data: Array<{
                __typename?: 'PartnerPlace';
                id: string;
                name: string;
                address_line_1?: string | null;
                address_line_2?: string | null;
                phone?: string | null;
                location: {
                  __typename?: 'GeoLocation';
                  lat?: number | null;
                  lng?: number | null;
                };
              }>;
            };
          };
          tags?: Array<{
            __typename?: 'Tag';
            id: string;
            title: string;
          }> | null;
          images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
          menu?: Array<{
            __typename?: 'Media';
            full_url?: string | null;
          } | null> | null;
          location: {
            __typename?: 'GeoLocation';
            lat?: number | null;
            lng?: number | null;
          };
          avatar: { __typename?: 'Media'; full_url?: string | null };
          ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
          parking: Array<{ __typename?: 'Tag'; title: string } | null>;
          specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
          cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
          dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
          service_options: Array<{ __typename?: 'Tag'; title: string } | null>;
          meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
          cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
        }
      | { __typename?: 'Reel' };
  } | null>;
};

export type CollectionItemCheckFragment = {
  __typename?: 'Collection';
  is_item_exists?: boolean | null;
  id: string;
  title: string;
  description?: string | null;
  myRole: CollectionUserRoleEnum;
  items: Array<{
    __typename?: 'CollectionItem';
    id: string;
    collectable:
      | { __typename?: 'Creator' }
      | {
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          avatar: { __typename?: 'Media'; full_url?: string | null };
          partner: { __typename?: 'Partner'; name: string };
        }
      | { __typename?: 'Reel' };
  } | null>;
};

export type CreatorFragment = {
  __typename?: 'Creator';
  id: string;
  name: string;
  username: string;
  bio?: string | null;
  tiktok_url?: string | null;
  instagram_url?: string | null;
  is_followed?: boolean | null;
  collaborations?: number | null;
  user: { __typename?: 'User'; id: string; name: string };
  avatar?: { __typename?: 'Media'; full_url?: string | null } | null;
  followers: {
    __typename?: 'UserPaginator';
    paginatorInfo: { __typename?: 'PaginatorInfo'; total: number };
  };
  reels: {
    __typename?: 'ReelPaginator';
    paginatorInfo: { __typename?: 'PaginatorInfo'; total: number };
    data: Array<{
      __typename?: 'Reel';
      id: string;
      full_url: string;
      caption?: string | null;
      thumbnail?: string | null;
      places: {
        __typename?: 'PartnerPlacePaginator';
        data: Array<{
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          price_per_person?: number | null;
          menu_url?: string | null;
          deals: {
            __typename?: 'DealPaginator';
            data: Array<{
              __typename?: 'Deal';
              id: string;
              title: string;
              description: string;
              deal_type: DealType;
              max_saving?: number | null;
              reuse_limit_days: number;
              service_types: Array<{
                __typename?: 'Tag';
                title: string;
              } | null>;
            }>;
          };
          rates: {
            __typename?: 'PartnerPlaceRate';
            google?: string | null;
            reviews_count?: number | null;
          };
          opening_hours: Array<{
            __typename?: 'PartnerPlaceOpeningHours';
            day?: number | null;
            from?: any | null;
            to?: any | null;
          }>;
          partner: {
            __typename?: 'Partner';
            id: string;
            name: string;
            description?: string | null;
            email?: string | null;
            instagram?: string | null;
            facebook?: string | null;
            twitter?: string | null;
            website?: string | null;
            partner_places: {
              __typename?: 'PartnerPlacePaginator';
              data: Array<{
                __typename?: 'PartnerPlace';
                id: string;
                name: string;
                address_line_1?: string | null;
                address_line_2?: string | null;
                phone?: string | null;
                location: {
                  __typename?: 'GeoLocation';
                  lat?: number | null;
                  lng?: number | null;
                };
              }>;
            };
          };
          tags?: Array<{
            __typename?: 'Tag';
            id: string;
            title: string;
          }> | null;
          images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
          menu?: Array<{
            __typename?: 'Media';
            full_url?: string | null;
          } | null> | null;
          location: {
            __typename?: 'GeoLocation';
            lat?: number | null;
            lng?: number | null;
          };
          avatar: { __typename?: 'Media'; full_url?: string | null };
          ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
          parking: Array<{ __typename?: 'Tag'; title: string } | null>;
          specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
          cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
          dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
          service_options: Array<{ __typename?: 'Tag'; title: string } | null>;
          meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
          cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
        }>;
      };
    }>;
  };
};

export type DealWithOutPlacesFragment = {
  __typename?: 'Deal';
  id: string;
  title: string;
  description: string;
  deal_type: DealType;
  max_saving?: number | null;
  reuse_limit_days: number;
  service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
};

export type DealFragment = {
  __typename?: 'Deal';
  id: string;
  title: string;
  description: string;
  deal_type: DealType;
  max_saving?: number | null;
  reuse_limit_days: number;
  partner_place: {
    __typename?: 'PartnerPlace';
    id: string;
    name: string;
    price_per_person?: number | null;
    menu_url?: string | null;
    rates: {
      __typename?: 'PartnerPlaceRate';
      google?: string | null;
      reviews_count?: number | null;
    };
    opening_hours: Array<{
      __typename?: 'PartnerPlaceOpeningHours';
      day?: number | null;
      from?: any | null;
      to?: any | null;
    }>;
    partner: {
      __typename?: 'Partner';
      id: string;
      name: string;
      description?: string | null;
      email?: string | null;
      instagram?: string | null;
      facebook?: string | null;
      twitter?: string | null;
      website?: string | null;
      partner_places: {
        __typename?: 'PartnerPlacePaginator';
        data: Array<{
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          address_line_1?: string | null;
          address_line_2?: string | null;
          phone?: string | null;
          location: {
            __typename?: 'GeoLocation';
            lat?: number | null;
            lng?: number | null;
          };
        }>;
      };
    };
    tags?: Array<{ __typename?: 'Tag'; id: string; title: string }> | null;
    images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
    menu?: Array<{
      __typename?: 'Media';
      full_url?: string | null;
    } | null> | null;
    location: {
      __typename?: 'GeoLocation';
      lat?: number | null;
      lng?: number | null;
    };
    avatar: { __typename?: 'Media'; full_url?: string | null };
  };
  service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
};

export type DealWithMyDealsFragment = {
  __typename?: 'Deal';
  id: string;
  title: string;
  description: string;
  deal_type: DealType;
  max_saving?: number | null;
  reuse_limit_days: number;
  myDeals: {
    __typename?: 'MyDealPaginator';
    data: Array<{
      __typename?: 'MyDeal';
      id: string;
      status: MyDealStatusEnum;
      reserved_at?: any | null;
      redeemed_at?: any | null;
      reuse_after?: any | null;
      reserve_slot: {
        __typename?: 'MyDealDateTimeSlot';
        date: string;
        slot: { __typename?: 'DealTimeSlot'; from: string; to: string };
      };
    }>;
  };
  service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
};

export type FullDealFragment = {
  __typename?: 'Deal';
  id: string;
  title: string;
  description: string;
  deal_type: DealType;
  max_saving?: number | null;
  reuse_limit_days: number;
  available_slots: Array<{
    __typename?: 'DealDateTimeSlot';
    date: string;
    available_seats: number;
    slots: Array<{ __typename?: 'DealTimeSlot'; from: string; to: string }>;
  }>;
  partner_place: {
    __typename?: 'PartnerPlace';
    id: string;
    name: string;
    price_per_person?: number | null;
    menu_url?: string | null;
    rates: {
      __typename?: 'PartnerPlaceRate';
      google?: string | null;
      reviews_count?: number | null;
    };
    opening_hours: Array<{
      __typename?: 'PartnerPlaceOpeningHours';
      day?: number | null;
      from?: any | null;
      to?: any | null;
    }>;
    partner: {
      __typename?: 'Partner';
      id: string;
      name: string;
      description?: string | null;
      email?: string | null;
      instagram?: string | null;
      facebook?: string | null;
      twitter?: string | null;
      website?: string | null;
      partner_places: {
        __typename?: 'PartnerPlacePaginator';
        data: Array<{
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          address_line_1?: string | null;
          address_line_2?: string | null;
          phone?: string | null;
          location: {
            __typename?: 'GeoLocation';
            lat?: number | null;
            lng?: number | null;
          };
        }>;
      };
    };
    tags?: Array<{ __typename?: 'Tag'; id: string; title: string }> | null;
    images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
    menu?: Array<{
      __typename?: 'Media';
      full_url?: string | null;
    } | null> | null;
    location: {
      __typename?: 'GeoLocation';
      lat?: number | null;
      lng?: number | null;
    };
    avatar: { __typename?: 'Media'; full_url?: string | null };
  };
  service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
};

export type MyDealFragment = {
  __typename?: 'MyDeal';
  id: string;
  status: MyDealStatusEnum;
  reserved_at?: any | null;
  redeemed_at?: any | null;
  reuse_after?: any | null;
  deal: {
    __typename?: 'Deal';
    id: string;
    title: string;
    description: string;
    deal_type: DealType;
    max_saving?: number | null;
    reuse_limit_days: number;
    partner_place: {
      __typename?: 'PartnerPlace';
      id: string;
      name: string;
      price_per_person?: number | null;
      menu_url?: string | null;
      rates: {
        __typename?: 'PartnerPlaceRate';
        google?: string | null;
        reviews_count?: number | null;
      };
      opening_hours: Array<{
        __typename?: 'PartnerPlaceOpeningHours';
        day?: number | null;
        from?: any | null;
        to?: any | null;
      }>;
      partner: {
        __typename?: 'Partner';
        id: string;
        name: string;
        description?: string | null;
        email?: string | null;
        instagram?: string | null;
        facebook?: string | null;
        twitter?: string | null;
        website?: string | null;
        partner_places: {
          __typename?: 'PartnerPlacePaginator';
          data: Array<{
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            address_line_1?: string | null;
            address_line_2?: string | null;
            phone?: string | null;
            location: {
              __typename?: 'GeoLocation';
              lat?: number | null;
              lng?: number | null;
            };
          }>;
        };
      };
      tags?: Array<{ __typename?: 'Tag'; id: string; title: string }> | null;
      images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
      menu?: Array<{
        __typename?: 'Media';
        full_url?: string | null;
      } | null> | null;
      location: {
        __typename?: 'GeoLocation';
        lat?: number | null;
        lng?: number | null;
      };
      avatar: { __typename?: 'Media'; full_url?: string | null };
    };
    service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
  };
  reserve_slot: {
    __typename?: 'MyDealDateTimeSlot';
    date: string;
    slot: { __typename?: 'DealTimeSlot'; from: string; to: string };
  };
};

export type MyDealWithoutDealFragment = {
  __typename?: 'MyDeal';
  id: string;
  status: MyDealStatusEnum;
  reserved_at?: any | null;
  redeemed_at?: any | null;
  reuse_after?: any | null;
  reserve_slot: {
    __typename?: 'MyDealDateTimeSlot';
    date: string;
    slot: { __typename?: 'DealTimeSlot'; from: string; to: string };
  };
};

export type PartnerFragment = {
  __typename?: 'Partner';
  id: string;
  name: string;
  description?: string | null;
  email?: string | null;
  instagram?: string | null;
  facebook?: string | null;
  twitter?: string | null;
  website?: string | null;
  partner_places: {
    __typename?: 'PartnerPlacePaginator';
    data: Array<{
      __typename?: 'PartnerPlace';
      id: string;
      name: string;
      address_line_1?: string | null;
      address_line_2?: string | null;
      phone?: string | null;
      location: {
        __typename?: 'GeoLocation';
        lat?: number | null;
        lng?: number | null;
      };
    }>;
  };
};

export type PlaceInsidePartnerFragment = {
  __typename?: 'PartnerPlace';
  id: string;
  name: string;
  address_line_1?: string | null;
  address_line_2?: string | null;
  phone?: string | null;
  location: {
    __typename?: 'GeoLocation';
    lat?: number | null;
    lng?: number | null;
  };
};

export type PlaceFragment = {
  __typename?: 'PartnerPlace';
  id: string;
  name: string;
  price_per_person?: number | null;
  menu_url?: string | null;
  rates: {
    __typename?: 'PartnerPlaceRate';
    google?: string | null;
    reviews_count?: number | null;
  };
  opening_hours: Array<{
    __typename?: 'PartnerPlaceOpeningHours';
    day?: number | null;
    from?: any | null;
    to?: any | null;
  }>;
  partner: {
    __typename?: 'Partner';
    id: string;
    name: string;
    description?: string | null;
    email?: string | null;
    instagram?: string | null;
    facebook?: string | null;
    twitter?: string | null;
    website?: string | null;
    partner_places: {
      __typename?: 'PartnerPlacePaginator';
      data: Array<{
        __typename?: 'PartnerPlace';
        id: string;
        name: string;
        address_line_1?: string | null;
        address_line_2?: string | null;
        phone?: string | null;
        location: {
          __typename?: 'GeoLocation';
          lat?: number | null;
          lng?: number | null;
        };
      }>;
    };
  };
  tags?: Array<{ __typename?: 'Tag'; id: string; title: string }> | null;
  images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
  menu?: Array<{
    __typename?: 'Media';
    full_url?: string | null;
  } | null> | null;
  location: {
    __typename?: 'GeoLocation';
    lat?: number | null;
    lng?: number | null;
  };
  avatar: { __typename?: 'Media'; full_url?: string | null };
};

export type PlaceTagsFragment = {
  __typename?: 'PartnerPlace';
  ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
  parking: Array<{ __typename?: 'Tag'; title: string } | null>;
  specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
  cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
  dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
  service_options: Array<{ __typename?: 'Tag'; title: string } | null>;
  meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
  cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
};

export type FullPlaceFragment = {
  __typename?: 'PartnerPlace';
  is_place_in_collection: boolean;
  id: string;
  name: string;
  price_per_person?: number | null;
  menu_url?: string | null;
  deals: {
    __typename?: 'DealPaginator';
    data: Array<{
      __typename?: 'Deal';
      id: string;
      title: string;
      description: string;
      deal_type: DealType;
      max_saving?: number | null;
      reuse_limit_days: number;
      myDeals: {
        __typename?: 'MyDealPaginator';
        data: Array<{
          __typename?: 'MyDeal';
          id: string;
          status: MyDealStatusEnum;
          reserved_at?: any | null;
          redeemed_at?: any | null;
          reuse_after?: any | null;
          reserve_slot: {
            __typename?: 'MyDealDateTimeSlot';
            date: string;
            slot: { __typename?: 'DealTimeSlot'; from: string; to: string };
          };
        }>;
      };
      service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
    }>;
  };
  reels: {
    __typename?: 'ReelPaginator';
    data: Array<{
      __typename?: 'Reel';
      id: string;
      full_url: string;
      caption?: string | null;
      thumbnail?: string | null;
      places: {
        __typename?: 'PartnerPlacePaginator';
        data: Array<{
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          price_per_person?: number | null;
          menu_url?: string | null;
          deals: {
            __typename?: 'DealPaginator';
            data: Array<{
              __typename?: 'Deal';
              id: string;
              title: string;
              description: string;
              deal_type: DealType;
              max_saving?: number | null;
              reuse_limit_days: number;
              service_types: Array<{
                __typename?: 'Tag';
                title: string;
              } | null>;
            }>;
          };
          rates: {
            __typename?: 'PartnerPlaceRate';
            google?: string | null;
            reviews_count?: number | null;
          };
          opening_hours: Array<{
            __typename?: 'PartnerPlaceOpeningHours';
            day?: number | null;
            from?: any | null;
            to?: any | null;
          }>;
          partner: {
            __typename?: 'Partner';
            id: string;
            name: string;
            description?: string | null;
            email?: string | null;
            instagram?: string | null;
            facebook?: string | null;
            twitter?: string | null;
            website?: string | null;
            partner_places: {
              __typename?: 'PartnerPlacePaginator';
              data: Array<{
                __typename?: 'PartnerPlace';
                id: string;
                name: string;
                address_line_1?: string | null;
                address_line_2?: string | null;
                phone?: string | null;
                location: {
                  __typename?: 'GeoLocation';
                  lat?: number | null;
                  lng?: number | null;
                };
              }>;
            };
          };
          tags?: Array<{
            __typename?: 'Tag';
            id: string;
            title: string;
          }> | null;
          images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
          menu?: Array<{
            __typename?: 'Media';
            full_url?: string | null;
          } | null> | null;
          location: {
            __typename?: 'GeoLocation';
            lat?: number | null;
            lng?: number | null;
          };
          avatar: { __typename?: 'Media'; full_url?: string | null };
          ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
          parking: Array<{ __typename?: 'Tag'; title: string } | null>;
          specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
          cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
          dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
          service_options: Array<{ __typename?: 'Tag'; title: string } | null>;
          meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
          cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
        }>;
      };
    }>;
  };
  rates: {
    __typename?: 'PartnerPlaceRate';
    google?: string | null;
    reviews_count?: number | null;
  };
  opening_hours: Array<{
    __typename?: 'PartnerPlaceOpeningHours';
    day?: number | null;
    from?: any | null;
    to?: any | null;
  }>;
  partner: {
    __typename?: 'Partner';
    id: string;
    name: string;
    description?: string | null;
    email?: string | null;
    instagram?: string | null;
    facebook?: string | null;
    twitter?: string | null;
    website?: string | null;
    partner_places: {
      __typename?: 'PartnerPlacePaginator';
      data: Array<{
        __typename?: 'PartnerPlace';
        id: string;
        name: string;
        address_line_1?: string | null;
        address_line_2?: string | null;
        phone?: string | null;
        location: {
          __typename?: 'GeoLocation';
          lat?: number | null;
          lng?: number | null;
        };
      }>;
    };
  };
  tags?: Array<{ __typename?: 'Tag'; id: string; title: string }> | null;
  images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
  menu?: Array<{
    __typename?: 'Media';
    full_url?: string | null;
  } | null> | null;
  location: {
    __typename?: 'GeoLocation';
    lat?: number | null;
    lng?: number | null;
  };
  avatar: { __typename?: 'Media'; full_url?: string | null };
  ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
  parking: Array<{ __typename?: 'Tag'; title: string } | null>;
  specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
  cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
  dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
  service_options: Array<{ __typename?: 'Tag'; title: string } | null>;
  meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
  cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
};

export type PlaceWithDealsFragment = {
  __typename?: 'PartnerPlace';
  id: string;
  name: string;
  price_per_person?: number | null;
  menu_url?: string | null;
  deals: {
    __typename?: 'DealPaginator';
    data: Array<{
      __typename?: 'Deal';
      id: string;
      title: string;
      description: string;
      deal_type: DealType;
      max_saving?: number | null;
      reuse_limit_days: number;
      service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
    }>;
  };
  rates: {
    __typename?: 'PartnerPlaceRate';
    google?: string | null;
    reviews_count?: number | null;
  };
  opening_hours: Array<{
    __typename?: 'PartnerPlaceOpeningHours';
    day?: number | null;
    from?: any | null;
    to?: any | null;
  }>;
  partner: {
    __typename?: 'Partner';
    id: string;
    name: string;
    description?: string | null;
    email?: string | null;
    instagram?: string | null;
    facebook?: string | null;
    twitter?: string | null;
    website?: string | null;
    partner_places: {
      __typename?: 'PartnerPlacePaginator';
      data: Array<{
        __typename?: 'PartnerPlace';
        id: string;
        name: string;
        address_line_1?: string | null;
        address_line_2?: string | null;
        phone?: string | null;
        location: {
          __typename?: 'GeoLocation';
          lat?: number | null;
          lng?: number | null;
        };
      }>;
    };
  };
  tags?: Array<{ __typename?: 'Tag'; id: string; title: string }> | null;
  images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
  menu?: Array<{
    __typename?: 'Media';
    full_url?: string | null;
  } | null> | null;
  location: {
    __typename?: 'GeoLocation';
    lat?: number | null;
    lng?: number | null;
  };
  avatar: { __typename?: 'Media'; full_url?: string | null };
  ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
  parking: Array<{ __typename?: 'Tag'; title: string } | null>;
  specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
  cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
  dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
  service_options: Array<{ __typename?: 'Tag'; title: string } | null>;
  meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
  cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
};

export type MiniPlaceFragment = {
  __typename?: 'PartnerPlace';
  id: string;
  name: string;
  avatar: { __typename?: 'Media'; full_url?: string | null };
  partner: { __typename?: 'Partner'; id: string; name: string };
};

export type ReelFragment = {
  __typename?: 'Reel';
  id: string;
  full_url: string;
  caption?: string | null;
  thumbnail?: string | null;
  places: {
    __typename?: 'PartnerPlacePaginator';
    data: Array<{
      __typename?: 'PartnerPlace';
      id: string;
      name: string;
      price_per_person?: number | null;
      menu_url?: string | null;
      deals: {
        __typename?: 'DealPaginator';
        data: Array<{
          __typename?: 'Deal';
          id: string;
          title: string;
          description: string;
          deal_type: DealType;
          max_saving?: number | null;
          reuse_limit_days: number;
          service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
        }>;
      };
      rates: {
        __typename?: 'PartnerPlaceRate';
        google?: string | null;
        reviews_count?: number | null;
      };
      opening_hours: Array<{
        __typename?: 'PartnerPlaceOpeningHours';
        day?: number | null;
        from?: any | null;
        to?: any | null;
      }>;
      partner: {
        __typename?: 'Partner';
        id: string;
        name: string;
        description?: string | null;
        email?: string | null;
        instagram?: string | null;
        facebook?: string | null;
        twitter?: string | null;
        website?: string | null;
        partner_places: {
          __typename?: 'PartnerPlacePaginator';
          data: Array<{
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            address_line_1?: string | null;
            address_line_2?: string | null;
            phone?: string | null;
            location: {
              __typename?: 'GeoLocation';
              lat?: number | null;
              lng?: number | null;
            };
          }>;
        };
      };
      tags?: Array<{ __typename?: 'Tag'; id: string; title: string }> | null;
      images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
      menu?: Array<{
        __typename?: 'Media';
        full_url?: string | null;
      } | null> | null;
      location: {
        __typename?: 'GeoLocation';
        lat?: number | null;
        lng?: number | null;
      };
      avatar: { __typename?: 'Media'; full_url?: string | null };
      ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
      parking: Array<{ __typename?: 'Tag'; title: string } | null>;
      specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
      cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
      dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
      service_options: Array<{ __typename?: 'Tag'; title: string } | null>;
      meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
      cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
    }>;
  };
};

export type UserFragment = {
  __typename?: 'User';
  id: string;
  name: string;
  email: string;
  status?: LoginStatusCode | null;
  fcm_token?: string | null;
  created_at?: any | null;
  creator?: { __typename?: 'Creator'; id: string } | null;
  invitations: {
    __typename?: 'UserInvitations';
    info: { __typename?: 'InvitationInfo'; remaining: number; total: number };
  };
};

export type UserFollowedPlacesFragment = {
  __typename?: 'User';
  following_places: {
    __typename?: 'PartnerPlacePaginator';
    data: Array<{
      __typename?: 'PartnerPlace';
      id: string;
      name: string;
      avatar: { __typename?: 'Media'; full_url?: string | null };
      partner: { __typename?: 'Partner'; id: string; name: string };
    }>;
  };
};

export type CancelDealMutationVariables = Exact<{
  input: CancelDealInput;
}>;

export type CancelDealMutation = {
  __typename?: 'Mutation';
  cancelDeal: { __typename?: 'CancelDealResponse'; message: string };
};

export type CreateCollectionMutationVariables = Exact<{
  input: CreateCollectionInput;
}>;

export type CreateCollectionMutation = {
  __typename?: 'Mutation';
  createCollection: {
    __typename?: 'Collection';
    id: string;
    title: string;
    description?: string | null;
    myRole: CollectionUserRoleEnum;
    items: Array<{
      __typename?: 'CollectionItem';
      id: string;
      collectable:
        | { __typename?: 'Creator' }
        | {
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            avatar: { __typename?: 'Media'; full_url?: string | null };
            partner: { __typename?: 'Partner'; name: string };
          }
        | { __typename?: 'Reel' };
    } | null>;
  };
};

export type EditCollectionMutationVariables = Exact<{
  input: EditCollectionInput;
}>;

export type EditCollectionMutation = {
  __typename?: 'Mutation';
  editCollection: {
    __typename?: 'Collection';
    id: string;
    title: string;
    description?: string | null;
    myRole: CollectionUserRoleEnum;
    items: Array<{
      __typename?: 'CollectionItem';
      id: string;
      collectable:
        | { __typename?: 'Creator' }
        | {
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            avatar: { __typename?: 'Media'; full_url?: string | null };
            partner: { __typename?: 'Partner'; name: string };
          }
        | { __typename?: 'Reel' };
    } | null>;
  };
};

export type DeleteCollectionMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;

export type DeleteCollectionMutation = {
  __typename?: 'Mutation';
  deleteCollection: boolean;
};

export type AddItemToCollectionMutationVariables = Exact<{
  input: AddItemToCollectionInput;
}>;

export type AddItemToCollectionMutation = {
  __typename?: 'Mutation';
  addItemToCollection: Array<{
    __typename?: 'Collection';
    id: string;
    title: string;
    description?: string | null;
    myRole: CollectionUserRoleEnum;
    items: Array<{
      __typename?: 'CollectionItem';
      id: string;
      collectable:
        | { __typename?: 'Creator' }
        | {
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            avatar: { __typename?: 'Media'; full_url?: string | null };
            partner: { __typename?: 'Partner'; name: string };
          }
        | { __typename?: 'Reel' };
    } | null>;
  }>;
};

export type RemoveItemFromCollectionMutationVariables = Exact<{
  input: RemoveItemFromCollectionInput;
}>;

export type RemoveItemFromCollectionMutation = {
  __typename?: 'Mutation';
  removeItemFromCollection: boolean;
};

export type RemoveItemFromAllCollectionsMutationVariables = Exact<{
  input: Scalars['ID']['input'];
}>;

export type RemoveItemFromAllCollectionsMutation = {
  __typename?: 'Mutation';
  removeItemFromAllCollections: boolean;
};

export type FollowCreatorMutationVariables = Exact<{
  input: CreatorFollowInput;
}>;

export type FollowCreatorMutation = {
  __typename?: 'Mutation';
  followCreator: {
    __typename?: 'CreatorFollowPayload';
    message: string;
    status: boolean;
    creator: {
      __typename?: 'Creator';
      id: string;
      name: string;
      username: string;
      bio?: string | null;
      tiktok_url?: string | null;
      instagram_url?: string | null;
      is_followed?: boolean | null;
      collaborations?: number | null;
      user: { __typename?: 'User'; id: string; name: string };
      avatar?: { __typename?: 'Media'; full_url?: string | null } | null;
      followers: {
        __typename?: 'UserPaginator';
        paginatorInfo: { __typename?: 'PaginatorInfo'; total: number };
      };
      reels: {
        __typename?: 'ReelPaginator';
        paginatorInfo: { __typename?: 'PaginatorInfo'; total: number };
        data: Array<{
          __typename?: 'Reel';
          id: string;
          full_url: string;
          caption?: string | null;
          thumbnail?: string | null;
          places: {
            __typename?: 'PartnerPlacePaginator';
            data: Array<{
              __typename?: 'PartnerPlace';
              id: string;
              name: string;
              price_per_person?: number | null;
              menu_url?: string | null;
              deals: {
                __typename?: 'DealPaginator';
                data: Array<{
                  __typename?: 'Deal';
                  id: string;
                  title: string;
                  description: string;
                  deal_type: DealType;
                  max_saving?: number | null;
                  reuse_limit_days: number;
                  service_types: Array<{
                    __typename?: 'Tag';
                    title: string;
                  } | null>;
                }>;
              };
              rates: {
                __typename?: 'PartnerPlaceRate';
                google?: string | null;
                reviews_count?: number | null;
              };
              opening_hours: Array<{
                __typename?: 'PartnerPlaceOpeningHours';
                day?: number | null;
                from?: any | null;
                to?: any | null;
              }>;
              partner: {
                __typename?: 'Partner';
                id: string;
                name: string;
                description?: string | null;
                email?: string | null;
                instagram?: string | null;
                facebook?: string | null;
                twitter?: string | null;
                website?: string | null;
                partner_places: {
                  __typename?: 'PartnerPlacePaginator';
                  data: Array<{
                    __typename?: 'PartnerPlace';
                    id: string;
                    name: string;
                    address_line_1?: string | null;
                    address_line_2?: string | null;
                    phone?: string | null;
                    location: {
                      __typename?: 'GeoLocation';
                      lat?: number | null;
                      lng?: number | null;
                    };
                  }>;
                };
              };
              tags?: Array<{
                __typename?: 'Tag';
                id: string;
                title: string;
              }> | null;
              images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
              menu?: Array<{
                __typename?: 'Media';
                full_url?: string | null;
              } | null> | null;
              location: {
                __typename?: 'GeoLocation';
                lat?: number | null;
                lng?: number | null;
              };
              avatar: { __typename?: 'Media'; full_url?: string | null };
              ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
              parking: Array<{ __typename?: 'Tag'; title: string } | null>;
              specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
              cuisine_types: Array<{
                __typename?: 'Tag';
                title: string;
              } | null>;
              dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
              service_options: Array<{
                __typename?: 'Tag';
                title: string;
              } | null>;
              meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
              cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
            }>;
          };
        }>;
      };
    };
  };
};

export type FollowPlaceMutationVariables = Exact<{
  input: PlaceFollowInput;
}>;

export type FollowPlaceMutation = {
  __typename?: 'Mutation';
  followPlace: {
    __typename?: 'PlaceFollowPayload';
    message: string;
    status: boolean;
  };
};

export type RedeemDealMutationVariables = Exact<{
  input: RedeemDealInput;
}>;

export type RedeemDealMutation = {
  __typename?: 'Mutation';
  redeemDeal: {
    __typename?: 'RedeemDealResponse';
    myDeal: {
      __typename?: 'MyDeal';
      id: string;
      status: MyDealStatusEnum;
      reserved_at?: any | null;
      redeemed_at?: any | null;
      reuse_after?: any | null;
      deal: {
        __typename?: 'Deal';
        id: string;
        title: string;
        description: string;
        deal_type: DealType;
        max_saving?: number | null;
        reuse_limit_days: number;
        partner_place: {
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          price_per_person?: number | null;
          menu_url?: string | null;
          rates: {
            __typename?: 'PartnerPlaceRate';
            google?: string | null;
            reviews_count?: number | null;
          };
          opening_hours: Array<{
            __typename?: 'PartnerPlaceOpeningHours';
            day?: number | null;
            from?: any | null;
            to?: any | null;
          }>;
          partner: {
            __typename?: 'Partner';
            id: string;
            name: string;
            description?: string | null;
            email?: string | null;
            instagram?: string | null;
            facebook?: string | null;
            twitter?: string | null;
            website?: string | null;
            partner_places: {
              __typename?: 'PartnerPlacePaginator';
              data: Array<{
                __typename?: 'PartnerPlace';
                id: string;
                name: string;
                address_line_1?: string | null;
                address_line_2?: string | null;
                phone?: string | null;
                location: {
                  __typename?: 'GeoLocation';
                  lat?: number | null;
                  lng?: number | null;
                };
              }>;
            };
          };
          tags?: Array<{
            __typename?: 'Tag';
            id: string;
            title: string;
          }> | null;
          images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
          menu?: Array<{
            __typename?: 'Media';
            full_url?: string | null;
          } | null> | null;
          location: {
            __typename?: 'GeoLocation';
            lat?: number | null;
            lng?: number | null;
          };
          avatar: { __typename?: 'Media'; full_url?: string | null };
        };
        service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
      };
      reserve_slot: {
        __typename?: 'MyDealDateTimeSlot';
        date: string;
        slot: { __typename?: 'DealTimeSlot'; from: string; to: string };
      };
    };
  };
};

export type LikeMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  type: LikableType;
}>;

export type LikeMutation = {
  __typename?: 'Mutation';
  like: {
    __typename?: 'LikePayload';
    status: boolean;
    message: string;
    likeable?:
      | { __typename?: 'Creator'; user: { __typename?: 'User'; id: string } }
      | { __typename?: 'PartnerPlace'; name: string }
      | { __typename?: 'Reel'; caption?: string | null; full_url: string }
      | null;
  };
};

export type GetReelUploadUrlMutationVariables = Exact<{
  input: GetReelUploadUrlInput;
}>;

export type GetReelUploadUrlMutation = {
  __typename?: 'Mutation';
  getReelUploadUrl: {
    __typename?: 'GetReelUploadUrlResponse';
    url: string;
    path: string;
  };
};

export type CreateReelMutationVariables = Exact<{
  input: CreateReelInput;
}>;

export type CreateReelMutation = {
  __typename?: 'Mutation';
  createReel: {
    __typename?: 'CreateReelResponse';
    reel: { __typename?: 'Reel'; id: string; caption?: string | null };
  };
};

export type ReserveDealMutationVariables = Exact<{
  input: ReserveDealInput;
}>;

export type ReserveDealMutation = {
  __typename?: 'Mutation';
  reserveDeal: {
    __typename?: 'ReserveDealResponse';
    myDeal: {
      __typename?: 'MyDeal';
      id: string;
      status: MyDealStatusEnum;
      reserved_at?: any | null;
      redeemed_at?: any | null;
      reuse_after?: any | null;
      deal: {
        __typename?: 'Deal';
        id: string;
        title: string;
        description: string;
        deal_type: DealType;
        max_saving?: number | null;
        reuse_limit_days: number;
        partner_place: {
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          price_per_person?: number | null;
          menu_url?: string | null;
          rates: {
            __typename?: 'PartnerPlaceRate';
            google?: string | null;
            reviews_count?: number | null;
          };
          opening_hours: Array<{
            __typename?: 'PartnerPlaceOpeningHours';
            day?: number | null;
            from?: any | null;
            to?: any | null;
          }>;
          partner: {
            __typename?: 'Partner';
            id: string;
            name: string;
            description?: string | null;
            email?: string | null;
            instagram?: string | null;
            facebook?: string | null;
            twitter?: string | null;
            website?: string | null;
            partner_places: {
              __typename?: 'PartnerPlacePaginator';
              data: Array<{
                __typename?: 'PartnerPlace';
                id: string;
                name: string;
                address_line_1?: string | null;
                address_line_2?: string | null;
                phone?: string | null;
                location: {
                  __typename?: 'GeoLocation';
                  lat?: number | null;
                  lng?: number | null;
                };
              }>;
            };
          };
          tags?: Array<{
            __typename?: 'Tag';
            id: string;
            title: string;
          }> | null;
          images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
          menu?: Array<{
            __typename?: 'Media';
            full_url?: string | null;
          } | null> | null;
          location: {
            __typename?: 'GeoLocation';
            lat?: number | null;
            lng?: number | null;
          };
          avatar: { __typename?: 'Media'; full_url?: string | null };
        };
        service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
      };
      reserve_slot: {
        __typename?: 'MyDealDateTimeSlot';
        date: string;
        slot: { __typename?: 'DealTimeSlot'; from: string; to: string };
      };
    };
  };
};

export type SocialLoginMutationVariables = Exact<{
  provider: SocialLoginProvider;
  token: Scalars['String']['input'];
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
}>;

export type SocialLoginMutation = {
  __typename?: 'Mutation';
  socialLogin: {
    __typename?: 'SocialLoginResponse';
    token: string;
    status_code?: LoginStatusCode | null;
    user: {
      __typename?: 'User';
      id: string;
      name: string;
      email: string;
      status?: LoginStatusCode | null;
      fcm_token?: string | null;
      created_at?: any | null;
      creator?: { __typename?: 'Creator'; id: string } | null;
      invitations: {
        __typename?: 'UserInvitations';
        info: {
          __typename?: 'InvitationInfo';
          remaining: number;
          total: number;
        };
      };
    };
  };
};

export type InviteUserMutationVariables = Exact<{ [key: string]: never }>;

export type InviteUserMutation = {
  __typename?: 'Mutation';
  inviteUser: string;
};

export type AcceptInvitationMutationVariables = Exact<{
  token: Scalars['String']['input'];
}>;

export type AcceptInvitationMutation = {
  __typename?: 'Mutation';
  acceptInvitation: {
    __typename?: 'AcceptInvitationResponse';
    statusCode: number;
    user: {
      __typename?: 'User';
      id: string;
      name: string;
      email: string;
      status?: LoginStatusCode | null;
      fcm_token?: string | null;
      created_at?: any | null;
      creator?: { __typename?: 'Creator'; id: string } | null;
      invitations: {
        __typename?: 'UserInvitations';
        info: {
          __typename?: 'InvitationInfo';
          remaining: number;
          total: number;
        };
      };
    };
  };
};

export type AddFcmTokenMutationVariables = Exact<{
  token: Scalars['String']['input'];
}>;

export type AddFcmTokenMutation = {
  __typename?: 'Mutation';
  addFCMToken: { __typename?: 'User'; id: string; fcm_token?: string | null };
};

export type ClearFcmTokenMutationVariables = Exact<{ [key: string]: never }>;

export type ClearFcmTokenMutation = {
  __typename?: 'Mutation';
  clearFCMToken: { __typename?: 'User'; id: string };
};

export type CollectionsQueryVariables = Exact<{
  collectableId?: InputMaybe<Scalars['ID']['input']>;
}>;

export type CollectionsQuery = {
  __typename?: 'Query';
  collections: Array<{
    __typename?: 'Collection';
    is_item_exists?: boolean | null;
    id: string;
    title: string;
    description?: string | null;
    myRole: CollectionUserRoleEnum;
    items: Array<{
      __typename?: 'CollectionItem';
      id: string;
      collectable:
        | { __typename?: 'Creator' }
        | {
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            avatar: { __typename?: 'Media'; full_url?: string | null };
            partner: { __typename?: 'Partner'; name: string };
          }
        | { __typename?: 'Reel' };
    } | null>;
  }>;
};

export type CollectionQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;

export type CollectionQuery = {
  __typename?: 'Query';
  collection?: {
    __typename?: 'Collection';
    id: string;
    title: string;
    description?: string | null;
    myRole: CollectionUserRoleEnum;
    items: Array<{
      __typename?: 'CollectionItem';
      id: string;
      collectable:
        | { __typename?: 'Creator' }
        | {
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            price_per_person?: number | null;
            menu_url?: string | null;
            deals: {
              __typename?: 'DealPaginator';
              data: Array<{
                __typename?: 'Deal';
                id: string;
                title: string;
                description: string;
                deal_type: DealType;
                max_saving?: number | null;
                reuse_limit_days: number;
                service_types: Array<{
                  __typename?: 'Tag';
                  title: string;
                } | null>;
              }>;
            };
            rates: {
              __typename?: 'PartnerPlaceRate';
              google?: string | null;
              reviews_count?: number | null;
            };
            opening_hours: Array<{
              __typename?: 'PartnerPlaceOpeningHours';
              day?: number | null;
              from?: any | null;
              to?: any | null;
            }>;
            partner: {
              __typename?: 'Partner';
              id: string;
              name: string;
              description?: string | null;
              email?: string | null;
              instagram?: string | null;
              facebook?: string | null;
              twitter?: string | null;
              website?: string | null;
              partner_places: {
                __typename?: 'PartnerPlacePaginator';
                data: Array<{
                  __typename?: 'PartnerPlace';
                  id: string;
                  name: string;
                  address_line_1?: string | null;
                  address_line_2?: string | null;
                  phone?: string | null;
                  location: {
                    __typename?: 'GeoLocation';
                    lat?: number | null;
                    lng?: number | null;
                  };
                }>;
              };
            };
            tags?: Array<{
              __typename?: 'Tag';
              id: string;
              title: string;
            }> | null;
            images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
            menu?: Array<{
              __typename?: 'Media';
              full_url?: string | null;
            } | null> | null;
            location: {
              __typename?: 'GeoLocation';
              lat?: number | null;
              lng?: number | null;
            };
            avatar: { __typename?: 'Media'; full_url?: string | null };
            ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
            parking: Array<{ __typename?: 'Tag'; title: string } | null>;
            specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
            cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
            dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
            service_options: Array<{
              __typename?: 'Tag';
              title: string;
            } | null>;
            meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
            cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
          }
        | { __typename?: 'Reel' };
    } | null>;
  } | null;
};

export type CreatorQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;

export type CreatorQuery = {
  __typename?: 'Query';
  creator: {
    __typename?: 'Creator';
    id: string;
    name: string;
    username: string;
    bio?: string | null;
    tiktok_url?: string | null;
    instagram_url?: string | null;
    is_followed?: boolean | null;
    collaborations?: number | null;
    user: { __typename?: 'User'; id: string; name: string };
    avatar?: { __typename?: 'Media'; full_url?: string | null } | null;
    followers: {
      __typename?: 'UserPaginator';
      paginatorInfo: { __typename?: 'PaginatorInfo'; total: number };
    };
    reels: {
      __typename?: 'ReelPaginator';
      paginatorInfo: { __typename?: 'PaginatorInfo'; total: number };
      data: Array<{
        __typename?: 'Reel';
        id: string;
        full_url: string;
        caption?: string | null;
        thumbnail?: string | null;
        places: {
          __typename?: 'PartnerPlacePaginator';
          data: Array<{
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            price_per_person?: number | null;
            menu_url?: string | null;
            deals: {
              __typename?: 'DealPaginator';
              data: Array<{
                __typename?: 'Deal';
                id: string;
                title: string;
                description: string;
                deal_type: DealType;
                max_saving?: number | null;
                reuse_limit_days: number;
                service_types: Array<{
                  __typename?: 'Tag';
                  title: string;
                } | null>;
              }>;
            };
            rates: {
              __typename?: 'PartnerPlaceRate';
              google?: string | null;
              reviews_count?: number | null;
            };
            opening_hours: Array<{
              __typename?: 'PartnerPlaceOpeningHours';
              day?: number | null;
              from?: any | null;
              to?: any | null;
            }>;
            partner: {
              __typename?: 'Partner';
              id: string;
              name: string;
              description?: string | null;
              email?: string | null;
              instagram?: string | null;
              facebook?: string | null;
              twitter?: string | null;
              website?: string | null;
              partner_places: {
                __typename?: 'PartnerPlacePaginator';
                data: Array<{
                  __typename?: 'PartnerPlace';
                  id: string;
                  name: string;
                  address_line_1?: string | null;
                  address_line_2?: string | null;
                  phone?: string | null;
                  location: {
                    __typename?: 'GeoLocation';
                    lat?: number | null;
                    lng?: number | null;
                  };
                }>;
              };
            };
            tags?: Array<{
              __typename?: 'Tag';
              id: string;
              title: string;
            }> | null;
            images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
            menu?: Array<{
              __typename?: 'Media';
              full_url?: string | null;
            } | null> | null;
            location: {
              __typename?: 'GeoLocation';
              lat?: number | null;
              lng?: number | null;
            };
            avatar: { __typename?: 'Media'; full_url?: string | null };
            ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
            parking: Array<{ __typename?: 'Tag'; title: string } | null>;
            specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
            cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
            dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
            service_options: Array<{
              __typename?: 'Tag';
              title: string;
            } | null>;
            meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
            cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
          }>;
        };
      }>;
    };
  };
};

export type MyDealsQueryVariables = Exact<{ [key: string]: never }>;

export type MyDealsQuery = {
  __typename?: 'Query';
  myDeals: {
    __typename?: 'MyDealPaginator';
    data: Array<{
      __typename?: 'MyDeal';
      id: string;
      status: MyDealStatusEnum;
      reserved_at?: any | null;
      redeemed_at?: any | null;
      reuse_after?: any | null;
      deal: {
        __typename?: 'Deal';
        id: string;
        title: string;
        description: string;
        deal_type: DealType;
        max_saving?: number | null;
        reuse_limit_days: number;
        partner_place: {
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          price_per_person?: number | null;
          menu_url?: string | null;
          rates: {
            __typename?: 'PartnerPlaceRate';
            google?: string | null;
            reviews_count?: number | null;
          };
          opening_hours: Array<{
            __typename?: 'PartnerPlaceOpeningHours';
            day?: number | null;
            from?: any | null;
            to?: any | null;
          }>;
          partner: {
            __typename?: 'Partner';
            id: string;
            name: string;
            description?: string | null;
            email?: string | null;
            instagram?: string | null;
            facebook?: string | null;
            twitter?: string | null;
            website?: string | null;
            partner_places: {
              __typename?: 'PartnerPlacePaginator';
              data: Array<{
                __typename?: 'PartnerPlace';
                id: string;
                name: string;
                address_line_1?: string | null;
                address_line_2?: string | null;
                phone?: string | null;
                location: {
                  __typename?: 'GeoLocation';
                  lat?: number | null;
                  lng?: number | null;
                };
              }>;
            };
          };
          tags?: Array<{
            __typename?: 'Tag';
            id: string;
            title: string;
          }> | null;
          images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
          menu?: Array<{
            __typename?: 'Media';
            full_url?: string | null;
          } | null> | null;
          location: {
            __typename?: 'GeoLocation';
            lat?: number | null;
            lng?: number | null;
          };
          avatar: { __typename?: 'Media'; full_url?: string | null };
        };
        service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
      };
      reserve_slot: {
        __typename?: 'MyDealDateTimeSlot';
        date: string;
        slot: { __typename?: 'DealTimeSlot'; from: string; to: string };
      };
    }>;
  };
};

export type DealQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;

export type DealQuery = {
  __typename?: 'Query';
  deal: {
    __typename?: 'Deal';
    id: string;
    title: string;
    description: string;
    deal_type: DealType;
    max_saving?: number | null;
    reuse_limit_days: number;
    available_slots: Array<{
      __typename?: 'DealDateTimeSlot';
      date: string;
      available_seats: number;
      slots: Array<{ __typename?: 'DealTimeSlot'; from: string; to: string }>;
    }>;
    partner_place: {
      __typename?: 'PartnerPlace';
      id: string;
      name: string;
      price_per_person?: number | null;
      menu_url?: string | null;
      rates: {
        __typename?: 'PartnerPlaceRate';
        google?: string | null;
        reviews_count?: number | null;
      };
      opening_hours: Array<{
        __typename?: 'PartnerPlaceOpeningHours';
        day?: number | null;
        from?: any | null;
        to?: any | null;
      }>;
      partner: {
        __typename?: 'Partner';
        id: string;
        name: string;
        description?: string | null;
        email?: string | null;
        instagram?: string | null;
        facebook?: string | null;
        twitter?: string | null;
        website?: string | null;
        partner_places: {
          __typename?: 'PartnerPlacePaginator';
          data: Array<{
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            address_line_1?: string | null;
            address_line_2?: string | null;
            phone?: string | null;
            location: {
              __typename?: 'GeoLocation';
              lat?: number | null;
              lng?: number | null;
            };
          }>;
        };
      };
      tags?: Array<{ __typename?: 'Tag'; id: string; title: string }> | null;
      images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
      menu?: Array<{
        __typename?: 'Media';
        full_url?: string | null;
      } | null> | null;
      location: {
        __typename?: 'GeoLocation';
        lat?: number | null;
        lng?: number | null;
      };
      avatar: { __typename?: 'Media'; full_url?: string | null };
    };
    service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
  };
};

export type PlaceQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;

export type PlaceQuery = {
  __typename?: 'Query';
  partnerPlace: {
    __typename?: 'PartnerPlace';
    is_place_in_collection: boolean;
    id: string;
    name: string;
    price_per_person?: number | null;
    menu_url?: string | null;
    deals: {
      __typename?: 'DealPaginator';
      data: Array<{
        __typename?: 'Deal';
        id: string;
        title: string;
        description: string;
        deal_type: DealType;
        max_saving?: number | null;
        reuse_limit_days: number;
        myDeals: {
          __typename?: 'MyDealPaginator';
          data: Array<{
            __typename?: 'MyDeal';
            id: string;
            status: MyDealStatusEnum;
            reserved_at?: any | null;
            redeemed_at?: any | null;
            reuse_after?: any | null;
            reserve_slot: {
              __typename?: 'MyDealDateTimeSlot';
              date: string;
              slot: { __typename?: 'DealTimeSlot'; from: string; to: string };
            };
          }>;
        };
        service_types: Array<{ __typename?: 'Tag'; title: string } | null>;
      }>;
    };
    reels: {
      __typename?: 'ReelPaginator';
      data: Array<{
        __typename?: 'Reel';
        id: string;
        full_url: string;
        caption?: string | null;
        thumbnail?: string | null;
        places: {
          __typename?: 'PartnerPlacePaginator';
          data: Array<{
            __typename?: 'PartnerPlace';
            id: string;
            name: string;
            price_per_person?: number | null;
            menu_url?: string | null;
            deals: {
              __typename?: 'DealPaginator';
              data: Array<{
                __typename?: 'Deal';
                id: string;
                title: string;
                description: string;
                deal_type: DealType;
                max_saving?: number | null;
                reuse_limit_days: number;
                service_types: Array<{
                  __typename?: 'Tag';
                  title: string;
                } | null>;
              }>;
            };
            rates: {
              __typename?: 'PartnerPlaceRate';
              google?: string | null;
              reviews_count?: number | null;
            };
            opening_hours: Array<{
              __typename?: 'PartnerPlaceOpeningHours';
              day?: number | null;
              from?: any | null;
              to?: any | null;
            }>;
            partner: {
              __typename?: 'Partner';
              id: string;
              name: string;
              description?: string | null;
              email?: string | null;
              instagram?: string | null;
              facebook?: string | null;
              twitter?: string | null;
              website?: string | null;
              partner_places: {
                __typename?: 'PartnerPlacePaginator';
                data: Array<{
                  __typename?: 'PartnerPlace';
                  id: string;
                  name: string;
                  address_line_1?: string | null;
                  address_line_2?: string | null;
                  phone?: string | null;
                  location: {
                    __typename?: 'GeoLocation';
                    lat?: number | null;
                    lng?: number | null;
                  };
                }>;
              };
            };
            tags?: Array<{
              __typename?: 'Tag';
              id: string;
              title: string;
            }> | null;
            images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
            menu?: Array<{
              __typename?: 'Media';
              full_url?: string | null;
            } | null> | null;
            location: {
              __typename?: 'GeoLocation';
              lat?: number | null;
              lng?: number | null;
            };
            avatar: { __typename?: 'Media'; full_url?: string | null };
            ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
            parking: Array<{ __typename?: 'Tag'; title: string } | null>;
            specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
            cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
            dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
            service_options: Array<{
              __typename?: 'Tag';
              title: string;
            } | null>;
            meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
            cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
          }>;
        };
      }>;
    };
    rates: {
      __typename?: 'PartnerPlaceRate';
      google?: string | null;
      reviews_count?: number | null;
    };
    opening_hours: Array<{
      __typename?: 'PartnerPlaceOpeningHours';
      day?: number | null;
      from?: any | null;
      to?: any | null;
    }>;
    partner: {
      __typename?: 'Partner';
      id: string;
      name: string;
      description?: string | null;
      email?: string | null;
      instagram?: string | null;
      facebook?: string | null;
      twitter?: string | null;
      website?: string | null;
      partner_places: {
        __typename?: 'PartnerPlacePaginator';
        data: Array<{
          __typename?: 'PartnerPlace';
          id: string;
          name: string;
          address_line_1?: string | null;
          address_line_2?: string | null;
          phone?: string | null;
          location: {
            __typename?: 'GeoLocation';
            lat?: number | null;
            lng?: number | null;
          };
        }>;
      };
    };
    tags?: Array<{ __typename?: 'Tag'; id: string; title: string }> | null;
    images: Array<{ __typename?: 'Media'; full_url?: string | null }>;
    menu?: Array<{
      __typename?: 'Media';
      full_url?: string | null;
    } | null> | null;
    location: {
      __typename?: 'GeoLocation';
      lat?: number | null;
      lng?: number | null;
    };
    avatar: { __typename?: 'Media'; full_url?: string | null };
    ambiance: Array<{ __typename?: 'Tag'; title: string } | null>;
    parking: Array<{ __typename?: 'Tag'; title: string } | null>;
    specialities: Array<{ __typename?: 'Tag'; title: string } | null>;
    cuisine_types: Array<{ __typename?: 'Tag'; title: string } | null>;
    dietary: Array<{ __typename?: 'Tag'; title: string } | null>;
    service_options: Array<{ __typename?: 'Tag'; title: string } | null>;
    meal_times: Array<{ __typename?: 'Tag'; title: string } | null>;
    cravings: Array<{ __typename?: 'Tag'; title: string } | null>;
  };
};

export type ReelStatusQueryVariables = Exact<{
  ids: Array<Scalars['ID']['input']> | Scalars['ID']['input'];
}>;

export type ReelStatusQuery = {
  __typename?: 'Query';
  reelsMetaStatuses: Array<{
    __typename?: 'Reel';
    id: string;
    caption?: string | null;
    is_liked: boolean;
    likes_count: number;
    is_place_followed: boolean;
    is_creator_followed: boolean;
    is_place_in_collection: boolean;
    saved_places_count: number;
  }>;
};

export type MeQueryVariables = Exact<{ [key: string]: never }>;

export type MeQuery = {
  __typename?: 'Query';
  me: {
    __typename?: 'User';
    id: string;
    name: string;
    email: string;
    status?: LoginStatusCode | null;
    fcm_token?: string | null;
    created_at?: any | null;
    creator?: { __typename?: 'Creator'; id: string } | null;
    invitations: {
      __typename?: 'UserInvitations';
      info: { __typename?: 'InvitationInfo'; remaining: number; total: number };
    };
  };
};

export type UserFollowedPlacesQueryVariables = Exact<{ [key: string]: never }>;

export type UserFollowedPlacesQuery = {
  __typename?: 'Query';
  me: {
    __typename?: 'User';
    following_places: {
      __typename?: 'PartnerPlacePaginator';
      data: Array<{
        __typename?: 'PartnerPlace';
        id: string;
        name: string;
        avatar: { __typename?: 'Media'; full_url?: string | null };
        partner: { __typename?: 'Partner'; id: string; name: string };
      }>;
    };
  };
};

export const PlaceInsidePartnerFragmentDoc = gql`
  fragment PlaceInsidePartner on PartnerPlace {
    id
    name
    address_line_1
    address_line_2
    phone
    location {
      lat
      lng
    }
  }
`;
export const PartnerFragmentDoc = gql`
  fragment Partner on Partner {
    id
    name
    description
    email
    instagram
    facebook
    twitter
    website
    partner_places(first: 10) {
      data {
        ...PlaceInsidePartner
      }
    }
  }
  ${PlaceInsidePartnerFragmentDoc}
`;
export const PlaceFragmentDoc = gql`
  fragment Place on PartnerPlace {
    id
    name
    price_per_person
    rates {
      google
      reviews_count
    }
    opening_hours {
      day
      from
      to
    }
    partner {
      ...Partner
    }
    tags {
      id
      title
    }
    images {
      full_url
    }
    menu {
      full_url
    }
    menu_url
    location {
      lat
      lng
    }
    avatar {
      full_url
    }
  }
  ${PartnerFragmentDoc}
`;
export const PlaceTagsFragmentDoc = gql`
  fragment PlaceTags on PartnerPlace {
    ambiance {
      title
    }
    parking {
      title
    }
    specialities {
      title
    }
    cuisine_types {
      title
    }
    dietary {
      title
    }
    service_options {
      title
    }
    meal_times {
      title
    }
    cravings {
      title
    }
  }
`;
export const DealWithOutPlacesFragmentDoc = gql`
  fragment DealWithOutPlaces on Deal {
    id
    title
    description
    deal_type
    max_saving
    reuse_limit_days
    service_types {
      title
    }
  }
`;
export const PlaceWithDealsFragmentDoc = gql`
  fragment PlaceWithDeals on PartnerPlace {
    ...Place
    ...PlaceTags
    deals(first: 100) {
      data {
        ...DealWithOutPlaces
      }
    }
  }
  ${PlaceFragmentDoc}
  ${PlaceTagsFragmentDoc}
  ${DealWithOutPlacesFragmentDoc}
`;
export const FullCollectionFragmentDoc = gql`
  fragment FullCollection on Collection {
    id
    title
    description
    items {
      id
      collectable {
        ... on PartnerPlace {
          ...PlaceWithDeals
        }
      }
    }
    myRole
  }
  ${PlaceWithDealsFragmentDoc}
`;
export const CollectionFragmentDoc = gql`
  fragment Collection on Collection {
    id
    title
    description
    items {
      id
      collectable {
        ... on PartnerPlace {
          id
          name
          avatar {
            full_url
          }
          partner {
            name
          }
        }
      }
    }
    myRole
  }
`;
export const CollectionItemCheckFragmentDoc = gql`
  fragment CollectionItemCheck on Collection {
    ...Collection
    is_item_exists(
      collectable_id: $collectableId
      collectable_type: PARTNER_LOCATION
    )
  }
  ${CollectionFragmentDoc}
`;
export const ReelFragmentDoc = gql`
  fragment Reel on Reel {
    id
    full_url
    caption
    thumbnail
    places(first: 1) {
      data {
        ...PlaceWithDeals
      }
    }
  }
  ${PlaceWithDealsFragmentDoc}
`;
export const CreatorFragmentDoc = gql`
  fragment Creator on Creator {
    id
    name
    username
    bio
    tiktok_url
    instagram_url
    user {
      id
      name
    }
    is_followed
    avatar {
      full_url
    }
    followers(first: 1) {
      paginatorInfo {
        total
      }
    }
    reels(first: 100) {
      paginatorInfo {
        total
      }
      data {
        ...Reel
      }
    }
    collaborations
  }
  ${ReelFragmentDoc}
`;
export const DealFragmentDoc = gql`
  fragment Deal on Deal {
    ...DealWithOutPlaces
    partner_place {
      ...Place
    }
  }
  ${DealWithOutPlacesFragmentDoc}
  ${PlaceFragmentDoc}
`;
export const FullDealFragmentDoc = gql`
  fragment FullDeal on Deal {
    ...Deal
    available_slots {
      date
      available_seats
      slots {
        from
        to
      }
    }
  }
  ${DealFragmentDoc}
`;
export const MyDealFragmentDoc = gql`
  fragment MyDeal on MyDeal {
    id
    deal {
      ...Deal
    }
    status
    reserved_at
    redeemed_at
    reuse_after
    reserve_slot {
      date
      slot {
        from
        to
      }
    }
  }
  ${DealFragmentDoc}
`;
export const MyDealWithoutDealFragmentDoc = gql`
  fragment MyDealWithoutDeal on MyDeal {
    id
    status
    reserved_at
    redeemed_at
    reuse_after
    reserve_slot {
      date
      slot {
        from
        to
      }
    }
  }
`;
export const DealWithMyDealsFragmentDoc = gql`
  fragment DealWithMyDeals on Deal {
    ...DealWithOutPlaces
    myDeals(first: 1) {
      data {
        ...MyDealWithoutDeal
      }
    }
  }
  ${DealWithOutPlacesFragmentDoc}
  ${MyDealWithoutDealFragmentDoc}
`;
export const FullPlaceFragmentDoc = gql`
  fragment FullPlace on PartnerPlace {
    ...Place
    deals(first: 100) {
      data {
        ...DealWithMyDeals
      }
    }
    reels(first: 20) {
      data {
        ...Reel
      }
    }
    ...PlaceTags
    is_place_in_collection
  }
  ${PlaceFragmentDoc}
  ${DealWithMyDealsFragmentDoc}
  ${ReelFragmentDoc}
  ${PlaceTagsFragmentDoc}
`;
export const UserFragmentDoc = gql`
  fragment User on User {
    id
    name
    email
    creator {
      id
    }
    status
    fcm_token
    created_at
    invitations {
      info {
        remaining
        total
      }
    }
  }
`;
export const MiniPlaceFragmentDoc = gql`
  fragment MiniPlace on PartnerPlace {
    id
    name
    avatar {
      full_url
    }
    partner {
      id
      name
    }
  }
`;
export const UserFollowedPlacesFragmentDoc = gql`
  fragment UserFollowedPlaces on User {
    following_places(first: 100) {
      data {
        ...MiniPlace
      }
    }
  }
  ${MiniPlaceFragmentDoc}
`;
export const CancelDealDocument = gql`
  mutation CancelDeal($input: CancelDealInput!) {
    cancelDeal(input: $input) {
      message
    }
  }
`;
export type CancelDealMutationFn = Apollo.MutationFunction<
  CancelDealMutation,
  CancelDealMutationVariables
>;

/**
 * __useCancelDealMutation__
 *
 * To run a mutation, you first call `useCancelDealMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCancelDealMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [cancelDealMutation, { data, loading, error }] = useCancelDealMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCancelDealMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CancelDealMutation,
    CancelDealMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CancelDealMutation, CancelDealMutationVariables>(
    CancelDealDocument,
    options
  );
}
export type CancelDealMutationHookResult = ReturnType<
  typeof useCancelDealMutation
>;
export type CancelDealMutationResult =
  Apollo.MutationResult<CancelDealMutation>;
export type CancelDealMutationOptions = Apollo.BaseMutationOptions<
  CancelDealMutation,
  CancelDealMutationVariables
>;
export const CreateCollectionDocument = gql`
  mutation CreateCollection($input: CreateCollectionInput!) {
    createCollection(input: $input) {
      ...Collection
    }
  }
  ${CollectionFragmentDoc}
`;
export type CreateCollectionMutationFn = Apollo.MutationFunction<
  CreateCollectionMutation,
  CreateCollectionMutationVariables
>;

/**
 * __useCreateCollectionMutation__
 *
 * To run a mutation, you first call `useCreateCollectionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateCollectionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createCollectionMutation, { data, loading, error }] = useCreateCollectionMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateCollectionMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateCollectionMutation,
    CreateCollectionMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateCollectionMutation,
    CreateCollectionMutationVariables
  >(CreateCollectionDocument, options);
}
export type CreateCollectionMutationHookResult = ReturnType<
  typeof useCreateCollectionMutation
>;
export type CreateCollectionMutationResult =
  Apollo.MutationResult<CreateCollectionMutation>;
export type CreateCollectionMutationOptions = Apollo.BaseMutationOptions<
  CreateCollectionMutation,
  CreateCollectionMutationVariables
>;
export const EditCollectionDocument = gql`
  mutation EditCollection($input: EditCollectionInput!) {
    editCollection(input: $input) {
      ...Collection
    }
  }
  ${CollectionFragmentDoc}
`;
export type EditCollectionMutationFn = Apollo.MutationFunction<
  EditCollectionMutation,
  EditCollectionMutationVariables
>;

/**
 * __useEditCollectionMutation__
 *
 * To run a mutation, you first call `useEditCollectionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditCollectionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editCollectionMutation, { data, loading, error }] = useEditCollectionMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useEditCollectionMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditCollectionMutation,
    EditCollectionMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditCollectionMutation,
    EditCollectionMutationVariables
  >(EditCollectionDocument, options);
}
export type EditCollectionMutationHookResult = ReturnType<
  typeof useEditCollectionMutation
>;
export type EditCollectionMutationResult =
  Apollo.MutationResult<EditCollectionMutation>;
export type EditCollectionMutationOptions = Apollo.BaseMutationOptions<
  EditCollectionMutation,
  EditCollectionMutationVariables
>;
export const DeleteCollectionDocument = gql`
  mutation DeleteCollection($id: ID!) {
    deleteCollection(id: $id)
  }
`;
export type DeleteCollectionMutationFn = Apollo.MutationFunction<
  DeleteCollectionMutation,
  DeleteCollectionMutationVariables
>;

/**
 * __useDeleteCollectionMutation__
 *
 * To run a mutation, you first call `useDeleteCollectionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteCollectionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteCollectionMutation, { data, loading, error }] = useDeleteCollectionMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteCollectionMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteCollectionMutation,
    DeleteCollectionMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteCollectionMutation,
    DeleteCollectionMutationVariables
  >(DeleteCollectionDocument, options);
}
export type DeleteCollectionMutationHookResult = ReturnType<
  typeof useDeleteCollectionMutation
>;
export type DeleteCollectionMutationResult =
  Apollo.MutationResult<DeleteCollectionMutation>;
export type DeleteCollectionMutationOptions = Apollo.BaseMutationOptions<
  DeleteCollectionMutation,
  DeleteCollectionMutationVariables
>;
export const AddItemToCollectionDocument = gql`
  mutation AddItemToCollection($input: AddItemToCollectionInput!) {
    addItemToCollection(input: $input) {
      ...Collection
    }
  }
  ${CollectionFragmentDoc}
`;
export type AddItemToCollectionMutationFn = Apollo.MutationFunction<
  AddItemToCollectionMutation,
  AddItemToCollectionMutationVariables
>;

/**
 * __useAddItemToCollectionMutation__
 *
 * To run a mutation, you first call `useAddItemToCollectionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddItemToCollectionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addItemToCollectionMutation, { data, loading, error }] = useAddItemToCollectionMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAddItemToCollectionMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AddItemToCollectionMutation,
    AddItemToCollectionMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AddItemToCollectionMutation,
    AddItemToCollectionMutationVariables
  >(AddItemToCollectionDocument, options);
}
export type AddItemToCollectionMutationHookResult = ReturnType<
  typeof useAddItemToCollectionMutation
>;
export type AddItemToCollectionMutationResult =
  Apollo.MutationResult<AddItemToCollectionMutation>;
export type AddItemToCollectionMutationOptions = Apollo.BaseMutationOptions<
  AddItemToCollectionMutation,
  AddItemToCollectionMutationVariables
>;
export const RemoveItemFromCollectionDocument = gql`
  mutation RemoveItemFromCollection($input: RemoveItemFromCollectionInput!) {
    removeItemFromCollection(input: $input)
  }
`;
export type RemoveItemFromCollectionMutationFn = Apollo.MutationFunction<
  RemoveItemFromCollectionMutation,
  RemoveItemFromCollectionMutationVariables
>;

/**
 * __useRemoveItemFromCollectionMutation__
 *
 * To run a mutation, you first call `useRemoveItemFromCollectionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRemoveItemFromCollectionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [removeItemFromCollectionMutation, { data, loading, error }] = useRemoveItemFromCollectionMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRemoveItemFromCollectionMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RemoveItemFromCollectionMutation,
    RemoveItemFromCollectionMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RemoveItemFromCollectionMutation,
    RemoveItemFromCollectionMutationVariables
  >(RemoveItemFromCollectionDocument, options);
}
export type RemoveItemFromCollectionMutationHookResult = ReturnType<
  typeof useRemoveItemFromCollectionMutation
>;
export type RemoveItemFromCollectionMutationResult =
  Apollo.MutationResult<RemoveItemFromCollectionMutation>;
export type RemoveItemFromCollectionMutationOptions =
  Apollo.BaseMutationOptions<
    RemoveItemFromCollectionMutation,
    RemoveItemFromCollectionMutationVariables
  >;
export const RemoveItemFromAllCollectionsDocument = gql`
  mutation RemoveItemFromAllCollections($input: ID!) {
    removeItemFromAllCollections(
      input: { collectable_id: $input, collectable_type: PARTNER_LOCATION }
    )
  }
`;
export type RemoveItemFromAllCollectionsMutationFn = Apollo.MutationFunction<
  RemoveItemFromAllCollectionsMutation,
  RemoveItemFromAllCollectionsMutationVariables
>;

/**
 * __useRemoveItemFromAllCollectionsMutation__
 *
 * To run a mutation, you first call `useRemoveItemFromAllCollectionsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRemoveItemFromAllCollectionsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [removeItemFromAllCollectionsMutation, { data, loading, error }] = useRemoveItemFromAllCollectionsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRemoveItemFromAllCollectionsMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RemoveItemFromAllCollectionsMutation,
    RemoveItemFromAllCollectionsMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RemoveItemFromAllCollectionsMutation,
    RemoveItemFromAllCollectionsMutationVariables
  >(RemoveItemFromAllCollectionsDocument, options);
}
export type RemoveItemFromAllCollectionsMutationHookResult = ReturnType<
  typeof useRemoveItemFromAllCollectionsMutation
>;
export type RemoveItemFromAllCollectionsMutationResult =
  Apollo.MutationResult<RemoveItemFromAllCollectionsMutation>;
export type RemoveItemFromAllCollectionsMutationOptions =
  Apollo.BaseMutationOptions<
    RemoveItemFromAllCollectionsMutation,
    RemoveItemFromAllCollectionsMutationVariables
  >;
export const FollowCreatorDocument = gql`
  mutation FollowCreator($input: CreatorFollowInput!) {
    followCreator(input: $input) {
      message
      status
      creator {
        ...Creator
      }
    }
  }
  ${CreatorFragmentDoc}
`;
export type FollowCreatorMutationFn = Apollo.MutationFunction<
  FollowCreatorMutation,
  FollowCreatorMutationVariables
>;

/**
 * __useFollowCreatorMutation__
 *
 * To run a mutation, you first call `useFollowCreatorMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useFollowCreatorMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [followCreatorMutation, { data, loading, error }] = useFollowCreatorMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useFollowCreatorMutation(
  baseOptions?: Apollo.MutationHookOptions<
    FollowCreatorMutation,
    FollowCreatorMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    FollowCreatorMutation,
    FollowCreatorMutationVariables
  >(FollowCreatorDocument, options);
}
export type FollowCreatorMutationHookResult = ReturnType<
  typeof useFollowCreatorMutation
>;
export type FollowCreatorMutationResult =
  Apollo.MutationResult<FollowCreatorMutation>;
export type FollowCreatorMutationOptions = Apollo.BaseMutationOptions<
  FollowCreatorMutation,
  FollowCreatorMutationVariables
>;
export const FollowPlaceDocument = gql`
  mutation FollowPlace($input: PlaceFollowInput!) {
    followPlace(input: $input) {
      message
      status
    }
  }
`;
export type FollowPlaceMutationFn = Apollo.MutationFunction<
  FollowPlaceMutation,
  FollowPlaceMutationVariables
>;

/**
 * __useFollowPlaceMutation__
 *
 * To run a mutation, you first call `useFollowPlaceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useFollowPlaceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [followPlaceMutation, { data, loading, error }] = useFollowPlaceMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useFollowPlaceMutation(
  baseOptions?: Apollo.MutationHookOptions<
    FollowPlaceMutation,
    FollowPlaceMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<FollowPlaceMutation, FollowPlaceMutationVariables>(
    FollowPlaceDocument,
    options
  );
}
export type FollowPlaceMutationHookResult = ReturnType<
  typeof useFollowPlaceMutation
>;
export type FollowPlaceMutationResult =
  Apollo.MutationResult<FollowPlaceMutation>;
export type FollowPlaceMutationOptions = Apollo.BaseMutationOptions<
  FollowPlaceMutation,
  FollowPlaceMutationVariables
>;
export const RedeemDealDocument = gql`
  mutation RedeemDeal($input: RedeemDealInput!) {
    redeemDeal(input: $input) {
      myDeal {
        ...MyDeal
      }
    }
  }
  ${MyDealFragmentDoc}
`;
export type RedeemDealMutationFn = Apollo.MutationFunction<
  RedeemDealMutation,
  RedeemDealMutationVariables
>;

/**
 * __useRedeemDealMutation__
 *
 * To run a mutation, you first call `useRedeemDealMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRedeemDealMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [redeemDealMutation, { data, loading, error }] = useRedeemDealMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRedeemDealMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RedeemDealMutation,
    RedeemDealMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<RedeemDealMutation, RedeemDealMutationVariables>(
    RedeemDealDocument,
    options
  );
}
export type RedeemDealMutationHookResult = ReturnType<
  typeof useRedeemDealMutation
>;
export type RedeemDealMutationResult =
  Apollo.MutationResult<RedeemDealMutation>;
export type RedeemDealMutationOptions = Apollo.BaseMutationOptions<
  RedeemDealMutation,
  RedeemDealMutationVariables
>;
export const LikeDocument = gql`
  mutation Like($id: ID!, $type: LikableType!) {
    like(input: { id: $id, type: $type }) {
      status
      message
      likeable {
        ... on PartnerPlace {
          name
        }
        ... on Creator {
          user {
            id
          }
        }
        ... on Reel {
          caption
          full_url
        }
      }
    }
  }
`;
export type LikeMutationFn = Apollo.MutationFunction<
  LikeMutation,
  LikeMutationVariables
>;

/**
 * __useLikeMutation__
 *
 * To run a mutation, you first call `useLikeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLikeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [likeMutation, { data, loading, error }] = useLikeMutation({
 *   variables: {
 *      id: // value for 'id'
 *      type: // value for 'type'
 *   },
 * });
 */
export function useLikeMutation(
  baseOptions?: Apollo.MutationHookOptions<LikeMutation, LikeMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<LikeMutation, LikeMutationVariables>(
    LikeDocument,
    options
  );
}
export type LikeMutationHookResult = ReturnType<typeof useLikeMutation>;
export type LikeMutationResult = Apollo.MutationResult<LikeMutation>;
export type LikeMutationOptions = Apollo.BaseMutationOptions<
  LikeMutation,
  LikeMutationVariables
>;
export const GetReelUploadUrlDocument = gql`
  mutation GetReelUploadUrl($input: GetReelUploadUrlInput!) {
    getReelUploadUrl(input: $input) {
      url
      path
    }
  }
`;
export type GetReelUploadUrlMutationFn = Apollo.MutationFunction<
  GetReelUploadUrlMutation,
  GetReelUploadUrlMutationVariables
>;

/**
 * __useGetReelUploadUrlMutation__
 *
 * To run a mutation, you first call `useGetReelUploadUrlMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useGetReelUploadUrlMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [getReelUploadUrlMutation, { data, loading, error }] = useGetReelUploadUrlMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetReelUploadUrlMutation(
  baseOptions?: Apollo.MutationHookOptions<
    GetReelUploadUrlMutation,
    GetReelUploadUrlMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    GetReelUploadUrlMutation,
    GetReelUploadUrlMutationVariables
  >(GetReelUploadUrlDocument, options);
}
export type GetReelUploadUrlMutationHookResult = ReturnType<
  typeof useGetReelUploadUrlMutation
>;
export type GetReelUploadUrlMutationResult =
  Apollo.MutationResult<GetReelUploadUrlMutation>;
export type GetReelUploadUrlMutationOptions = Apollo.BaseMutationOptions<
  GetReelUploadUrlMutation,
  GetReelUploadUrlMutationVariables
>;
export const CreateReelDocument = gql`
  mutation CreateReel($input: CreateReelInput!) {
    createReel(input: $input) {
      reel {
        id
        caption
      }
    }
  }
`;
export type CreateReelMutationFn = Apollo.MutationFunction<
  CreateReelMutation,
  CreateReelMutationVariables
>;

/**
 * __useCreateReelMutation__
 *
 * To run a mutation, you first call `useCreateReelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateReelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createReelMutation, { data, loading, error }] = useCreateReelMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateReelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateReelMutation,
    CreateReelMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CreateReelMutation, CreateReelMutationVariables>(
    CreateReelDocument,
    options
  );
}
export type CreateReelMutationHookResult = ReturnType<
  typeof useCreateReelMutation
>;
export type CreateReelMutationResult =
  Apollo.MutationResult<CreateReelMutation>;
export type CreateReelMutationOptions = Apollo.BaseMutationOptions<
  CreateReelMutation,
  CreateReelMutationVariables
>;
export const ReserveDealDocument = gql`
  mutation ReserveDeal($input: ReserveDealInput!) {
    reserveDeal(input: $input) {
      myDeal {
        ...MyDeal
      }
    }
  }
  ${MyDealFragmentDoc}
`;
export type ReserveDealMutationFn = Apollo.MutationFunction<
  ReserveDealMutation,
  ReserveDealMutationVariables
>;

/**
 * __useReserveDealMutation__
 *
 * To run a mutation, you first call `useReserveDealMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useReserveDealMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [reserveDealMutation, { data, loading, error }] = useReserveDealMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useReserveDealMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ReserveDealMutation,
    ReserveDealMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<ReserveDealMutation, ReserveDealMutationVariables>(
    ReserveDealDocument,
    options
  );
}
export type ReserveDealMutationHookResult = ReturnType<
  typeof useReserveDealMutation
>;
export type ReserveDealMutationResult =
  Apollo.MutationResult<ReserveDealMutation>;
export type ReserveDealMutationOptions = Apollo.BaseMutationOptions<
  ReserveDealMutation,
  ReserveDealMutationVariables
>;
export const SocialLoginDocument = gql`
  mutation SocialLogin(
    $provider: SocialLoginProvider!
    $token: String!
    $firstName: String
    $lastName: String
  ) {
    socialLogin(
      provider: $provider
      token: $token
      firstName: $firstName
      lastName: $lastName
    ) {
      token
      user {
        ...User
      }
      status_code
    }
  }
  ${UserFragmentDoc}
`;
export type SocialLoginMutationFn = Apollo.MutationFunction<
  SocialLoginMutation,
  SocialLoginMutationVariables
>;

/**
 * __useSocialLoginMutation__
 *
 * To run a mutation, you first call `useSocialLoginMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSocialLoginMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [socialLoginMutation, { data, loading, error }] = useSocialLoginMutation({
 *   variables: {
 *      provider: // value for 'provider'
 *      token: // value for 'token'
 *      firstName: // value for 'firstName'
 *      lastName: // value for 'lastName'
 *   },
 * });
 */
export function useSocialLoginMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SocialLoginMutation,
    SocialLoginMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<SocialLoginMutation, SocialLoginMutationVariables>(
    SocialLoginDocument,
    options
  );
}
export type SocialLoginMutationHookResult = ReturnType<
  typeof useSocialLoginMutation
>;
export type SocialLoginMutationResult =
  Apollo.MutationResult<SocialLoginMutation>;
export type SocialLoginMutationOptions = Apollo.BaseMutationOptions<
  SocialLoginMutation,
  SocialLoginMutationVariables
>;
export const InviteUserDocument = gql`
  mutation InviteUser {
    inviteUser
  }
`;
export type InviteUserMutationFn = Apollo.MutationFunction<
  InviteUserMutation,
  InviteUserMutationVariables
>;

/**
 * __useInviteUserMutation__
 *
 * To run a mutation, you first call `useInviteUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useInviteUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [inviteUserMutation, { data, loading, error }] = useInviteUserMutation({
 *   variables: {
 *   },
 * });
 */
export function useInviteUserMutation(
  baseOptions?: Apollo.MutationHookOptions<
    InviteUserMutation,
    InviteUserMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<InviteUserMutation, InviteUserMutationVariables>(
    InviteUserDocument,
    options
  );
}
export type InviteUserMutationHookResult = ReturnType<
  typeof useInviteUserMutation
>;
export type InviteUserMutationResult =
  Apollo.MutationResult<InviteUserMutation>;
export type InviteUserMutationOptions = Apollo.BaseMutationOptions<
  InviteUserMutation,
  InviteUserMutationVariables
>;
export const AcceptInvitationDocument = gql`
  mutation AcceptInvitation($token: String!) {
    acceptInvitation(token: $token) {
      user {
        ...User
      }
      statusCode
    }
  }
  ${UserFragmentDoc}
`;
export type AcceptInvitationMutationFn = Apollo.MutationFunction<
  AcceptInvitationMutation,
  AcceptInvitationMutationVariables
>;

/**
 * __useAcceptInvitationMutation__
 *
 * To run a mutation, you first call `useAcceptInvitationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAcceptInvitationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [acceptInvitationMutation, { data, loading, error }] = useAcceptInvitationMutation({
 *   variables: {
 *      token: // value for 'token'
 *   },
 * });
 */
export function useAcceptInvitationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AcceptInvitationMutation,
    AcceptInvitationMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AcceptInvitationMutation,
    AcceptInvitationMutationVariables
  >(AcceptInvitationDocument, options);
}
export type AcceptInvitationMutationHookResult = ReturnType<
  typeof useAcceptInvitationMutation
>;
export type AcceptInvitationMutationResult =
  Apollo.MutationResult<AcceptInvitationMutation>;
export type AcceptInvitationMutationOptions = Apollo.BaseMutationOptions<
  AcceptInvitationMutation,
  AcceptInvitationMutationVariables
>;
export const AddFcmTokenDocument = gql`
  mutation AddFCMToken($token: String!) {
    addFCMToken(token: $token) {
      id
      fcm_token
    }
  }
`;
export type AddFcmTokenMutationFn = Apollo.MutationFunction<
  AddFcmTokenMutation,
  AddFcmTokenMutationVariables
>;

/**
 * __useAddFcmTokenMutation__
 *
 * To run a mutation, you first call `useAddFcmTokenMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddFcmTokenMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addFcmTokenMutation, { data, loading, error }] = useAddFcmTokenMutation({
 *   variables: {
 *      token: // value for 'token'
 *   },
 * });
 */
export function useAddFcmTokenMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AddFcmTokenMutation,
    AddFcmTokenMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<AddFcmTokenMutation, AddFcmTokenMutationVariables>(
    AddFcmTokenDocument,
    options
  );
}
export type AddFcmTokenMutationHookResult = ReturnType<
  typeof useAddFcmTokenMutation
>;
export type AddFcmTokenMutationResult =
  Apollo.MutationResult<AddFcmTokenMutation>;
export type AddFcmTokenMutationOptions = Apollo.BaseMutationOptions<
  AddFcmTokenMutation,
  AddFcmTokenMutationVariables
>;
export const ClearFcmTokenDocument = gql`
  mutation ClearFCMToken {
    clearFCMToken {
      id
    }
  }
`;
export type ClearFcmTokenMutationFn = Apollo.MutationFunction<
  ClearFcmTokenMutation,
  ClearFcmTokenMutationVariables
>;

/**
 * __useClearFcmTokenMutation__
 *
 * To run a mutation, you first call `useClearFcmTokenMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useClearFcmTokenMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [clearFcmTokenMutation, { data, loading, error }] = useClearFcmTokenMutation({
 *   variables: {
 *   },
 * });
 */
export function useClearFcmTokenMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ClearFcmTokenMutation,
    ClearFcmTokenMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    ClearFcmTokenMutation,
    ClearFcmTokenMutationVariables
  >(ClearFcmTokenDocument, options);
}
export type ClearFcmTokenMutationHookResult = ReturnType<
  typeof useClearFcmTokenMutation
>;
export type ClearFcmTokenMutationResult =
  Apollo.MutationResult<ClearFcmTokenMutation>;
export type ClearFcmTokenMutationOptions = Apollo.BaseMutationOptions<
  ClearFcmTokenMutation,
  ClearFcmTokenMutationVariables
>;
export const CollectionsDocument = gql`
  query Collections($collectableId: ID) {
    collections {
      ...CollectionItemCheck
    }
  }
  ${CollectionItemCheckFragmentDoc}
`;

/**
 * __useCollectionsQuery__
 *
 * To run a query within a React component, call `useCollectionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useCollectionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCollectionsQuery({
 *   variables: {
 *      collectableId: // value for 'collectableId'
 *   },
 * });
 */
export function useCollectionsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    CollectionsQuery,
    CollectionsQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<CollectionsQuery, CollectionsQueryVariables>(
    CollectionsDocument,
    options
  );
}
export function useCollectionsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    CollectionsQuery,
    CollectionsQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<CollectionsQuery, CollectionsQueryVariables>(
    CollectionsDocument,
    options
  );
}
export function useCollectionsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<
        CollectionsQuery,
        CollectionsQueryVariables
      >
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<CollectionsQuery, CollectionsQueryVariables>(
    CollectionsDocument,
    options
  );
}
export type CollectionsQueryHookResult = ReturnType<typeof useCollectionsQuery>;
export type CollectionsLazyQueryHookResult = ReturnType<
  typeof useCollectionsLazyQuery
>;
export type CollectionsSuspenseQueryHookResult = ReturnType<
  typeof useCollectionsSuspenseQuery
>;
export type CollectionsQueryResult = Apollo.QueryResult<
  CollectionsQuery,
  CollectionsQueryVariables
>;
export const CollectionDocument = gql`
  query Collection($id: ID!) {
    collection(id: $id) {
      ...FullCollection
    }
  }
  ${FullCollectionFragmentDoc}
`;

/**
 * __useCollectionQuery__
 *
 * To run a query within a React component, call `useCollectionQuery` and pass it any options that fit your needs.
 * When your component renders, `useCollectionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCollectionQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useCollectionQuery(
  baseOptions: Apollo.QueryHookOptions<
    CollectionQuery,
    CollectionQueryVariables
  > &
    (
      | { variables: CollectionQueryVariables; skip?: boolean }
      | { skip: boolean }
    )
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<CollectionQuery, CollectionQueryVariables>(
    CollectionDocument,
    options
  );
}
export function useCollectionLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    CollectionQuery,
    CollectionQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<CollectionQuery, CollectionQueryVariables>(
    CollectionDocument,
    options
  );
}
export function useCollectionSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<CollectionQuery, CollectionQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<CollectionQuery, CollectionQueryVariables>(
    CollectionDocument,
    options
  );
}
export type CollectionQueryHookResult = ReturnType<typeof useCollectionQuery>;
export type CollectionLazyQueryHookResult = ReturnType<
  typeof useCollectionLazyQuery
>;
export type CollectionSuspenseQueryHookResult = ReturnType<
  typeof useCollectionSuspenseQuery
>;
export type CollectionQueryResult = Apollo.QueryResult<
  CollectionQuery,
  CollectionQueryVariables
>;
export const CreatorDocument = gql`
  query Creator($id: ID!) {
    creator(id: $id) {
      ...Creator
    }
  }
  ${CreatorFragmentDoc}
`;

/**
 * __useCreatorQuery__
 *
 * To run a query within a React component, call `useCreatorQuery` and pass it any options that fit your needs.
 * When your component renders, `useCreatorQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCreatorQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useCreatorQuery(
  baseOptions: Apollo.QueryHookOptions<CreatorQuery, CreatorQueryVariables> &
    ({ variables: CreatorQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<CreatorQuery, CreatorQueryVariables>(
    CreatorDocument,
    options
  );
}
export function useCreatorLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<CreatorQuery, CreatorQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<CreatorQuery, CreatorQueryVariables>(
    CreatorDocument,
    options
  );
}
export function useCreatorSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<CreatorQuery, CreatorQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<CreatorQuery, CreatorQueryVariables>(
    CreatorDocument,
    options
  );
}
export type CreatorQueryHookResult = ReturnType<typeof useCreatorQuery>;
export type CreatorLazyQueryHookResult = ReturnType<typeof useCreatorLazyQuery>;
export type CreatorSuspenseQueryHookResult = ReturnType<
  typeof useCreatorSuspenseQuery
>;
export type CreatorQueryResult = Apollo.QueryResult<
  CreatorQuery,
  CreatorQueryVariables
>;
export const MyDealsDocument = gql`
  query myDeals {
    myDeals(first: 100) {
      data {
        ...MyDeal
      }
    }
  }
  ${MyDealFragmentDoc}
`;

/**
 * __useMyDealsQuery__
 *
 * To run a query within a React component, call `useMyDealsQuery` and pass it any options that fit your needs.
 * When your component renders, `useMyDealsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMyDealsQuery({
 *   variables: {
 *   },
 * });
 */
export function useMyDealsQuery(
  baseOptions?: Apollo.QueryHookOptions<MyDealsQuery, MyDealsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<MyDealsQuery, MyDealsQueryVariables>(
    MyDealsDocument,
    options
  );
}
export function useMyDealsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<MyDealsQuery, MyDealsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<MyDealsQuery, MyDealsQueryVariables>(
    MyDealsDocument,
    options
  );
}
export function useMyDealsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<MyDealsQuery, MyDealsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<MyDealsQuery, MyDealsQueryVariables>(
    MyDealsDocument,
    options
  );
}
export type MyDealsQueryHookResult = ReturnType<typeof useMyDealsQuery>;
export type MyDealsLazyQueryHookResult = ReturnType<typeof useMyDealsLazyQuery>;
export type MyDealsSuspenseQueryHookResult = ReturnType<
  typeof useMyDealsSuspenseQuery
>;
export type MyDealsQueryResult = Apollo.QueryResult<
  MyDealsQuery,
  MyDealsQueryVariables
>;
export const DealDocument = gql`
  query deal($id: ID!) {
    deal(id: $id) {
      ...FullDeal
    }
  }
  ${FullDealFragmentDoc}
`;

/**
 * __useDealQuery__
 *
 * To run a query within a React component, call `useDealQuery` and pass it any options that fit your needs.
 * When your component renders, `useDealQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDealQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDealQuery(
  baseOptions: Apollo.QueryHookOptions<DealQuery, DealQueryVariables> &
    ({ variables: DealQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<DealQuery, DealQueryVariables>(DealDocument, options);
}
export function useDealLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<DealQuery, DealQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<DealQuery, DealQueryVariables>(
    DealDocument,
    options
  );
}
export function useDealSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<DealQuery, DealQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<DealQuery, DealQueryVariables>(
    DealDocument,
    options
  );
}
export type DealQueryHookResult = ReturnType<typeof useDealQuery>;
export type DealLazyQueryHookResult = ReturnType<typeof useDealLazyQuery>;
export type DealSuspenseQueryHookResult = ReturnType<
  typeof useDealSuspenseQuery
>;
export type DealQueryResult = Apollo.QueryResult<DealQuery, DealQueryVariables>;
export const PlaceDocument = gql`
  query Place($id: ID!) {
    partnerPlace(id: $id) {
      ...FullPlace
    }
  }
  ${FullPlaceFragmentDoc}
`;

/**
 * __usePlaceQuery__
 *
 * To run a query within a React component, call `usePlaceQuery` and pass it any options that fit your needs.
 * When your component renders, `usePlaceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePlaceQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function usePlaceQuery(
  baseOptions: Apollo.QueryHookOptions<PlaceQuery, PlaceQueryVariables> &
    ({ variables: PlaceQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<PlaceQuery, PlaceQueryVariables>(
    PlaceDocument,
    options
  );
}
export function usePlaceLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<PlaceQuery, PlaceQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<PlaceQuery, PlaceQueryVariables>(
    PlaceDocument,
    options
  );
}
export function usePlaceSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<PlaceQuery, PlaceQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<PlaceQuery, PlaceQueryVariables>(
    PlaceDocument,
    options
  );
}
export type PlaceQueryHookResult = ReturnType<typeof usePlaceQuery>;
export type PlaceLazyQueryHookResult = ReturnType<typeof usePlaceLazyQuery>;
export type PlaceSuspenseQueryHookResult = ReturnType<
  typeof usePlaceSuspenseQuery
>;
export type PlaceQueryResult = Apollo.QueryResult<
  PlaceQuery,
  PlaceQueryVariables
>;
export const ReelStatusDocument = gql`
  query ReelStatus($ids: [ID!]!) {
    reelsMetaStatuses(ids: $ids) {
      id
      caption
      is_liked
      likes_count
      is_place_followed
      is_creator_followed
      is_place_in_collection
      saved_places_count
    }
  }
`;

/**
 * __useReelStatusQuery__
 *
 * To run a query within a React component, call `useReelStatusQuery` and pass it any options that fit your needs.
 * When your component renders, `useReelStatusQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useReelStatusQuery({
 *   variables: {
 *      ids: // value for 'ids'
 *   },
 * });
 */
export function useReelStatusQuery(
  baseOptions: Apollo.QueryHookOptions<
    ReelStatusQuery,
    ReelStatusQueryVariables
  > &
    (
      | { variables: ReelStatusQueryVariables; skip?: boolean }
      | { skip: boolean }
    )
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ReelStatusQuery, ReelStatusQueryVariables>(
    ReelStatusDocument,
    options
  );
}
export function useReelStatusLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    ReelStatusQuery,
    ReelStatusQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ReelStatusQuery, ReelStatusQueryVariables>(
    ReelStatusDocument,
    options
  );
}
export function useReelStatusSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<ReelStatusQuery, ReelStatusQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<ReelStatusQuery, ReelStatusQueryVariables>(
    ReelStatusDocument,
    options
  );
}
export type ReelStatusQueryHookResult = ReturnType<typeof useReelStatusQuery>;
export type ReelStatusLazyQueryHookResult = ReturnType<
  typeof useReelStatusLazyQuery
>;
export type ReelStatusSuspenseQueryHookResult = ReturnType<
  typeof useReelStatusSuspenseQuery
>;
export type ReelStatusQueryResult = Apollo.QueryResult<
  ReelStatusQuery,
  ReelStatusQueryVariables
>;
export const MeDocument = gql`
  query Me {
    me {
      ...User
    }
  }
  ${UserFragmentDoc}
`;

/**
 * __useMeQuery__
 *
 * To run a query within a React component, call `useMeQuery` and pass it any options that fit your needs.
 * When your component renders, `useMeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMeQuery({
 *   variables: {
 *   },
 * });
 */
export function useMeQuery(
  baseOptions?: Apollo.QueryHookOptions<MeQuery, MeQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<MeQuery, MeQueryVariables>(MeDocument, options);
}
export function useMeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<MeQuery, MeQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<MeQuery, MeQueryVariables>(MeDocument, options);
}
export function useMeSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<MeQuery, MeQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<MeQuery, MeQueryVariables>(
    MeDocument,
    options
  );
}
export type MeQueryHookResult = ReturnType<typeof useMeQuery>;
export type MeLazyQueryHookResult = ReturnType<typeof useMeLazyQuery>;
export type MeSuspenseQueryHookResult = ReturnType<typeof useMeSuspenseQuery>;
export type MeQueryResult = Apollo.QueryResult<MeQuery, MeQueryVariables>;
export const UserFollowedPlacesDocument = gql`
  query UserFollowedPlaces {
    me {
      ...UserFollowedPlaces
    }
  }
  ${UserFollowedPlacesFragmentDoc}
`;

/**
 * __useUserFollowedPlacesQuery__
 *
 * To run a query within a React component, call `useUserFollowedPlacesQuery` and pass it any options that fit your needs.
 * When your component renders, `useUserFollowedPlacesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUserFollowedPlacesQuery({
 *   variables: {
 *   },
 * });
 */
export function useUserFollowedPlacesQuery(
  baseOptions?: Apollo.QueryHookOptions<
    UserFollowedPlacesQuery,
    UserFollowedPlacesQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    UserFollowedPlacesQuery,
    UserFollowedPlacesQueryVariables
  >(UserFollowedPlacesDocument, options);
}
export function useUserFollowedPlacesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    UserFollowedPlacesQuery,
    UserFollowedPlacesQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    UserFollowedPlacesQuery,
    UserFollowedPlacesQueryVariables
  >(UserFollowedPlacesDocument, options);
}
export function useUserFollowedPlacesSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<
        UserFollowedPlacesQuery,
        UserFollowedPlacesQueryVariables
      >
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    UserFollowedPlacesQuery,
    UserFollowedPlacesQueryVariables
  >(UserFollowedPlacesDocument, options);
}
export type UserFollowedPlacesQueryHookResult = ReturnType<
  typeof useUserFollowedPlacesQuery
>;
export type UserFollowedPlacesLazyQueryHookResult = ReturnType<
  typeof useUserFollowedPlacesLazyQuery
>;
export type UserFollowedPlacesSuspenseQueryHookResult = ReturnType<
  typeof useUserFollowedPlacesSuspenseQuery
>;
export type UserFollowedPlacesQueryResult = Apollo.QueryResult<
  UserFollowedPlacesQuery,
  UserFollowedPlacesQueryVariables
>;
