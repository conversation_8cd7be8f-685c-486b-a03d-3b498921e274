fragment Collection on Collection {
  id
  title
  description
  items {
    id
    collectable {
      ... on PartnerPlace {
        id
        name
        avatar {
          full_url
        }
        partner {
          name
        }
      }
    }
  }
  myRole
}

fragment FullCollection on Collection {
  id
  title
  description
  items {
    id
    collectable {
      ... on PartnerPlace {
        ...PlaceWithDeals
      }
    }
  }
  myRole
}

fragment CollectionItemCheck on Collection {
  ...Collection
  is_item_exists(
    collectable_id: $collectableId
    collectable_type: PARTNER_LOCATION
  )
}
