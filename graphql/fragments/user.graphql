fragment MiniUser on User {
  id
  name
}

fragment User on User {
  ...MiniUser
  email
  creator {
    id
  }
  status
  fcm_token
  expire_at
  created_at
  invitations {
    info {
      remaining
      total
    }
  }
  stats {
    amount_saved
    deals_completed
    places_visited
    reviews_written
  }
}

fragment UserFollowing on User {
  following_places(first: 100) {
    paginatorInfo {
      total
    }
    data {
      ...MiniPlace
    }
  }
  following_creators(first: 100) {
    paginatorInfo {
      total
    }
    data {
      ...Creator
    }
  }
}
