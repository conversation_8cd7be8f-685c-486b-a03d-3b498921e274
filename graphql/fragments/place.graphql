fragment Partner on Partner {
  id
  name
  description
  email
  instagram
  facebook
  twitter
  website
  partner_places(first: 10) {
    data {
      ...PlaceInsidePartner
    }
  }
}

fragment PlaceInsidePartner on PartnerPlace {
  id
  name
  address_line_1
  address_line_2
  phone
  location {
    lat
    lng
  }
}

fragment Place on PartnerPlace {
  id
  name
  price_per_person
  rates {
    google
    reviews_count
  }
  opening_hours {
    day
    from
    to
  }
  partner {
    ...Partner
  }
  tags {
    id
    title
  }
  images {
    full_url
  }
  menu {
    full_url
  }
  menu_url
  location {
    lat
    lng
  }
  avatar {
    full_url
  }
}

fragment PlaceTags on PartnerPlace {
  ambiance {
    title
  }
  parking {
    title
  }
  specialities {
    title
  }
  cuisine_types {
    title
  }
  dietary {
    title
  }
  service_options {
    title
  }
  meal_times {
    title
  }
  cravings {
    title
  }
}

fragment FullPlace on PartnerPlace {
  ...Place
  deals(first: 100) {
    data {
      ...DealWithMyDeals
    }
  }
  reels(first: 20) {
    data {
      ...Reel
    }
  }
  ...PlaceTags
  is_place_in_collection
}

fragment PlaceWithDeals on PartnerPlace {
  ...Place
  ...PlaceTags
  deals(first: 100) {
    data {
      ...DealWithOutPlaces
    }
  }
}
fragment Mini<PERSON>lace on PartnerPlace {
  id
  name
  avatar {
    full_url
  }
  partner {
    id
    name
  }
}
