fragment DealWithOutPlaces on Deal {
  id
  title
  description
  deal_type
  max_saving
  reuse_limit_days
  service_types {
    title
  }
}

fragment Deal on Deal {
  ...DealWithOutPlaces
  partner_place {
    ...Place
  }
}

fragment DealWithMyDeals on Deal {
  ...DealWithOutPlaces
  myDeals(first: 1) {
    data {
      ...MyDealWithoutDeal
    }
  }
}

fragment FullDeal on Deal {
  ...Deal
  available_slots {
    date
    available_seats
    slots {
      from
      to
    }
  }
}

fragment MyDeal on MyDeal {
  id
  deal {
    ...Deal
  }
  status
  reserved_at
  redeemed_at
  reuse_after
  reserve_slot {
    date
    slot {
      from
      to
    }
  }
}

fragment MyDealWithoutDeal on MyDeal {
  id
  status
  reserved_at
  redeemed_at
  reuse_after
  reserve_slot {
    date
    slot {
      from
      to
    }
  }
}
