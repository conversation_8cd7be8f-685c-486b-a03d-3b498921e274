fragment Deal on Deal {
  id
  title
  description
  deal_type
  max_saving
  reuse_limit_days
  is_one_time
  service_types {
    title
  }
  partner_place {
    ...MiniPlace
  }
}

fragment DealWithCanReview on Deal {
  ...Deal
  can_review
}

fragment DealWithMyDeals on Deal {
  ...DealWithCanReview
  myDeals(first: 1) {
    data {
      ...MyDealWithoutDeal
    }
  }
}

fragment FullDeal on Deal {
  ...Deal
  available_slots {
    date
    available_seats
    slots {
      from
      to
    }
  }
}

fragment MyDeal on MyDeal {
  id
  deal {
    ...DealWithCanReview
  }
  status
  reserved_at
  redeemed_at
  reuse_after
  reserve_slot {
    date
    slot {
      from
      to
    }
  }
}

fragment MyDealWithoutDeal on MyDeal {
  id
  status
  reserved_at
  redeemed_at
  reuse_after
  reserve_slot {
    date
    slot {
      from
      to
    }
  }
}
