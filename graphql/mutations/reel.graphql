mutation Like($id: ID!, $type: LikableType!) {
  like(input: { id: $id, type: $type }) {
    status
    message
    likeable {
      ... on PartnerPlace {
        name
      }
      ... on Creator {
        user {
          id
        }
      }
      ... on Reel {
        caption
        full_url
      }
    }
  }
}

mutation GetReelUploadUrl($input: GetReelUploadUrlInput!) {
  getReelUploadUrl(input: $input) {
    url
    path
  }
}

mutation CreateReel($input: CreateReelInput!) {
  createReel(input: $input) {
    reel {
      id
      caption
    }
  }
}
