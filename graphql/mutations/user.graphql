mutation SocialLogin(
  $provider: SocialLoginProvider!
  $token: String!
  $firstName: String
  $lastName: String
) {
  socialLogin(
    provider: $provider
    token: $token
    firstName: $firstName
    lastName: $lastName
  ) {
    token
    user {
      ...User
    }
    status_code
  }
}

mutation InviteUser {
  inviteUser
}

mutation AcceptInvitation($token: String!) {
  acceptInvitation(token: $token) {
    user {
      ...User
    }
    statusCode
  }
}

mutation AddFCMToken($token: String!) {
  addFCMToken(token: $token) {
    id
    fcm_token
  }
}

mutation ClearFCMToken {
  clearFCMToken {
    id
  }
}
