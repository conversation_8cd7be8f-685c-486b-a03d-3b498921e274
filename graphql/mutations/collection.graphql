mutation CreateCollection($input: CreateCollectionInput!) {
  createCollection(input: $input) {
    ...Collection
  }
}

mutation EditCollection($input: EditCollectionInput!) {
  editCollection(input: $input) {
    ...Collection
  }
}

mutation DeleteCollection($id: ID!) {
  deleteCollection(id: $id)
}

mutation AddItemToCollection($input: AddItemToCollectionInput!) {
  addItemToCollection(input: $input) {
    ...Collection
  }
}

mutation RemoveItemFromCollection($input: RemoveItemFromCollectionInput!) {
  removeItemFromCollection(input: $input)
}

mutation RemoveItemFromAllCollections($input: ID!) {
  removeItemFromAllCollections(
    input: { collectable_id: $input, collectable_type: PARTNER_LOCATION }
  )
}
