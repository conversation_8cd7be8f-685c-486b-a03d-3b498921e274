import { StyleSheet, View, RefreshControl, FlatList } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { moderateScale } from '@/utils/scaling';
import { Deal } from '@/components/ui/Deal';
import { useState, useMemo, useEffect } from 'react';
import { DealFilters } from '@/components/ui/DealFilters';
import { useTranslation } from 'react-i18next';
import { Image } from 'expo-image';
import {
  useMyDealsQuery,
  MyDealStatusEnum,
  MyDeal as GraphQLMyDeal,
} from '@/graphql/generated/graphql';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/Colors';

export default function DealsScreen() {
  const { t } = useTranslation();
  const { data, loading, error, refetch } = useMyDealsQuery();
  const params = useLocalSearchParams<{ activeFilter?: string }>();

  const [activeFilter, setActiveFilter] = useState<MyDealStatusEnum>(
    MyDealStatusEnum.Redeemable
  );
  const [refreshing, setRefreshing] = useState(false);

  const counts = useMemo(
    () => ({
      redeemable:
        data?.myDeals?.data?.filter(
          (deal) => deal.status === MyDealStatusEnum.Redeemable
        ).length || 0,
      upcoming:
        data?.myDeals?.data?.filter(
          (deal) => deal.status === MyDealStatusEnum.Upcoming
        ).length || 0,
      redeemed:
        data?.myDeals?.data?.filter(
          (deal) =>
            deal.status === MyDealStatusEnum.Redeemed ||
            deal.status === MyDealStatusEnum.NoShow
        ).length || 0,
    }),
    [data?.myDeals]
  );

  // Helper function to get the first non-empty filter
  const getFirstNonEmptyFilter = (): MyDealStatusEnum => {
    if (counts.redeemable > 0) return MyDealStatusEnum.Redeemable;
    if (counts.upcoming > 0) return MyDealStatusEnum.Upcoming;
    if (counts.redeemed > 0) return MyDealStatusEnum.Redeemed;
    // If all are empty, default to Redeemable
    return MyDealStatusEnum.Redeemable;
  };

  // Update activeFilter when params change or data loads
  useEffect(() => {
    if (params.activeFilter) {
      // Validate if the received filter is a valid enum value
      const filterValue = params.activeFilter as MyDealStatusEnum;
      if (Object.values(MyDealStatusEnum).includes(filterValue)) {
        setActiveFilter(filterValue);
      }
    } else if (data?.myDeals) {
      // No param provided, set to first non-empty group
      const firstNonEmptyFilter = getFirstNonEmptyFilter();
      setActiveFilter(firstNonEmptyFilter);
    }
  }, [params.activeFilter, data?.myDeals, counts]);

  // Handle pull to refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing deals:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const filteredDeals = useMemo(() => {
    if (!data?.myDeals) return [];
    // For Redeemed/NoShow deals
    if (activeFilter === MyDealStatusEnum.Redeemed) {
      return data.myDeals.data.filter(
        (deal) =>
          deal.status === MyDealStatusEnum.Redeemed ||
          deal.status === MyDealStatusEnum.NoShow
      );
    }
    return data.myDeals.data.filter((deal) => deal.status === activeFilter);
  }, [activeFilter, data?.myDeals]);

  // Render header component
  const renderHeader = () => (
    <View style={styles.header}>
      <Text variant='h2' align='center'>
        {t('deals.title')}
      </Text>
      <DealFilters
        activeFilter={activeFilter}
        onFilterChange={setActiveFilter}
        counts={counts}
      />
    </View>
  );

  // Render empty component
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Image
        source={require('@/assets/images/empty-deals.png')}
        style={styles.emptyImage}
      />
      <Text variant='body' align='center' style={styles.emptyText}>
        {t(`deals.empty.${activeFilter.toLowerCase()}.title`)}
      </Text>
      <Text variant='caption' align='center' style={styles.emptySubtext}>
        {t(`deals.empty.${activeFilter.toLowerCase()}.message`)}
      </Text>
    </View>
  );

  // Render deal item
  const renderDeal = ({
    item,
    index,
  }: {
    item: GraphQLMyDeal;
    index: number;
  }) => <Deal key={item.id || index} myDeal={item} isInDealsScreen={true} />;

  if (error) {
    return <ErrorScreen message={t('common.notFound')} />;
  }

  return (
    <Screen isLoading={loading}>
      <FlatList
        data={filteredDeals as GraphQLMyDeal[]}
        renderItem={renderDeal}
        keyExtractor={(item, index) => item.id || index.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={!loading ? renderEmpty : null}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[950]}
            colors={[colors.primary[950]]}
          />
        }
      />
    </Screen>
  );
}

const styles = StyleSheet.create({
  header: {
    marginTop: moderateScale(24),
    marginBottom: moderateScale(16),
    gap: moderateScale(24),
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: moderateScale(16),
    paddingBottom: moderateScale(100),
  },
  emptyContainer: {
    alignItems: 'center',
    width: moderateScale(193.79),
    height: moderateScale(144),
    alignSelf: 'center',
    marginTop: moderateScale(100),
  },
  emptyImage: {
    width: '100%',
    height: '100%',
  },
  emptyText: {
    width: '140%',
    marginTop: moderateScale(16),
    fontWeight: 'bold',
  },
  emptySubtext: {
    width: '140%',
    marginTop: moderateScale(8),
  },
});
