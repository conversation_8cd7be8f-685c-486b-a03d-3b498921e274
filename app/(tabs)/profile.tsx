import { colors } from '@/constants/Colors';
import { useAuthStore } from '@/store/auth';
import {
  StyleSheet,
  View,
  ScrollView,
  Alert,
  Pressable,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Image } from 'expo-image';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { SettingsItem } from '@/components/ui/SettingsItem';
import * as Application from 'expo-application';
import { useTranslation } from 'react-i18next';
import {
  useMeQuery,
  useUserFollowedPlacesQuery,
} from '@/graphql/generated/graphql';
import { TextButton } from '@/components/ui/TextButton';
import { router } from 'expo-router';
import { useMemo } from 'react';
import { addMonths, differenceInDays, format } from 'date-fns';
import { Tooltip } from '@/components/ui/Tooltip';
import { InviteModal } from '@/components/InviteModal';
import { DevModeModal } from '@/components/ui/DevModeModal';
import { useDevMode } from '@/hooks/useDevMode';
import { useLogout } from '@/hooks/useLogout';

const FAQ_URL = 'https://conari.app/faq';
const TERMS_URL = 'https://conari.app/user-terms';
const PRIVACY_URL = 'https://conari.app/privacy';
const BECOME_CREATOR_URL = 'https://conari.app/creator-application';
const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function ProfileScreen() {
  const logout = useLogout();
  const { isTrialModalOpened } = useAuthStore();
  const MONTHS_IN_TRIAL = 6;

  const { t } = useTranslation();

  //fetch user info
  const { data: userData } = useMeQuery();
  const user = userData?.me;
  // Dev mode hook
  const {
    currentDevUrl,
    isStaging,
    devModeModalRef,
    handleDevModeClick,
    handleDevModeSetUrl,
    handleDevModeResetUrl,
    handleDevModeCancel,
    getDisplayUrl,
  } = useDevMode();

  // Fetch user's followed places
  const { data: followedPlacesData, loading } = useUserFollowedPlacesQuery();
  const followedPlaces = followedPlacesData?.me?.following_places?.data || [];

  // Calculate trial information
  const trialInfo = useMemo(() => {
    if (!user?.created_at || !isTrialModalOpened) {
      return null;
    }

    const trialStartDate = new Date(user.created_at);
    const trialEndDate = addMonths(trialStartDate, MONTHS_IN_TRIAL);

    const now = new Date();
    const timeRemaining = differenceInDays(trialEndDate, now);
    const daysRemaining = Math.max(0, timeRemaining);

    const isTrialActive = timeRemaining > 0;

    return {
      startDate: trialStartDate,
      endDate: trialEndDate,
      daysRemaining: Math.max(0, daysRemaining),
      isActive: isTrialActive,
      formattedEndDate: format(trialEndDate, 'MMM d, yyyy'),
    };
  }, [user?.created_at, isTrialModalOpened]);

  // Navigate to place details
  const navigateToPlace = (placeId: string) => {
    router.push(`/places/${placeId}`);
  };

  // Navigate to WebView for external links
  const openInWebView = (url: string, title: string) => {
    router.push({
      pathname: '/webview',
      params: { url, title },
    });
  };

  // Handle trial info click
  const handleTrialInfoClick = () => {
    if (!trialInfo) return;

    const message = trialInfo.isActive
      ? t('trial.infoMessage', { endDate: trialInfo.formattedEndDate })
      : t('trial.infoMessageExpired', { endDate: trialInfo.formattedEndDate });

    Alert.alert(t('trial.infoTitle'), message, [
      { text: t('common.ok'), style: 'default' },
    ]);
  };

  //show confirm dialog when user press logout button
  const onLogout = () => {
    Alert.alert(t('profile.logout'), t('profile.logoutConfirmation'), [
      { text: t('common.cancel'), style: 'cancel' },
      { text: t('profile.logout'), onPress: logout },
    ]);
  };

  // const user = useAuthStore((state) => state.user);
  const version = Application.nativeApplicationVersion;
  const buildNumber = Application.nativeBuildVersion;

  return (
    <Screen style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text variant='h2' align='center'>
              {t('profile.profile')}
            </Text>
          </View>

          {/* Following Places Section */}
          {(loading || followedPlaces.length > 0) && (
            <View style={styles.fullWidthSection}>
              <View style={styles.sectionTitleContainer}>
                <Text variant='h3' style={styles.sectionTitle}>
                  {t('place.followingPlaces')}
                </Text>
              </View>

              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size='small' color={colors.primary[950]} />
                </View>
              ) : (
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.followedPlacesContainer}
                >
                  {followedPlaces.map((place) => (
                    <Pressable
                      key={place.id}
                      style={styles.followedPlaceItem}
                      onPress={() => navigateToPlace(place.id)}
                    >
                      <View style={styles.followedPlaceImageContainer}>
                        {place.avatar?.full_url ? (
                          <Image
                            source={{ uri: place.avatar.full_url }}
                            style={styles.followedPlaceImage}
                          />
                        ) : (
                          <View style={styles.followedPlaceImagePlaceholder}>
                            <IconSymbol
                              name='building.2'
                              size={24}
                              color={colors.darkGrey[500]}
                            />
                          </View>
                        )}
                      </View>
                      <Text
                        variant='tiny'
                        style={styles.followedPartnerName}
                        numberOfLines={1}
                      >
                        {place.partner.name}
                      </Text>
                      <Text
                        variant='tiny'
                        style={styles.followedPlaceName}
                        numberOfLines={1}
                        color={colors.darkGrey[500]}
                      >
                        {place.name}
                      </Text>
                    </Pressable>
                  ))}
                </ScrollView>
              )}
            </View>
          )}

          <Tooltip content={<InviteModal user={user} />}>
            {/* send invite clickable banner  */}
            <View style={styles.sendInviteBanner}>
              <View style={styles.sendInviteBannerContent}>
                <Text variant='caption1' color={colors.base[100]}>
                  {t('profile.invite.bannerText1')}
                </Text>
                <Text variant='caption1' color={colors.base[100]}>
                  {t('profile.invite.bannerText2')}
                </Text>

                {/* send an invite now  Text + arrow */}
                <View style={styles.sendInviteBannerArrowContainer}>
                  <Text
                    variant='caption'
                    color={colors.base[100]}
                    weight='bold'
                  >
                    {t('profile.invite.bannerButton')}
                  </Text>
                </View>
              </View>
              <View style={styles.sendInviteBannerImageContainer}>
                <Image
                  source={require('@/assets/images/invite.png')}
                  style={styles.sendInviteBannerImage}
                  contentFit='contain'
                />
              </View>
            </View>
          </Tooltip>

          {/* <View style={styles.section}>
            <Text variant='h3' style={styles.sectionTitle}>
              General Settings
            </Text>
            <SettingsItem icon='person.circle' label='Personal Information' />
            <SettingsItem icon='lock' label='Password & Security' />
            <SettingsItem icon='bell.badge' label='Notification Preferences' />
            <SettingsItem icon='heart' label='Following List' />
          </View> */}

          <View style={styles.section}>
            {/* <Text variant='h3' style={styles.sectionTitle}>
              Others
            </Text> */}
            {/* terms and conditions */}
            <SettingsItem
              icon='questionmark.circle'
              label={t('profile.faq')}
              onPress={() => openInWebView(FAQ_URL, t('profile.faq'))}
            />
            <SettingsItem
              icon='doc.text'
              label={t('profile.termsAndConditions')}
              onPress={() =>
                openInWebView(TERMS_URL, t('profile.termsAndConditions'))
              }
            />
            <SettingsItem
              icon='lock.shield'
              label={t('profile.privacyPolicy')}
              onPress={() =>
                openInWebView(PRIVACY_URL, t('profile.privacyPolicy'))
              }
            />
            <SettingsItem
              icon='person.crop.circle'
              label={t('profile.accountManagement')}
              onPress={() => router.push('/accountManagement')}
            />
            <SettingsItem
              icon='star.circle'
              label={t('profile.becomeCreator')}
              onPress={() =>
                openInWebView(BECOME_CREATOR_URL, t('profile.becomeCreator'))
              }
            />

            {/* Trial Status Section */}
            {user && trialInfo && (
              <SettingsItem
                icon='crown'
                label={t('trial.premiumAccess')}
                rightElement={
                  <Text style={styles.version}>
                    {t('trial.daysLeft', { days: trialInfo.daysRemaining })}
                  </Text>
                }
                onPress={handleTrialInfoClick}
              />
            )}

            <SettingsItem
              icon='app.badge'
              label={t('profile.appVersion')}
              rightElement={
                <Text style={styles.version}>
                  {t('profile.v')} {version} ({buildNumber})
                </Text>
              }
            />
            {/* Dev Mode Section - Only show in staging */}
            {isStaging && (
              <SettingsItem
                icon='hammer'
                label='Dev Mode'
                rightElement={
                  <Text style={styles.version}>
                    {currentDevUrl ? 'Custom URL' : 'Default'}
                  </Text>
                }
                onPress={handleDevModeClick}
              />
            )}
          </View>

          <View style={styles.footer}>
            {/* logout button */}
            <TextButton onPress={onLogout} variant='secondary'>
              {t('profile.logout')}
            </TextButton>
          </View>
        </View>
      </ScrollView>

      {/* Dev Mode Modal */}
      <DevModeModal
        ref={devModeModalRef}
        currentUrl={getDisplayUrl()}
        onSetUrl={handleDevModeSetUrl}
        onResetUrl={handleDevModeResetUrl}
        onCancel={handleDevModeCancel}
      />
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.softGrey[800],
  },
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(20),
    marginBottom: moderateScale(100),
  },
  header: {
    marginTop: moderateScale(24),
    marginBottom: moderateScale(16),
  },
  section: {
    marginTop: moderateScale(32),
  },
  fullWidthSection: {
    marginTop: moderateScale(32),
    marginHorizontal: -moderateScale(20),
    width: SCREEN_WIDTH,
  },
  sectionTitleContainer: {
    paddingHorizontal: moderateScale(20),
    marginBottom: moderateScale(16),
  },
  sectionTitle: {
    marginVertical: moderateScale(8),
  },
  version: {
    color: colors.darkGrey[300],
    fontSize: moderateScale(14),
  },
  footer: {
    marginTop: moderateScale(32),
  },
  followedPlacesContainer: {
    paddingHorizontal: moderateScale(20),
    paddingBottom: moderateScale(16),
  },
  followedPlaceItem: {
    marginRight: moderateScale(16),
    alignItems: 'center',
    width: moderateScale(80),
  },
  followedPlaceImageContainer: {
    width: moderateScale(80),
    height: moderateScale(80),
    borderRadius: moderateScale(40),
    marginBottom: moderateScale(8),
    position: 'relative',
    overflow: 'visible',
    shadowColor: colors.base[950],
    shadowOffset: { width: 2, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 2.84,
    elevation: 5,
  },
  followedPlaceImage: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(40),
    backgroundColor: colors.softGrey[400],
  },
  followedPlaceImagePlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(40),
    backgroundColor: colors.softGrey[400],
    alignItems: 'center',
    justifyContent: 'center',
  },
  followedPlaceName: {
    textAlign: 'center',
    maxWidth: moderateScale(80),
    fontSize: moderateScale(8),
  },
  followedPartnerName: {
    textAlign: 'center',
    maxWidth: moderateScale(80),
  },
  loadingContainer: {
    height: moderateScale(120),
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendInviteBanner: {
    backgroundColor: colors.primary[950],
    height: moderateScale(97),
    borderRadius: moderateScale(16),
    marginTop: moderateScale(32),
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  sendInviteBannerContent: {
    flex: 1,
    height: '100%',
    paddingVertical: verticalScale(16),
    paddingLeft: moderateScale(16),
  },
  sendInviteBannerImageContainer: {
    paddingRight: moderateScale(16),
    paddingLeft: moderateScale(2),
    // backgroundColor: 'red',
  },
  sendInviteBannerImage: {
    width: moderateScale(136),
    height: verticalScale(107),
  },
  sendInviteBannerArrowContainer: {
    marginTop: verticalScale(10),
  },
});
