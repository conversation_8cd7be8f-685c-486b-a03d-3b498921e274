import { StyleSheet, View, FlatList } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { Button } from '@/components/ui/Button';
import { CollectionCard } from '@/components/CollectionCard';
import { useCollectionsQuery } from '@/graphql/generated/graphql';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Text } from '@/components/ui/Text';
import { Image } from 'expo-image';

export default function CollectionsScreen() {
  const { data, loading } = useCollectionsQuery();
  const router = useRouter();
  const { t } = useTranslation();

  const handleNewCollection = () => {
    router.push('/(modals)/collection');
  };

  // Create combined data array with collections and new collection item
  const combinedData = data?.collections
    ? [...data.collections, { id: 'new-collection', isNew: true }]
    : [];

  const renderItem = ({ item }: { item: any }) => {
    if (item.isNew) {
      return <CollectionCard isNew onPress={handleNewCollection} />;
    }
    return <CollectionCard collection={item} />;
  };

  const renderEmptyComponent = () => {
    if (loading) return null;

    return (
      <View style={styles.emptyContainer}>
        <Image
          source={require('@/assets/images/noCollection.png')}
          style={styles.emptyImage}
          contentFit='contain'
        />
        <Text variant='h1' align='center' style={styles.emptyText}>
          {t(`collections.emptyState`)}
        </Text>
        <Text variant='caption' align='center' style={styles.emptySubtext}>
          {t(`collections.emptyStateSubtext`)}
        </Text>
        <Button
          onPress={handleNewCollection}
          title={t(`collections.newCollection`)}
          variant='outline'
          style={styles.newCollectionButton}
        />
      </View>
    );
  };

  return (
    <Screen isLoading={loading}>
      {data?.collections && data?.collections?.length > 0 ? (
        <View>
          <View style={styles.header}>
            <Text variant='h2' align='center'>
              {t('collections.title')}
            </Text>
          </View>

          <FlatList
            data={combinedData}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            columnWrapperStyle={styles.row}
            contentContainerStyle={styles.container}
          />
        </View>
      ) : (
        renderEmptyComponent()
      )}
    </Screen>
  );
}

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: moderateScale(20),
    marginTop: moderateScale(24),
    marginBottom: moderateScale(16),
  },
  container: {
    padding: moderateScale(16),
    paddingBottom: moderateScale(100),
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: moderateScale(12),
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    width: '80%',
    alignSelf: 'center',
    marginTop: moderateScale(140),
  },
  emptyImage: {
    width: moderateScale(274),
    height: verticalScale(220),
  },
  emptyText: {
    width: '80%',
    marginTop: moderateScale(40),
  },
  emptySubtext: {
    width: '75%',
    marginTop: moderateScale(24),
    marginBottom: moderateScale(8),
  },
  newCollectionButton: {
    marginTop: moderateScale(24),
    width: moderateScale(200),
  },
});
