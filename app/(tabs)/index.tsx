import React, { useState } from 'react';
import { liteClient as algoliasearch } from 'algoliasearch/lite';
import { InstantSearch } from 'react-instantsearch-core';
import { HomeContent, InsightsMiddleware } from '@/components/home';
import { useNotifications } from '@/hooks/useNotifications';
import Constants from 'expo-constants';
import { analyticsConfig } from '@/config/analytics';

const { ALGOLIA_APP_ID, ALGOLIA_SEARCH_KEY } = Constants.expoConfig?.extra as {
  ALGOLIA_APP_ID: string;
  ALGOLIA_SEARCH_KEY: string;
};

export default function HomeScreen() {
  const [currentIndex, setCurrentIndex] = useState('reels');
  useNotifications();

  if (!ALGOLIA_APP_ID || !ALGOLIA_SEARCH_KEY) {
    throw new Error('ALGOLIA_APP_ID and ALGOLIA_SEARCH_KEY must be set');
  }

  const searchClient = algoliasearch(ALGOLIA_APP_ID, ALGOLIA_SEARCH_KEY);

  return (
    <InstantSearch
      searchClient={searchClient}
      indexName={currentIndex}
      insights={false} // Disable insights at the InstantSearch level
    >
      <HomeContent
        setCurrentIndex={setCurrentIndex}
        currentIndex={currentIndex}
      />
      {/* Include InsightsMiddleware based on analytics config */}
      {analyticsConfig.isEnabled && <InsightsMiddleware />}
    </InstantSearch>
  );
}
