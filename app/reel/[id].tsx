import React, { useEffect, useState, useRef } from 'react';
import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { liteClient as algoliasearch } from 'algoliasearch/lite';
import { colors } from '../../constants/Colors';
import { Screen } from '../../components/ui/Screen';
import Reel from '../../components/Reel';
import { BackButton } from '../../components/ui/BackButton';
import type { Reel as ReelType } from '../../schemas/reel';
import { useIsFocused } from '@react-navigation/native';
import { verticalScale } from '@/utils/scaling';
import Constants from 'expo-constants';

const { ALGOLIA_APP_ID, ALGOLIA_SEARCH_KEY } = Constants.expoConfig?.extra as {
  ALGOLIA_APP_ID: string;
  ALGOLIA_SEARCH_KEY: string;
};

// Define search result type
interface AlgoliaSearchResults {
  results: {
    hits: any[];
  }[];
}

export default function ReelScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [reel, setReel] = useState<ReelType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isMuted, setIsMuted] = useState(false);
  const isFocused = useIsFocused();
  const mounted = useRef(true);

  useEffect(() => {
    mounted.current = true;

    return () => {
      mounted.current = false;
    };
  }, []);

  useEffect(() => {
    if (!id) {
      router.replace('/(tabs)');
      return;
    }

    // Create Algolia client
    if (!ALGOLIA_APP_ID || !ALGOLIA_SEARCH_KEY) {
      setError(new Error('Algolia configuration missing'));
      setLoading(false);
      return;
    }

    const searchClient = algoliasearch(ALGOLIA_APP_ID, ALGOLIA_SEARCH_KEY);

    // Fetch the specific reel by ID
    const fetchReel = async () => {
      try {
        // Use search with a filter for the specific objectID
        const searchResponse = (await searchClient.search([
          {
            indexName: 'reels',
            params: {
              filters: `objectID:${id}`,
              query: '',
              getRankingInfo: true,
            },
          },
        ])) as AlgoliaSearchResults;

        if (mounted.current) {
          if (searchResponse.results[0].hits.length > 0) {
            setReel(searchResponse.results[0].hits[0] as ReelType);
          } else {
            setError(new Error('Reel not found'));
          }
          setLoading(false);
        }
      } catch (err) {
        console.error('Error fetching reel:', err);
        if (mounted.current) {
          setError(err instanceof Error ? err : new Error('Unknown error'));
          setLoading(false);
        }
      }
    };

    fetchReel();

    return () => {
      // Clean up any async operations here
    };
  }, [id, router]);

  const handleBack = () => {
    router.push('/(tabs)');
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  if (loading) {
    return (
      <Screen style={styles.container}>
        <BackButton onPress={handleBack} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={colors.primary[950]} />
        </View>
      </Screen>
    );
  }

  if (error || !reel) {
    return (
      <Screen style={styles.container}>
        <BackButton onPress={handleBack} />
        <View style={styles.errorContainer}>
          <View style={styles.messageContainer}>
            <ActivityIndicator size='large' color={colors.primary[950]} />
            <Text style={styles.errorText}>
              {error?.message || 'Reel not found'}
            </Text>
          </View>
        </View>
      </Screen>
    );
  }

  return (
    <View style={styles.container}>
      <BackButton onPress={handleBack} absolute transparent />
      <View style={styles.reelContainer}>
        {/* Only render the reel component when the screen is focused */}
        {isFocused && (
          <Reel
            reel={reel}
            index={0}
            isVisible={isFocused}
            isStandalone={true}
            isMuted={isMuted}
            toggleMute={toggleMute}
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.base[950],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageContainer: {
    padding: 20,
    borderRadius: 10,
    backgroundColor: colors.base[950],
    alignItems: 'center',
  },
  errorText: {
    color: colors.base[100],
    marginTop: 10,
  },
  reelContainer: {
    paddingTop: verticalScale(40),
  },
});
