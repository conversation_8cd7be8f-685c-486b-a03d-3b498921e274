import { colors } from '@/constants/Colors';
import { StyleSheet, View, ScrollView, Alert } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { moderateScale } from '@/utils/scaling';
import { SettingsItem } from '@/components/ui/SettingsItem';
import { useTranslation } from 'react-i18next';
import { BackButton } from '@/components/ui/BackButton';
import { router } from 'expo-router';
import { useLogout } from '@/hooks/useLogout';

export default function ProfileScreen() {
  const logout = useLogout();
  const { t } = useTranslation();
  //show confirm dialog when user press delete account button
  const onDeleteAccount = () => {
    Alert.alert(
      t('profile.deleteAccount'),
      t('profile.deleteAccountConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('profile.deleteAccount'), onPress: logout },
      ]
    );
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <Screen style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <BackButton onPress={handleBack} />
        </View>

        <View style={styles.content}>
          <SettingsItem
            icon='trash'
            label={t('profile.deleteAccount')}
            onPress={onDeleteAccount}
          />
        </View>
      </ScrollView>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.softGrey[800],
  },
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(20),
    marginBottom: moderateScale(100),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(16),
  },
});
