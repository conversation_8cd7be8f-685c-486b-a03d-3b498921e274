import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Modal,
} from 'react-native';
import { Image } from 'expo-image';
import { Text } from '@/components/ui/Text';
import { useLocalSearchParams, useRouter, useNavigation } from 'expo-router';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import {
  useCreatorQuery,
  useFollowCreatorMutation,
  Creator,
} from '@/graphql/generated/graphql';
import { Screen } from '@/components/ui/Screen';
import { BackButton } from '@/components/ui/BackButton';
import { RightHeaderButton } from '@/components/ui/RightHeaderButton';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ReelThumbnail } from '@/components/ReelThumbnail';
import { useTranslation } from 'react-i18next';
import { FollowButton } from '@/components/ui/FollowButton';
import FontAwesome from '@expo/vector-icons/build/FontAwesome5';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useAuthStore } from '@/store/auth';
import { shareCreator } from '@/services/branch';
import { useApolloClient } from '@apollo/client';

export default function CreatorScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { t } = useTranslation();
  const router = useRouter();
  const navigation = useNavigation();
  const client = useApolloClient();
  const [isFollowing, setIsFollowing] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const { data: creatorData, loading } = useCreatorQuery({
    variables: { id },
    skip: !id,
  });

  // Follow creator mutation
  const [followCreator, { loading: followLoading }] = useFollowCreatorMutation({
    onCompleted: (data) => {
      if (data.followCreator.status) {
        setIsFollowing((prev) => !prev);
        // Update the creator data immutably using Apollo Client's cache
        const updatedCreator = data.followCreator.creator;
        if (updatedCreator) {
          client.cache.modify({
            id: client.cache.identify({
              __typename: 'Creator',
              id: updatedCreator.id,
            }),
            fields: {
              is_followed: () => updatedCreator.is_followed,
              followers: () => updatedCreator.followers,
            },
          });
        }
      }
    },
    onError: (error) => {
      console.error('Error following/unfollowing creator:', error);
    },
  });

  const creator = creatorData?.creator;

  // Update isFollowing state when creator data is loaded
  useEffect(() => {
    if (creator) {
      setIsFollowing(creator.is_followed || false);
    }
  }, [creator]);

  const handleBack = () => {
    if (navigation.canGoBack()) {
      router.back();
    } else {
      // No previous screen, go to home
      router.replace('/(tabs)');
    }
  };

  // Handle follow button press
  const handleFollowCreator = () => {
    if (followLoading || !id) return;

    followCreator({
      variables: {
        input: { id: id as string },
      },
    });
  };

  if (loading) {
    return <LoadingScreen onBack={handleBack} message={t('common.loading')} />;
  }

  if (!creator) {
    return <ErrorScreen message={t('common.notFound')} onBack={handleBack} />;
  }

  return (
    <Screen withSafeArea={false}>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <BackButton onPress={handleBack} absolute />

        <RightHeaderButton
          onPress={() => shareCreator(creator as Creator)}
          absolute
        >
          <IconSymbol
            name='square.and.arrow.up'
            size={22}
            color={colors.base[950]}
          />
        </RightHeaderButton>

        {/* Creator profile info */}
        <View style={styles.creatorInfo}>
          <Text variant='h2' numberOfLines={1}>
            {creator.name}
          </Text>
          <TouchableOpacity
            onPress={() => setShowAvatarModal(true)}
            activeOpacity={0.8}
          >
            <Image
              source={{ uri: creator.avatar?.full_url }}
              style={styles.avatar}
              contentFit='cover'
            />
          </TouchableOpacity>

          <Text
            variant='caption'
            color={colors.primary[900]}
            style={styles.username}
            numberOfLines={1}
          >
            @{creator.username}
          </Text>

          {creator.bio && (
            <Text
              variant='caption'
              style={styles.bio}
              align='center'
              numberOfLines={3}
            >
              {creator.bio}
            </Text>
          )}

          {/* Actions container */}
          <View style={styles.actionsContainer}>
            {/* hide follow button if creator is the current user */}
            {creator.user?.id !== useAuthStore.getState().user?.id && (
              <FollowButton
                isFollowing={isFollowing}
                onPress={handleFollowCreator}
                style={styles.followButton}
                loading={followLoading}
              />
            )}

            <View style={styles.socialButtons}>
              {creator.instagram_url && (
                <TouchableOpacity
                  style={styles.socialButton}
                  onPress={() =>
                    creator.instagram_url &&
                    Linking.openURL(creator.instagram_url)
                  }
                >
                  <FontAwesome
                    name='instagram'
                    size={24}
                    color={colors.base[950]}
                  />
                </TouchableOpacity>
              )}

              {creator.tiktok_url && (
                <TouchableOpacity
                  style={styles.socialButton}
                  onPress={() =>
                    creator.tiktok_url && Linking.openURL(creator.tiktok_url)
                  }
                >
                  <FontAwesome
                    name='tiktok'
                    size={24}
                    color={colors.base[950]}
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Stats */}
          <View style={styles.stats}>
            <View style={styles.statItem}>
              <Text variant='h3'>{creator.reels.paginatorInfo.total}</Text>
              <Text variant='caption1' color={colors.darkGrey[500]}>
                {t('creator.sections.reels')}
              </Text>
            </View>

            <View style={styles.statItem}>
              <Text variant='h3'>{creator.collaborations}</Text>
              <Text variant='caption1' color={colors.darkGrey[500]}>
                {t('creator.sections.collaborations')}
              </Text>
            </View>

            <View style={styles.statItem}>
              <Text variant='h3'>{creator.followers.paginatorInfo.total}</Text>
              <Text variant='caption1' color={colors.darkGrey[500]}>
                {t('creator.sections.followers')}
              </Text>
            </View>
          </View>
        </View>

        {/* Reels grid */}
        <View style={styles.reelsContainer}>
          <Text variant='h3' style={styles.sectionTitle}>
            {t('creator.sections.reels')}
          </Text>

          <View style={styles.reelsGrid}>
            {creator?.reels?.data?.map((reel: any, index: number) => (
              <ReelThumbnail
                key={reel.id}
                reel={reel}
                placeId={creator.id}
                isCreator={true}
                style={[
                  styles.reelItem,
                  index % 2 === 0 ? styles.reelItemLeft : styles.reelItemRight,
                ]}
              />
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Full Screen Avatar Modal */}
      <Modal
        visible={showAvatarModal}
        transparent={true}
        animationType='fade'
        onRequestClose={() => setShowAvatarModal(false)}
      >
        <View style={styles.modalOverlay}>
          {/* Backdrop TouchableOpacity */}
          <TouchableOpacity
            style={StyleSheet.absoluteFillObject}
            activeOpacity={1}
            onPress={() => setShowAvatarModal(false)}
          />

          {/* Content container - separate from backdrop */}
          <View style={styles.modalContent}>
            <Image
              source={{ uri: creator.avatar?.full_url }}
              style={styles.fullScreenAvatar}
              contentFit='contain'
            />

            {/* Close button */}
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowAvatarModal(false)}
            >
              <IconSymbol name='xmark' size={24} color={colors.base[100]} />
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: verticalScale(40),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  creatorInfo: {
    alignItems: 'center',
    paddingHorizontal: moderateScale(8),
    marginTop: verticalScale(100),
  },
  avatar: {
    marginTop: verticalScale(10),
    width: moderateScale(80),
    height: moderateScale(80),
    borderRadius: moderateScale(40),
    backgroundColor: colors.darkGrey[300],
  },
  username: {
    marginTop: verticalScale(10),
  },
  bio: {
    marginTop: verticalScale(4),
    color: colors.darkGrey[700],
    width: '60%',
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: verticalScale(16),
    paddingHorizontal: moderateScale(16),
  },
  followButton: {
    marginRight: moderateScale(4),
  },
  socialButtons: {
    flexDirection: 'row',
  },
  socialButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: colors.softGrey[700],
    marginHorizontal: moderateScale(4),
  },
  stats: {
    flexDirection: 'row',
    marginTop: verticalScale(24),
    width: '100%',
    justifyContent: 'space-evenly',
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: colors.softGrey[400],
    paddingVertical: moderateScale(8),
    borderRadius: moderateScale(16),
    width: '30%',
    gap: moderateScale(4),
  },
  reelsContainer: {
    marginTop: verticalScale(24),
    paddingHorizontal: moderateScale(16),
  },
  sectionTitle: {
    marginBottom: verticalScale(12),
  },
  reelsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  reelItem: {
    marginBottom: verticalScale(12),
    width: '48%',
  },
  reelItemLeft: {
    marginRight: moderateScale(4),
  },
  reelItemRight: {
    marginLeft: moderateScale(4),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: colors.blackTransparent.black09,
  },
  modalContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenAvatar: {
    width: moderateScale(300),
    height: moderateScale(300),
    borderRadius: moderateScale(150),
  },
  closeButton: {
    position: 'absolute',
    top: verticalScale(50),
    right: moderateScale(20),
    zIndex: 1,
    backgroundColor: colors.blackTransparent.black03,
    borderRadius: moderateScale(20),
    width: moderateScale(40),
    height: moderateScale(40),
    justifyContent: 'center',
    alignItems: 'center',
  },
});
