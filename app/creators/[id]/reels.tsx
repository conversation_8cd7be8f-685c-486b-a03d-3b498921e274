import React, { useState, useEffect, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Screen } from '@/components/ui/Screen';
import { colors } from '@/constants/Colors';
import { ReelList } from '@/components/home/<USER>';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { BackButton } from '@/components/ui/BackButton';
import { useTranslation } from 'react-i18next';
import { useCreatorQuery } from '@/graphql/generated/graphql';
import { useIsFocused } from '@react-navigation/native';
import type { Reel as ReelType } from '@/schemas/reel';

export default function CreatorReelsScreen() {
  const { t } = useTranslation();
  const params = useLocalSearchParams();
  const id = params.id as string;
  // Make sure reelId is properly cast to string for comparison
  const reelId = params.reelId ? String(params.reelId) : undefined;

  const router = useRouter();
  const isFocused = useIsFocused();

  // Fetch creator data including reels
  const { data, loading, error } = useCreatorQuery({
    variables: {
      id: id as string,
    },
    fetchPolicy: 'cache-and-network',
  });

  // Map the reels data to the format expected by ReelList component
  const mappedReels = useMemo(() => {
    if (!data?.creator?.reels?.data.length) return [];

    return data.creator.reels.data.map((reel: any): ReelType => {
      // Get the first place if it exists
      const firstPlace = reel.places?.data?.[0];
      return {
        objectID: reel.id,
        url: reel.full_url,
        caption: reel.caption,
        places: firstPlace
          ? [
              {
                id: firstPlace.id || '',
                name: firstPlace.name || '',
                deals: (firstPlace.deals?.data || []).map((deal: any) => ({
                  id: deal.id || '',
                  title: deal.title || '',
                  deal_type: deal.deal_type,
                })),
                rates: {
                  google: firstPlace.rates?.google
                    ? Number(firstPlace.rates.google)
                    : 0,
                  reviews_count: firstPlace.rates?.reviews_count
                    ? Number(firstPlace.rates.reviews_count)
                    : 0,
                },
                avatar:
                  typeof firstPlace.avatar?.full_url === 'string'
                    ? firstPlace.avatar.full_url
                    : '',
                ambiance:
                  firstPlace.ambiance?.map((ambiance: any) => ambiance.title) ||
                  [],
                parking:
                  firstPlace.parking?.map((parking: any) => parking.title) ||
                  [],
                specialities:
                  firstPlace.specialities?.map(
                    (speciality: any) => speciality.title
                  ) || [],
                cuisine_types:
                  firstPlace.cuisine_types?.map(
                    (cuisine_type: any) => cuisine_type.title
                  ) || [],
                dietary:
                  firstPlace.dietary?.map((dietary: any) => dietary.title) ||
                  [],
                service_options:
                  firstPlace.service_options?.map(
                    (service_option: any) => service_option.title
                  ) || [],
                meal_times:
                  firstPlace.meal_times?.map(
                    (meal_time: any) => meal_time.title
                  ) || [],
              },
            ]
          : [],
        partner: {
          name: firstPlace.partner?.name || '',
        },
        creator: {
          id: id as string,
          name: data.creator?.user?.name || '',
          avatar: data.creator?.avatar?.full_url || '',
        },
        _geoloc: [{ lat: 0, lng: 0 }],
        _rankingInfo: { geoDistance: 0 },
      };
    });
  }, [data?.creator, id]);

  // Find the initial reel index if reelId is provided
  const initialReelIndex = useMemo(() => {
    if (!reelId || !mappedReels.length) return 0;
    const index = mappedReels.findIndex((reel) => reel.objectID === reelId);
    return index !== -1 ? index : 0;
  }, [reelId, mappedReels]);

  // Use the found initial index
  const [visibleIndex, setVisibleIndex] = useState(initialReelIndex);

  // Update visibleIndex when initialReelIndex changes
  useEffect(() => {
    setVisibleIndex(initialReelIndex);
  }, [initialReelIndex]);

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace(`/creators/${id}`);
    }
  };

  if (loading) {
    return <LoadingScreen onBack={handleBack} message={t('common.loading')} />;
  }

  if (error || !data?.creator) {
    return <ErrorScreen message={t('common.notFound')} onBack={handleBack} />;
  }

  if (!mappedReels.length) {
    return <ErrorScreen message={t('common.notFound')} onBack={handleBack} />;
  }

  return (
    <Screen withSafeArea={false}>
      <View style={styles.container}>
        <BackButton onPress={handleBack} absolute transparent />

        <ReelList
          key={`reels-${reelId}`} // Force remount when reelId changes
          items={mappedReels}
          visibleIndex={visibleIndex}
          setVisibleIndex={setVisibleIndex}
          isLastPage={true}
          loadMore={() => {}}
          isFocused={isFocused}
          initialScrollIndex={initialReelIndex}
          isStandalone={true}
        />
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.base[950],
  },
});
