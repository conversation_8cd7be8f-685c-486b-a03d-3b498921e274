/**
 * +native-intent.tsx  ·  Conari deep-link interceptor (verbose)
 *
 * Adds console logs so you can watch the decision tree in the metro log
 * or React Native Flipper / Reactotron during development.
 */

export function redirectSystemPath({
  path,
  initial,
}: {
  path: string;
  initial: boolean;
}) {
  // ----------  quick helper ----------
  const log = (...args: any[]) => {
    if (__DEV__) console.log('[native-intent]', ...args);
  };

  try {
    log('Incoming:', { path, initial });

    // Only handle the very first URL that boots the app; let Expo Router
    // handle any subsequent in-app links normally.
    if (!initial) {
      log('Not initial → returning original path');
      return path;
    }

    // Turn whatever we received into a URL object so we can inspect it
    // safely (adds dummy base if needed).
    const url = new URL(path, 'conari://app.bootstrap');
    log('Parsed URL:', url.toString());

    /* ───────── 1️⃣  hosted Branch link  ───────── */
    const host = url.hostname.toLowerCase();
    const isBranchHosted =
      host === 'conari.app.link' || host.endsWith('.app.link');
    log('isBranchHosted?', isBranchHosted);

    if (isBranchHosted) {
      log(
        'Branch-hosted link detected → redirecting to "/" to let Branch SDK handle it'
      );
      // Let Branch SDK process this link - it will emit the branchParams event
      // with the extracted data, which will be caught in _layout.tsx
      return '/';
    }

    /* ───────── 2️⃣  custom-scheme link  ───────── */
    if (url.protocol === 'conari:') {
      const cleaned = url.pathname.replace(/^\/+/, ''); // strip leading "/"
      log('Custom-scheme path segment:', cleaned || '(empty)');

      const looksLikeAlias =
        /^[A-Za-z0-9_-]{6,}$/.test(cleaned) && /[A-Z0-9]/.test(cleaned);

      log('looksLikeAlias?', looksLikeAlias);

      if (looksLikeAlias) {
        log('Alias detected → redirecting to "/" to handle as Branch alias');
        return '/';
      } else {
        log('Not an alias → letting Expo Router handle it');
        return path;
      }
    }

    /* ───────── anything else ───────── */
    log('Non-Branch URL → returning original path');
    return path;
  } catch (err) {
    // Never crash here — fall back to a route you own.
    log('Error inside native-intent:', err);
    return '/unexpected-error';
  }
}
