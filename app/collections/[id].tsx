import React, { useEffect, useRef } from 'react';
import { StyleSheet, View, FlatList, Platform } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import {
  useCollectionsQuery,
  useCollectionQuery,
  CollectionQueryVariables,
  useDeleteCollectionMutation,
} from '@/graphql/generated/graphql';
import { useLocalSearchParams, useRouter, useNavigation } from 'expo-router';
import { Text } from '@/components/ui/Text';
import { CollectionPlace } from '@/components/CollectionPlace';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import { BackButton } from '@/components/ui/BackButton';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { RightHeaderButton } from '@/components/ui/RightHeaderButton';
import { colors } from '@/constants/Colors';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { MenuView, type MenuComponentRef } from '@react-native-menu/menu';
import { shareCollection } from '@/services/branch';
import { DangerModal } from '@/components/ui/DangerModal';
import { Modalize } from 'react-native-modalize';
import { Image } from 'expo-image';
import { Button } from '@/components/ui/Button';

export default function CollectionDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { t } = useTranslation();
  const router = useRouter();
  const navigation = useNavigation();
  const contentOpacity = useSharedValue(0);
  const contentTranslateY = useSharedValue(50);
  const menuRef = useRef<MenuComponentRef>(null);
  const cancelModalRef = useRef<Modalize>(null);
  const [deleteCollection, { loading: deleteCollectionLoading }] =
    useDeleteCollectionMutation();

  const actions = [
    {
      id: 'edit',
      title: t('common.edit'),
      image: Platform.select({
        ios: 'square.and.pencil',
        android: 'ic_menu_edit',
      }),
      imageColor: colors.base[950],
      titleColor: colors.base[950],
    },
    // {
    //   id: 'share',
    //   title: t('common.share'),
    //   image: Platform.select({
    //     ios: 'square.and.arrow.up',
    //     android: 'ic_menu_share',
    //   }),
    //   imageColor: colors.base[950],
    //   titleColor: colors.base[950],
    // },

    {
      id: 'delete',
      title: t('common.delete'),
      attributes: {
        destructive: true,
      },
      image: Platform.select({
        ios: 'trash',
        android: 'ic_menu_delete',
      }),
      imageColor: colors.info.red,
      titleColor: colors.info.red,
    },
  ];
  // Define variables for collection query
  const variables: CollectionQueryVariables = {
    id: id || '',
  };

  const { data, loading, error } = useCollectionQuery({
    variables,
    skip: !id,
  });

  const { refetch: refetchCollections } = useCollectionsQuery();

  // Animation effect when data is loaded
  useEffect(() => {
    if (data) {
      contentOpacity.value = withTiming(1, {
        duration: 500,
        easing: Easing.out(Easing.quad),
      });
      contentTranslateY.value = withTiming(0, {
        duration: 500,
        easing: Easing.out(Easing.quad),
      });
    }
  }, [data]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: contentOpacity.value,
      transform: [{ translateY: contentTranslateY.value }],
    };
  });

  const handleBack = () => {
    if (navigation.canGoBack()) {
      router.back();
    } else {
      router.replace('/(tabs)/collections');
    }
  };

  const renderEmptyComponent = () => {
    if (loading) return null;

    return (
      <View style={styles.emptyContainer}>
        <Image
          source={require('@/assets/images/noCollection.png')}
          style={styles.emptyImage}
          contentFit='contain'
        />
        <Text variant='h2' align='center' style={styles.emptyText}>
          {t(`collections.emptyState`)}
        </Text>
        <Text variant='caption' align='center' style={styles.emptySubtext}>
          {t(`collections.emptyStateSubtext`)}
        </Text>
        <Button
          onPress={() => {
            router.push({ pathname: '/' });
          }}
          title={t(`collections.startAdding`)}
          variant='outline'
          style={styles.newCollectionButton}
        />
      </View>
    );
  };

  const collection = data?.collection;

  // Filter collection items to only include places
  const places = collection?.items
    .filter((item) => item?.collectable.__typename === 'PartnerPlace')
    .map((item) =>
      item?.collectable.__typename === 'PartnerPlace' ? item.collectable : null
    )
    .filter(Boolean);

  if (error) {
    return <ErrorScreen message={t('common.notFound')} />;
  }

  const handlePressAction = (event: string) => {
    if (event === 'edit') {
      // Navigate to edit collection modal with collection data
      router.push({
        pathname: '/(modals)/collection',
        params: { collectionId: id },
      });
    } else if (event === 'share') {
      // Handle share action
      if (collection) {
        shareCollection(collection);
      }
    } else if (event === 'delete') {
      // Handle delete action
      cancelModalRef.current?.open();
    }
  };

  const handleCancelConfirm = async () => {
    const response = await deleteCollection({ variables: { id: id || '' } });
    if (response) {
      //refetch collections
      await refetchCollections();
      router.replace('/(tabs)/collections');
    }
  };

  return (
    <Screen withSafeArea={false}>
      <MenuView
        ref={menuRef}
        onPressAction={({ nativeEvent }) => {
          const { event } = nativeEvent;
          handlePressAction(event);
        }}
        actions={actions}
        themeVariant='light'
        style={{
          top: verticalScale(60),
          right: moderateScale(16),
          position: 'absolute',
          zIndex: 1,
        }}
      >
        <RightHeaderButton
          onPress={
            Platform.OS === 'android' ? () => menuRef.current?.show() : () => {}
          }
        >
          <IconSymbol name='ellipsis' size={22} color={colors.base[950]} />
        </RightHeaderButton>
      </MenuView>

      <BackButton onPress={handleBack} absolute />

      <Animated.View style={[styles.contentContainer, animatedStyle]}>
        <View style={styles.header}>
          <Text variant='h2' style={styles.title} numberOfLines={2}>
            {collection?.title}
          </Text>
          {collection?.description && (
            <Text variant='body' style={styles.description}>
              {collection.description}
            </Text>
          )}
        </View>
        {places && places.length > 0 && (
          <Text
            variant='caption1'
            style={styles.count}
            color={colors.darkGrey[400]}
          >
            {places.length}{' '}
            {t(
              places.length === 1
                ? 'collections.restaurant'
                : 'collections.restaurants'
            )}
          </Text>
        )}

        {places && places.length > 0 ? (
          <FlatList
            data={places}
            keyExtractor={(item) => item!.id}
            renderItem={({ item }) =>
              item && <CollectionPlace place={item} collectionId={id} />
            }
            contentContainerStyle={styles.listContentContainer}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          !loading && renderEmptyComponent()
        )}
      </Animated.View>
      <DangerModal
        ref={cancelModalRef}
        isLoading={deleteCollectionLoading}
        onConfirm={handleCancelConfirm}
        onCancel={() => {
          cancelModalRef.current?.close();
        }}
        title={t('collections.deleteCollection')}
        description={t('collections.deleteCollectionConfirmation')}
        image={require('@/assets/images/deleteCollection.png')}
      />
    </Screen>
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    paddingTop: verticalScale(100), // Add space for the back button
    paddingHorizontal: moderateScale(16),
    paddingBottom: verticalScale(40),
  },
  header: {
    marginTop: verticalScale(8),
  },
  title: {
    marginBottom: moderateScale(4),
  },
  description: {
    marginBottom: moderateScale(4),
    opacity: 0.8,
  },
  count: {
    marginVertical: moderateScale(4),
  },
  listContentContainer: {
    paddingBottom: verticalScale(100),
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    width: '80%',
    alignSelf: 'center',
    marginTop: moderateScale(80),
  },
  emptyImage: {
    width: moderateScale(200),
    height: verticalScale(150),
  },
  emptyText: {
    marginTop: moderateScale(40),
  },
  emptySubtext: {
    marginTop: moderateScale(24),
    marginBottom: moderateScale(8),
  },
  newCollectionButton: {
    marginTop: moderateScale(24),
    width: moderateScale(200),
  },
});
