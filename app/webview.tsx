import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { useLocalSearchParams, router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { moderateScale } from '@/utils/scaling';
import { BackButton } from '@/components/ui/BackButton';
import { LoadingScreen } from '@/components/ui/LoadingScreen';

export default function WebViewScreen() {
  const { url, title } = useLocalSearchParams<{ url: string; title: string }>();
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);

  const handleBack = () => {
    router.back();
  };

  return (
    <Screen>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
        {title && (
          <Text variant='h3' style={styles.title} numberOfLines={1}>
            {title || t('common.webview')}
          </Text>
        )}
      </View>
      <View style={styles.webViewContainer}>
        {url ? (
          <>
            <WebView
              accessibilityLabel={title}
              source={{ uri: url }}
              style={styles.webView}
              onLoadStart={() => setIsLoading(true)}
              onLoadEnd={() => setIsLoading(false)}
            />
            {isLoading && (
              <View style={styles.loadingContainer}>
                <LoadingScreen message={t('common.loading')} />
              </View>
            )}
          </>
        ) : (
          <View style={styles.loadingContainer}>
            <Text variant='body'>{t('common.no_url')}</Text>
          </View>
        )}
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(16),
  },
  title: {
    flex: 1,
    textAlign: 'center',
    marginRight: moderateScale(40), // To balance the back button on the left
  },
  webViewContainer: {
    flex: 1,
    position: 'relative',
  },
  webView: {
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});
