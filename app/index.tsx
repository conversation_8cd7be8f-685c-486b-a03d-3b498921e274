import { Redirect } from 'expo-router';
import { useAuthStore } from '@/store/auth';
import { LoginStatusCode } from '@/graphql/generated/graphql';

export default function Index() {
  // Get authentication state from store
  const isLoggedIn = useAuthStore((state) => state.isAuthenticated);
  const statusCode = useAuthStore((state) => state.statusCode);

  // Determine the initial route based on authentication and status
  let initialRoute:
    | '/(tabs)'
    | '/(auth)/onboarding'
    | '/(auth)/awaiting-approval' = '/(auth)/onboarding';

  if (isLoggedIn) {
    // User is logged in (either through Apple or dev simulation)
    if (statusCode === LoginStatusCode.Success) {
      initialRoute = '/(tabs)';
    } else if (statusCode) {
      // Any other status code (AWAITING_APPROVAL, PENDING_INVITATION, etc.)
      initialRoute = '/(auth)/awaiting-approval';
    } else {
      // Fallback for logged in users without status code
      initialRoute = '/(tabs)';
    }
  } else {
    // User is not logged in
    initialRoute = '/(auth)/onboarding';
  }

  return <Redirect href={initialRoute} />;
}
