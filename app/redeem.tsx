import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { LinearGradient } from 'expo-linear-gradient';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale, scale } from '@/utils/scaling';
import { Image } from 'expo-image';
import { AppleCashSwirl } from '../components/ui/AppleCashSwirl';
import { SquareCheckmark } from '../components/ui/SquareCheckmark';
import { Button } from '@/components/ui/Button';
import { useLocalSearchParams, useRouter } from 'expo-router';
import RedeemStar from '@/components/ui/RedeemStar';
import { useTranslation } from 'react-i18next';
import { formatTimeRange } from '@/utils/time';
import { format } from 'date-fns';
import { MyDeal, MyDealStatusEnum } from '@/graphql/generated/graphql';
import { ErrorScreen } from '@/components/ui/ErrorScreen';

export default function RedeemDeal() {
  const { t } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams<{ deal: string }>();

  const deal: MyDeal = JSON.parse(params.deal);

  React.useEffect(() => {
    if (
      !deal?.deal?.id ||
      !deal?.deal?.title ||
      !deal?.deal?.partner_place?.name
    ) {
      // If required data is missing, go back
      router.back();
    }
  }, [deal]);

  if (!params.deal) {
    router.back();
    return null;
  }

  const handleFinish = () => {
    //navigate to deals screen with redeemed filter
    router.push({
      pathname: '/deals',
      params: {
        activeFilter: MyDealStatusEnum.Redeemed,
      },
    });
  };

  if (!deal) {
    return <ErrorScreen message={t('common.notFound')} onBack={handleFinish} />;
  }

  const { partner_place, title } = deal.deal;
  const { date, slot } = deal.reserve_slot;

  return (
    <Screen>
      <View style={styles.container}>
        {/* Background Circle */}
        <View style={styles.backgroundCircle} />
        <RedeemStar style={styles.blob1} />
        <RedeemStar style={styles.blob2} />
        <RedeemStar style={styles.blob3} />
        <RedeemStar style={styles.blob4} />
        <View style={styles.cardWrapper}>
          <LinearGradient
            colors={[colors.secondary[400], colors.primary[950]]}
            start={{ x: 0, y: 0.1 }}
            end={{ x: 0.9, y: 0.9 }}
            style={styles.card}
          >
            {/* Partner Info */}
            <View style={styles.partnerInfo}>
              <Image
                source={partner_place.avatar?.full_url}
                style={styles.partnerImage}
                contentFit='cover'
              />
              <View style={styles.partnerNameContainer}>
                <Text style={styles.partnerName} color={colors.base[100]}>
                  {partner_place.partner.name}
                </Text>
                <Text variant='tiny' color={colors.base[100]}>
                  {partner_place.name}
                </Text>
              </View>
            </View>

            {/* Animated swirl in the background */}
            <View style={styles.swirlContainer}>
              <AppleCashSwirl />
            </View>

            {/* Centered check */}
            <View style={styles.checkWrapper}>
              <SquareCheckmark />
            </View>

            {/* Bottom Info */}
            <View style={styles.bottomInfo}>
              {/* Deal Title */}
              <View style={styles.dealTitleContainer}>
                <Text variant='tiny' color={colors.base[100]}>
                  {t('redeem.dealTitle')}
                </Text>
                <Text
                  variant='caption1'
                  style={styles.dealTitle}
                  color={colors.base[100]}
                  weight='bold'
                  numberOfLines={2}
                >
                  {title}
                </Text>
              </View>

              {/* Date and Time */}
              <View style={styles.dateTimeContainer}>
                <Text variant='tiny' color={colors.base[100]}>
                  {t('redeem.dateAndTime')}
                </Text>
                <View style={styles.dateTime}>
                  <Text
                    variant='caption1'
                    weight='bold'
                    color={colors.base[100]}
                  >
                    {date && format(new Date(date), 'MMM d, yyyy')}
                  </Text>
                  {slot && (
                    <Text
                      variant='caption1'
                      weight='bold'
                      color={colors.base[100]}
                    >
                      {formatTimeRange(slot.from, slot.to)}
                    </Text>
                  )}
                </View>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Success Message */}
        <View style={styles.messageContainer}>
          <Text align='center' variant='h1' style={styles.heading}>
            {t('redeem.success.title')}
          </Text>
          <Text align='center' variant='caption' style={styles.description}>
            {t('redeem.success.description')}
          </Text>
        </View>
      </View>
      {/* Finish Button */}
      <View style={styles.buttonContainer}>
        <Button title={t('common.done')} onPress={handleFinish} />
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundCircle: {
    position: 'absolute',
    top: verticalScale(115),
    width: scale(303),
    height: scale(310),
    borderRadius: scale(151.5),
    backgroundColor: colors.primary[100],
  },
  blob1: {
    position: 'absolute',
    top: verticalScale(115),
    left: scale(280),
    width: scale(200),
    height: scale(200),
  },
  blob2: {
    position: 'absolute',
    top: verticalScale(135),
    left: scale(65),
    width: scale(130),
    height: scale(130),
  },
  blob3: {
    position: 'absolute',
    top: verticalScale(415),
    left: scale(290),
    width: scale(50),
    height: scale(50),
  },
  blob4: {
    position: 'absolute',
    top: verticalScale(430),
    left: scale(100),
    width: scale(120),
    height: scale(120),
  },
  cardWrapper: {
    position: 'absolute',
    top: verticalScale(160),
    shadowColor: colors.base[950],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
    borderRadius: 16,
  },
  card: {
    width: moderateScale(343),
    height: verticalScale(320),
    borderRadius: moderateScale(35),
    padding: moderateScale(16),
    overflow: 'hidden',
  },
  partnerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
    marginBottom: verticalScale(8),
  },
  partnerImage: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderRadius: moderateScale(16),
    borderWidth: 1,
    borderColor: colors.base[100],
  },
  partnerNameContainer: {
    gap: moderateScale(2),
  },
  partnerName: {
    fontFamily: 'Coolvetica',
  },
  swirlContainer: {
    alignSelf: 'center',
    justifyContent: 'center',
    position: 'absolute',
  },
  checkWrapper: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: moderateScale(50),
    height: moderateScale(50),
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomInfo: {
    position: 'absolute',
    bottom: moderateScale(16),
    left: moderateScale(24),
    right: moderateScale(24),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  dealTitleContainer: {
    flex: 1,
    marginRight: moderateScale(16),
    gap: moderateScale(4),
  },
  dealTitle: {
    width: moderateScale(110),
  },
  dateTimeContainer: {
    alignItems: 'flex-end',
    gap: moderateScale(4),
  },
  dateTime: {
    alignItems: 'flex-end',
  },
  messageContainer: {
    alignItems: 'center',
    position: 'relative',
    top: verticalScale(250),
    width: '70%',
  },
  heading: {
    marginBottom: moderateScale(16),
  },
  description: {
    color: colors.darkGrey[700],
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: moderateScale(16),
    gap: moderateScale(10),
  },
});
