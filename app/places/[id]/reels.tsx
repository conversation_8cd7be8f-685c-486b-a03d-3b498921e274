import React, { useState, useEffect, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Screen } from '@/components/ui/Screen';
import { colors } from '@/constants/Colors';
import { ReelList } from '@/components/home/<USER>';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { BackButton } from '@/components/ui/BackButton';
import { useTranslation } from 'react-i18next';
import { usePlaceQuery } from '@/graphql/generated/graphql';
import { useIsFocused } from '@react-navigation/native';
import type { Reel as ReelType } from '@/schemas/reel';

export default function PlaceReelsScreen() {
  const { t } = useTranslation();
  const params = useLocalSearchParams();
  const id = params.id as string;
  // Make sure reelId is properly cast to string for comparison
  const reelId = params.reelId ? String(params.reelId) : undefined;

  const router = useRouter();
  const isFocused = useIsFocused();

  // Helper function to extract titles and filter out undefined values
  const extractTitles = (items: any[] | undefined | null): string[] => {
    return items?.map((item) => item?.title).filter(Boolean) || [];
  };

  // Fetch place data including reels
  const { data, loading, error } = usePlaceQuery({
    variables: {
      id: id as string,
    },
    fetchPolicy: 'cache-and-network',
  });

  // Map the reels data to the format expected by ReelList component
  const mappedReels = useMemo(() => {
    if (!data?.partnerPlace?.reels?.data.length) return [];

    return data.partnerPlace.reels.data.map(
      (reel: any): ReelType => ({
        objectID: reel.id,
        url: reel.full_url,
        caption: reel.caption,
        places: [
          {
            id: id as string,
            name: data.partnerPlace?.name || '',
            deals: (data.partnerPlace?.deals?.data || []).map((deal) => ({
              id: deal.id || '',
              title: deal.title || '',
              deal_type: deal.deal_type,
            })),
            rates: {
              google: data.partnerPlace?.rates?.google
                ? Number(data.partnerPlace.rates.google)
                : 0,
              reviews_count: data.partnerPlace?.rates?.reviews_count
                ? Number(data.partnerPlace.rates.reviews_count)
                : 0,
            },
            avatar:
              typeof data.partnerPlace?.avatar?.full_url === 'string'
                ? data.partnerPlace.avatar.full_url
                : '',
            ambiance: extractTitles(data.partnerPlace?.ambiance),
            parking: extractTitles(data.partnerPlace?.parking),
            specialities: extractTitles(data.partnerPlace?.specialities),
            cuisine_types: extractTitles(data.partnerPlace?.cuisine_types),
            dietary: extractTitles(data.partnerPlace?.dietary),
            service_options: extractTitles(data.partnerPlace?.service_options),
            meal_times: extractTitles(data.partnerPlace?.meal_times),
            cravings: extractTitles(data.partnerPlace?.cravings),
          },
        ],
        partner: {
          name: data.partnerPlace?.partner?.name || '',
        },
        _geoloc: [{ lat: 0, lng: 0 }],
        _rankingInfo: { geoDistance: 0 },
      })
    );
  }, [data?.partnerPlace, id]);

  // Find the initial reel index if reelId is provided
  const initialReelIndex = useMemo(() => {
    if (!reelId || !mappedReels.length) return 0;
    const index = mappedReels.findIndex((reel) => reel.objectID === reelId);
    return index !== -1 ? index : 0;
  }, [reelId, mappedReels]);

  // Use the found initial index
  const [visibleIndex, setVisibleIndex] = useState(initialReelIndex);

  // Update visibleIndex when initialReelIndex changes
  useEffect(() => {
    setVisibleIndex(initialReelIndex);
  }, [initialReelIndex]);

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace(`/places/${id}`);
    }
  };

  if (loading) {
    return <LoadingScreen onBack={handleBack} message={t('common.loading')} />;
  }

  if (error || !data?.partnerPlace) {
    return <ErrorScreen message={t('common.notFound')} onBack={handleBack} />;
  }

  if (!mappedReels.length) {
    return <ErrorScreen message={t('common.notFound')} onBack={handleBack} />;
  }

  return (
    <Screen withSafeArea={false}>
      <View style={styles.container}>
        <BackButton onPress={handleBack} absolute transparent />

        <ReelList
          key={`reels-${reelId}`} // Force remount when reelId changes
          items={mappedReels}
          visibleIndex={visibleIndex}
          setVisibleIndex={setVisibleIndex}
          isLastPage={true}
          loadMore={() => {}}
          isFocused={isFocused}
          initialScrollIndex={initialReelIndex}
          isStandalone={true}
        />
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.base[950],
  },
});
