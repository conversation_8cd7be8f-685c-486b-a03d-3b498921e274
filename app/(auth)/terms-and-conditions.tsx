import { Screen } from '@/components/ui/Screen';
import { StyleSheet, ScrollView, View } from 'react-native';
import { moderateScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import { Text } from '@/components/ui/Text';
import { WebView } from 'react-native-webview';

export default function TermsAndConditionsScreen() {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  // TODO: Get the actual terms and conditions from the backend based on app language
  const htmlContent = `
    <html dir="${isRTL ? 'rtl' : 'ltr'}">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: -apple-system, system-ui;
            line-height: 1.6;
            color: #1C1C1E;
            padding: 0;
            margin: 0;
          }
          h2 {
            color: #7569F3;
            margin-top: 24px;
          }
          p {
            margin: 12px 0;
          }
          .section {
            margin-bottom: 20px;
          }
          ul {
            padding-left: 20px;
          }
          li {
            margin: 8px 0;
          }
          .highlight {
            background-color: #F0EFFE;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
          }
        </style>
      </head>
      <body>
        <div class="section">
          <h2>1. Acceptance of Terms</h2>
          <p>By accessing or using Conari, you agree to be bound by these Terms of Service and all applicable laws and regulations.</p>
        </div>

        <div class="section">
          <h2>2. User Registration</h2>
          <p>To use certain features of Conari, you must:</p>
          <ul>
            <li>Provide accurate and complete registration information</li>
            <li>Maintain the security of your account</li>
            <li>Promptly update any changes to your information</li>
          </ul>
        </div>

        <div class="highlight">
          <h2>3. Privacy & Data Protection</h2>
          <p>We take your privacy seriously. Our data collection and use practices are detailed in our Privacy Policy.</p>
        </div>

        <div class="section">
          <h2>4. User Content</h2>
          <p>When posting reviews or content, you agree:</p>
          <ul>
            <li>To provide honest and accurate information</li>
            <li>Not to post harmful or inappropriate content</li>
            <li>To respect intellectual property rights</li>
          </ul>
        </div>

        <div class="section">
          <h2>5. Restaurant Listings</h2>
          <p>Conari provides restaurant information that:</p>
          <ul>
            <li>Is gathered from various sources</li>
            <li>May be updated periodically</li>
            <li>Should be verified independently</li>
          </ul>
        </div>

        <div class="highlight">
          <h2>6. Deals and Promotions</h2>
          <p>Restaurant deals displayed in the app are:</p>
          <ul>
            <li>Subject to availability</li>
            <li>May have specific terms and conditions</li>
            <li>Could be time-sensitive</li>
          </ul>
        </div>

        <div class="section">
          <h2>7. Modifications to Service</h2>
          <p>Conari reserves the right to modify or discontinue the service with or without notice.</p>
        </div>

        <div class="section">
          <h2>8. Limitation of Liability</h2>
          <p>Conari is not liable for:</p>
          <ul>
            <li>Accuracy of restaurant information</li>
            <li>Availability of deals</li>
            <li>User-generated content</li>
          </ul>
        </div>

        <div class="section">
          <h2>9. Contact Information</h2>
          <p>For questions about these terms, please contact us at:</p>
          <p>Email: <EMAIL></p>
          <p>Last updated: March 2024</p>
        </div>
      </body>
    </html>
  `;

  return (
    <Screen withSafeArea={false}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <Text variant='h1'>{t('terms.title')}</Text>
        </View>
        <View style={styles.summary}>
          <Text variant='caption'>{t('terms.summary')}</Text>
        </View>
        <WebView
          originWhitelist={['*']}
          source={{ html: htmlContent }}
          style={styles.webview}
        />
      </ScrollView>
    </Screen>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(20),
  },
  header: {
    marginTop: moderateScale(24),
  },
  summary: {
    marginTop: moderateScale(16),
  },
  webview: {
    marginTop: moderateScale(16),
    flex: 1,
  },
});
