import { StyleSheet, View, ActivityIndicator } from 'react-native';
import { Text } from '@/components/ui/Text';
import { Screen } from '@/components/ui/Screen';
import { Button } from '@/components/ui/Button';
import { colors } from '@/constants/Colors';
import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { requestNotificationPermission } from '@/services/notifications';
import { router } from 'expo-router';
import {
  useMeQuery,
  LoginStatusCode,
  useAddFcmTokenMutation,
} from '@/graphql/generated/graphql';
import { Image } from 'expo-image';

export default function AwaitingApprovalScreen() {
  const { t } = useTranslation();
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [loading, setLoading] = useState(false);
  const statusCode = useAuthStore((state) => state.statusCode);
  const { data } = useMeQuery();
  const [addFCMToken] = useAddFcmTokenMutation();

  // Effect to check user status and redirect if approved
  useEffect(() => {
    if (
      statusCode === LoginStatusCode.Success ||
      data?.me?.status === LoginStatusCode.Success
    ) {
      // If status is SUCCESS, update status code in store and redirect
      if (data?.me?.status === LoginStatusCode.Success) {
        useAuthStore.getState().setStatusCode(LoginStatusCode.Success);
      }
      router.replace('/(tabs)');
    }
  }, [data, statusCode]);

  const handleEnableNotifications = async () => {
    setLoading(true);
    try {
      const token = await requestNotificationPermission();
      if (token) {
        setNotificationsEnabled(true);
        // Send token to backend
        addFCMToken({ variables: { token } });
      }
    } catch (error) {
      console.error('Error enabling notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Screen>
      <View style={styles.container}>
        <Image
          source={require('@/assets/images/awaiting-approval.png')}
          style={styles.image}
          contentFit='contain'
        />

        <Text variant='h1' style={styles.title}>
          {t('pendingApproval.title')}
        </Text>

        <Text variant='caption' style={styles.subtitle} align='center'>
          {t('pendingApproval.subtitle')}
        </Text>

        <View style={styles.buttonContainer}>
          <Button
            style={styles.button}
            variant={notificationsEnabled ? 'primary' : 'secondary'}
            title={t(
              notificationsEnabled
                ? 'pendingApproval.notificationsEnabled'
                : 'pendingApproval.enableNotifications'
            )}
            onPress={handleEnableNotifications}
            disabled={loading}
            leftIcon={
              loading ? (
                <ActivityIndicator
                  color={
                    notificationsEnabled
                      ? colors.base[100]
                      : colors.primary[950]
                  }
                />
              ) : (
                <IconSymbol
                  name={notificationsEnabled ? 'bell.fill' : 'bell'}
                  size={moderateScale(20)}
                  color={
                    notificationsEnabled
                      ? colors.base[100]
                      : colors.primary[950]
                  }
                />
              )
            }
          />

          <Text variant='caption1' style={styles.notificationDescription}>
            {t('pendingApproval.notificationsDescription')}
          </Text>
        </View>
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
  },
  image: {
    marginLeft: moderateScale(40),
    width: moderateScale(280),
    height: verticalScale(296),
    marginBottom: verticalScale(48),
  },
  title: {
    marginBottom: verticalScale(16),
  },
  subtitle: {
    marginBottom: verticalScale(22),
    width: '75%',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    gap: verticalScale(12),
    bottom: verticalScale(15),
    position: 'absolute',
  },
  button: {
    width: '100%',
  },
  notificationDescription: {
    color: colors.darkGrey[700],
    textAlign: 'center',
    width: '70%',
  },
});
