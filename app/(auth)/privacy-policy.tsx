import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { StyleSheet, ScrollView, View } from 'react-native';
import { moderateScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import { WebView } from 'react-native-webview';

export default function PrivacyPolicyScreen() {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  // TODO: Get the actual privacy policy from the backend based on app language
  const htmlContent = `
    <html dir="${isRTL ? 'rtl' : 'ltr'}">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: -apple-system, system-ui;
            line-height: 1.6;
            color: #1C1C1E;
            padding: 0;
            margin: 0;
            direction: ${isRTL ? 'rtl' : 'ltr'};
            text-align: ${isRTL ? 'right' : 'left'};
          }
          h2 {
            color: #7569F3;
            margin-top: 24px;
          }
          p {
            margin: 12px 0;
          }
          .section {
            margin-bottom: 20px;
          }
          ul {
            padding-left: 20px;
          }
          li {
            margin: 8px 0;
          }
          .highlight {
            background-color: #F0EFFE;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
          }
        </style>
      </head>
      <body>
        <div class="section">
          <h2>1. Information We Collect</h2>
          <p>When using Conari, we may collect:</p>
          <ul>
            <li>Phone number for authentication</li>
            <li>Device information and identifiers</li>
            <li>Location data (with your permission)</li>
            <li>Usage data and preferences</li>
          </ul>
        </div>

        <div class="highlight">
          <h2>2. How We Use Your Information</h2>
          <p>We use your information to:</p>
          <ul>
            <li>Provide and improve our services</li>
            <li>Personalize your experience</li>
            <li>Send you relevant notifications</li>
            <li>Ensure security of our platform</li>
          </ul>
        </div>

        <div class="section">
          <h2>3. Information Sharing</h2>
          <p>We may share your information with:</p>
          <ul>
            <li>Restaurant partners</li>
            <li>Service providers</li>
            <li>Legal authorities when required</li>
          </ul>
        </div>

        <div class="highlight">
          <h2>4. Data Security</h2>
          <p>We protect your data through:</p>
          <ul>
            <li>Encryption during transmission</li>
            <li>Secure storage systems</li>
            <li>Regular security audits</li>
            <li>Access controls</li>
          </ul>
        </div>

        <div class="section">
          <h2>5. Your Rights</h2>
          <p>You have the right to:</p>
          <ul>
            <li>Access your personal data</li>
            <li>Request data correction</li>
            <li>Delete your account</li>
            <li>Opt-out of marketing</li>
          </ul>
        </div>

        <div class="section">
          <h2>6. Location Services</h2>
          <p>Our location features:</p>
          <ul>
            <li>Help find nearby restaurants</li>
            <li>Are optional and can be disabled</li>
            <li>Improve restaurant recommendations</li>
          </ul>
        </div>

        <div class="highlight">
          <h2>7. Cookies & Tracking</h2>
          <p>We use cookies and similar technologies to:</p>
          <ul>
            <li>Remember your preferences</li>
            <li>Analyze app usage</li>
            <li>Improve our services</li>
          </ul>
        </div>

        <div class="section">
          <h2>8. Children's Privacy</h2>
          <p>Our service is not intended for users under 13 years of age.</p>
        </div>

        <div class="section">
          <h2>9. Contact Us</h2>
          <p>For privacy concerns, contact us at:</p>
          <p>Email: <EMAIL></p>
          <p>Last updated: March 2024</p>
        </div>
      </body>
    </html>
  `;

  return (
    <Screen withSafeArea={false}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <Text variant='h1'>{t('privacy.title')}</Text>
        </View>
        <View style={styles.summary}>
          <Text variant='caption'>{t('privacy.summary')}</Text>
        </View>
        <WebView
          originWhitelist={['*']}
          source={{ html: htmlContent }}
          style={styles.webview}
        />
      </ScrollView>
    </Screen>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(20),
  },
  header: {
    marginTop: moderateScale(24),
  },
  summary: {
    marginTop: moderateScale(16),
  },
  webview: {
    marginTop: moderateScale(16),
    flex: 1,
  },
});
