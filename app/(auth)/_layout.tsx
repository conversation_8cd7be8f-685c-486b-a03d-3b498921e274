import { colors } from '@/constants/Colors';
import { Stack } from 'expo-router';
import { moderateScale } from '@/utils/scaling';
import { Pressable, StyleSheet } from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
// import { useLanguageStore } from '@/store/language';
// import { Text } from '@/components/ui/Text';

export default function AuthLayout() {
  // const { language, toggleLanguage } = useLanguageStore();

  const CustomBackButton = ({ onPress }: { onPress: () => void }) => (
    <Pressable style={styles.backButton} onPress={onPress}>
      <IconSymbol name='chevron.left' size={24} color={colors.base[950]} />
    </Pressable>
  );

  // const LanguageButton = () => (
  //   <Pressable style={styles.langButton} onPress={toggleLanguage}>
  //     <Text style={styles.langText}>{language.toUpperCase()}</Text>
  //   </Pressable>
  // );

  return (
    <Stack
      screenOptions={({ navigation }) => ({
        headerShadowVisible: false,
        headerTitle: '',
        headerLeft: () =>
          navigation.canGoBack() ? (
            <CustomBackButton onPress={() => navigation.goBack()} />
          ) : null,
        // headerRight: () => <LanguageButton />,
      })}
    >
      <Stack.Screen
        name='onboarding'
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name='otp'
        options={{
          title: 'OTP',
        }}
      />
    </Stack>
  );
}

const styles = StyleSheet.create({
  backButton: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: colors.softGrey[400],
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: moderateScale(8),
  },
  // langButton: {
  //   backgroundColor: colors.softGrey[400],
  //   paddingHorizontal: moderateScale(12),
  //   paddingVertical: moderateScale(6),
  //   borderRadius: moderateScale(16),
  //   marginRight: moderateScale(16),
  // },
  // langText: {
  //   fontSize: moderateScale(12),
  //   fontWeight: '600',
  //   color: colors.base[950],
  // },
});
