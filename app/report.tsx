import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  Alert,
  Linking,
  ScrollView,
} from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { Button } from '@/components/ui/Button';
import { BackButton } from '@/components/ui/BackButton';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import { useRouter, useLocalSearchParams } from 'expo-router';

const EMAIL_SUPPORT = '<EMAIL>';

export default function ReportScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { placeName, partnerName, dealName } = useLocalSearchParams();
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!feedback.trim()) {
      Alert.alert(t('common.error'), t('report.emptyFeedback'));
      return;
    }

    setIsSubmitting(true);

    try {
      const subject = encodeURIComponent(t('report.emailSubject'));

      // Build email body with context information
      let emailBody = feedback;

      // Add context information if available
      if (partnerName || placeName || dealName) {
        emailBody += '\n\n---\n';
        emailBody += t('report.contextInfo') + '\n';

        if (partnerName) {
          emailBody += `${t('report.partnerName')}: ${partnerName}\n`;
        }

        if (placeName) {
          emailBody += `${t('report.placeName')}: ${placeName}\n`;
        }

        if (dealName) {
          emailBody += `${t('report.dealName')}: ${dealName}\n`;
        }
      }

      const body = encodeURIComponent(emailBody);
      const mailtoUrl = `mailto:${EMAIL_SUPPORT}?subject=${subject}&body=${body}`;

      const canOpen = await Linking.canOpenURL(mailtoUrl);

      if (canOpen) {
        await Linking.openURL(mailtoUrl);
        Alert.alert(t('report.success.title'), t('report.success.message'), [
          {
            text: t('common.ok'),
            onPress: () => router.back(),
          },
        ]);
      } else {
        Alert.alert(t('common.error'), t('report.noEmailApp'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('report.submitError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <Screen>
      <View style={styles.container}>
        <View style={styles.header}>
          <BackButton onPress={handleBack} />
          <Text variant='h2' style={styles.title}>
            {t('report.title')}
          </Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Text variant='body' style={styles.description}>
            {t('report.description')}
          </Text>

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textArea}
              placeholder={t('report.placeholder')}
              placeholderTextColor={colors.darkGrey[500]}
              value={feedback}
              onChangeText={setFeedback}
              multiline
              numberOfLines={8}
              textAlignVertical='top'
            />
          </View>

          <Button
            title={t('report.submit')}
            onPress={handleSubmit}
            disabled={isSubmitting || !feedback.trim()}
            loading={isSubmitting}
            style={styles.submitButton}
          />
        </ScrollView>
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(16),
  },
  title: {
    flex: 1,
    textAlign: 'center',
    marginRight: moderateScale(40), // To balance the back button on the left
  },
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(20),
    paddingTop: verticalScale(20),
  },
  description: {
    marginBottom: verticalScale(24),
    color: colors.darkGrey[700],
    lineHeight: verticalScale(22),
  },
  inputContainer: {
    marginBottom: verticalScale(24),
  },
  textArea: {
    backgroundColor: colors.base[100],
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    fontSize: moderateScale(16),
    color: colors.base[950],
    minHeight: verticalScale(120),
    maxHeight: verticalScale(200),
    borderWidth: 1,
    borderColor: colors.darkGrey[300],
  },
  submitButton: {
    marginBottom: verticalScale(40),
  },
});
