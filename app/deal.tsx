import React, { useState, useMemo } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { colors } from '@/constants/Colors';
import { moderateScale, scale, verticalScale } from '@/utils/scaling';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { DealInfoTooltip } from '@/components/ui/DealInfoTooltip';
import { Image } from 'expo-image';
import { Button } from '@/components/ui/Button';
import { Tooltip } from '@/components/ui/Tooltip';
import { useTranslation } from 'react-i18next';
import { BackButton } from '@/components/ui/BackButton';
import { RightHeaderButton } from '@/components/ui/RightHeaderButton';
import { showErrorToast } from '@/utils/Toast';
import {
  useReserveDealMutation,
  MyDealDateTimeSlotInput,
  useMyDealsQuery,
  useDealQuery,
  DealDateTimeSlot,
  ReserveDealInput,
} from '@/graphql/generated/graphql';
import { DateItem } from '@/components/ui/DateItem';
import { TimeSlot } from '@/components/ui/TimeSlot';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import {
  renderMaxSavingTag,
  renderReuseLimitDaysTag,
  renderServiceTypeTags,
} from '@/utils/serviceTypeUtils';

export default function DealScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams<{
    dealId: string;
    myDealIdToRenew?: string;
  }>();
  const dealId = params.dealId;
  const myDealIdToRenew = params.myDealIdToRenew;

  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedDateData, setSelectedDateData] =
    useState<DealDateTimeSlot | null>(null);
  const [reserveDealMutation, { loading: reserveDealLoading }] =
    useReserveDealMutation();
  const [selectedSlot, setSelectedSlot] =
    useState<MyDealDateTimeSlotInput | null>(null);
  const { refetch } = useMyDealsQuery();

  // Ensure dealId is valid
  const validDealId = dealId && typeof dealId === 'string' ? dealId : '';

  // Fetch deal data with the query
  const { data, loading, error } = useDealQuery({
    variables: { id: validDealId },
  });

  // Sort available slots by date and filter out past dates
  const sortedAvailableSlots = useMemo(() => {
    if (!data?.deal?.available_slots) return [];

    return [...data.deal.available_slots].sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA.getTime() - dateB.getTime();
    });
  }, [data?.deal?.available_slots]);

  // Map GraphQL service_types to ServiceType interface expected by DealInfoTooltip
  const mappedServiceTypes = useMemo(() => {
    if (!data?.deal?.service_types) return null;

    return data.deal.service_types
      .filter((type) => type?.title && type?.__typename)
      .map((type) => ({
        id: `${type?.__typename}:${type?.title}`,
        title: type?.title || '',
      }));
  }, [data?.deal?.service_types]);

  const handleBack = () => {
    router.back();
  };

  const handleDateSelect = (dateData: DealDateTimeSlot) => {
    setSelectedDate(dateData.date);
    setSelectedDateData(dateData);
    setSelectedSlot(null); // Reset slot when date changes
  };

  const handleTimeSelect = (slot: { from: string; to: string }) => {
    if (selectedDate) {
      setSelectedSlot({
        date: selectedDate,
        slot: {
          from: slot.from,
          to: slot.to,
        },
      });
    }
  };

  const handleBook = async () => {
    if (!selectedSlot || !validDealId || !data?.deal?.partner_place) return;

    try {
      // Prepare mutation input
      const mutationInput: ReserveDealInput = {
        id: validDealId,
        reserve_slot: selectedSlot,
      };

      // Add myDealIdToRenew to the mutation input if it exists
      if (myDealIdToRenew) {
        mutationInput.myDealIdToRenew = myDealIdToRenew;
      }

      const response = await reserveDealMutation({
        variables: {
          input: mutationInput,
        },
      });

      if (response.data?.reserveDeal) {
        // Refresh my deals data
        refetch();

        // Get the status of the newly reserved deal
        const reservedDealStatus = response.data.reserveDeal.myDeal.status;

        router.replace({
          pathname: '/reserveSuccess',
          params: {
            partnerName: data.deal.partner_place.partner.name,
            placeName: data.deal.partner_place.name,
            placeId: data.deal.partner_place.id,
            date: selectedSlot.date,
            timeFrom: selectedSlot.slot.from,
            timeTo: selectedSlot.slot.to,
            dealStatus: reservedDealStatus, // Pass the deal status
          },
        });
      }
    } catch (error) {
      showErrorToast(
        error instanceof Error ? error.message : t('common.errors.unknown')
      );
    }
  };

  // If data is not available and there's no error, we show a loading state
  if (loading) {
    return <LoadingScreen onBack={handleBack} message={t('common.loading')} />;
  }

  const deal = data?.deal;

  // If there's no deal data or an error occurred, return the ErrorScreen
  if (error || !deal) {
    return <ErrorScreen message={t('common.notFound')} onBack={handleBack} />;
  }

  return (
    <Screen isLoading={reserveDealLoading}>
      <ScrollView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <BackButton onPress={handleBack} />
          <RightHeaderButton onPress={() => {}}>
            <View style={styles.infoButton}>
              <DealInfoTooltip
                serviceTypes={mappedServiceTypes}
                maxSaving={deal.max_saving}
                reuseLimitDays={deal.reuse_limit_days}
                iconColor={colors.base[950]}
              />
            </View>
          </RightHeaderButton>
        </View>

        {/* Deal Image */}
        <View style={styles.imageContainer}>
          <Image
            source={require('@/assets/images/deal.png')}
            style={styles.dealImage}
            contentFit='cover'
          />
        </View>

        {/* Deal Info */}
        <View style={styles.content}>
          <View style={styles.topContainer}>
            <Text
              variant='h1'
              numberOfLines={2}
              style={styles.title}
              align='center'
            >
              {deal.title}
            </Text>
            <Text
              variant='caption'
              numberOfLines={2}
              style={styles.description}
              align='center'
            >
              {deal.description}
            </Text>

            {/* Tags */}
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.tagsContainer}>
                {renderServiceTypeTags({
                  serviceTypes: deal.service_types,
                })}
                {renderMaxSavingTag(deal.max_saving || 0)}
                {renderReuseLimitDaysTag(
                  deal.is_one_time,
                  deal.reuse_limit_days
                )}
              </View>
            </ScrollView>
          </View>

          <View>
            <View style={styles.dashedLine}>
              {[...Array(30)].map((_, index) => (
                <View key={index} style={styles.dash} />
              ))}
            </View>
            {/* Left circle cutout */}
            <View style={[styles.circle, styles.leftCircle]} />
            {/* Right circle cutout */}
            <View style={[styles.circle, styles.rightCircle]} />
          </View>

          {/* Date Selection */}
          <Text variant='h3' style={styles.sectionTitle}>
            {t('deals.dealScreen.date')}
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.datesContainer}
          >
            {sortedAvailableSlots.map((dateSlot, index) => (
              <DateItem
                key={index}
                date={dateSlot.date}
                available_seats={
                  dateSlot.available_seats
                    ? Number(dateSlot.available_seats)
                    : 0
                }
                selected={selectedDate === dateSlot.date}
                onSelect={() => handleDateSelect(dateSlot)}
              />
            ))}
          </ScrollView>

          {/* Time Selection */}
          {selectedDateData && selectedDateData.slots.length > 0 && (
            <>
              <Text variant='h3' style={styles.sectionTitle}>
                {t('deals.dealScreen.time')}
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.timeSlotsContainer}
              >
                {selectedDateData.slots.map((slot, index) => (
                  <TimeSlot
                    key={index}
                    from={slot.from}
                    to={slot.to}
                    selected={
                      selectedSlot?.slot.from === slot.from &&
                      selectedSlot?.slot.to === slot.to
                    }
                    onSelect={() => handleTimeSelect(slot)}
                  />
                ))}
              </ScrollView>
            </>
          )}
        </View>
      </ScrollView>

      <View style={styles.bottomButtons}>
        <Button
          title={
            reserveDealLoading
              ? t('common.loading')
              : t('deals.dealScreen.bookButton')
          }
          onPress={handleBook}
          style={styles.bookButton}
          disabled={!selectedSlot || reserveDealLoading}
          variant='primary'
        />
        <Tooltip
          content={
            <View>
              <Text variant='h2' style={styles.termsTitle}>
                {t('deals.dealScreen.termsTitle')}
              </Text>
              <View style={styles.termsList}>
                <Text variant='caption' style={styles.termsItemText}>
                  1.{' '}
                  <Text weight='bold'>
                    {t('deals.dealScreen.terms.reservation').split(' - ')[0]}
                  </Text>
                  {' - '}
                  {t('deals.dealScreen.terms.reservation').split(' - ')[1]}
                </Text>
                <Text variant='caption' style={styles.termsItemText}>
                  2.{' '}
                  <Text weight='bold'>
                    {t('deals.dealScreen.terms.timeSlot').split(' - ')[0]}
                  </Text>
                  {' - '}
                  {t('deals.dealScreen.terms.timeSlot').split(' - ')[1]}
                </Text>
                <Text variant='caption' style={styles.termsItemText}>
                  3.{' '}
                  <Text weight='bold'>
                    {t('deals.dealScreen.terms.mandatory').split(' - ')[0]}
                  </Text>
                  {' - '}
                  {t('deals.dealScreen.terms.mandatory').split(' - ')[1]}
                </Text>
                <Text variant='caption' style={styles.termsItemText}>
                  4.{' '}
                  <Text weight='bold'>
                    {t('deals.dealScreen.terms.conditions').split(' - ')[0]}
                  </Text>
                  {' - '}
                  {t('deals.dealScreen.terms.conditions').split(' - ')[1]}
                </Text>
                <Text variant='caption' style={styles.termsItemText}>
                  5.{' '}
                  <Text weight='bold'>
                    {t('deals.dealScreen.terms.combination').split(' - ')[0]}
                  </Text>
                  {' - '}
                  {t('deals.dealScreen.terms.combination').split(' - ')[1]}
                </Text>
              </View>
            </View>
          }
        >
          <Text variant='body' style={styles.termsButton} align='center'>
            {t('deals.dealScreen.termsButton')}
          </Text>
        </Tooltip>
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.base[100],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: moderateScale(16),
    backgroundColor: colors.base[100],
  },
  infoButton: {
    padding: moderateScale(8),
  },
  imageContainer: {
    alignItems: 'center',
    marginTop: verticalScale(-48),
    marginBottom: verticalScale(16),
  },
  dealImage: {
    width: scale(189.8),
    height: verticalScale(130),
  },
  content: {
    paddingHorizontal: moderateScale(16),
  },
  topContainer: {
    alignItems: 'center',
  },
  title: {
    marginBottom: verticalScale(17),
    width: '80%',
  },
  description: {
    marginBottom: verticalScale(16),
    color: colors.darkGrey[500],
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: moderateScale(8),
    marginBottom: verticalScale(16),
  },
  dashedLine: {
    position: 'relative',
    marginBottom: moderateScale(16),
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(7.1),
    overflow: 'hidden',
    height: moderateScale(20),
  },
  dash: {
    height: 1,
    backgroundColor: colors.darkGrey[300],
    width: moderateScale(7.3),
  },
  circle: {
    position: 'absolute',
    width: moderateScale(20),
    height: moderateScale(20),
    borderRadius: moderateScale(10),
    backgroundColor: colors.softGrey[900],
  },
  leftCircle: {
    left: -moderateScale(27),
  },
  rightCircle: {
    right: -moderateScale(27),
  },
  sectionTitle: {
    marginBottom: verticalScale(8),
  },
  datesContainer: {
    marginBottom: verticalScale(18),
  },
  timeSlotsContainer: {
    marginBottom: verticalScale(24),
  },
  bottomButtons: {
    paddingHorizontal: moderateScale(16),
    paddingBottom: moderateScale(24),
    paddingTop: moderateScale(8),
    backgroundColor: colors.base[100],
  },
  bookButton: {
    marginBottom: verticalScale(16),
  },
  termsTitle: {
    marginBottom: verticalScale(16),
  },
  termsList: {
    gap: verticalScale(20),
  },
  termsItemText: {
    lineHeight: verticalScale(24),
    flexShrink: 1,
    marginBottom: verticalScale(4),
  },
  termsButton: {
    color: colors.primary[950],
    fontWeight: 'bold',
    height: verticalScale(24),
  },
});
