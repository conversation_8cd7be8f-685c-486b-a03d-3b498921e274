import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Image } from 'expo-image';
import { router, Stack, useNavigation } from 'expo-router';
import { colors } from '@/constants/Colors';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { FollowButton } from '@/components/ui/FollowButton';
import { BackButton } from '@/components/ui/BackButton';
import { moderateScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import {
  useUserFollowingQuery,
  useFollowPlaceMutation,
  useFollowCreatorMutation,
  MiniPlaceFragment,
  MiniCreatorFragment,
} from '@/graphql/generated/graphql';
import { showUnfollowConfirmation } from '@/utils/confirmations';

interface PlaceItemProps {
  place: MiniPlaceFragment;
  onToggleFollow: (placeId: string, placeName: string) => void;
  isUpdating: boolean;
}

interface CreatorItemProps {
  creator: MiniCreatorFragment;
  onToggleFollow: (creatorId: string, creatorName: string) => void;
  isUpdating: boolean;
}

const PlaceItem = ({ place, onToggleFollow, isUpdating }: PlaceItemProps) => {
  return (
    <View style={styles.followingItem}>
      <TouchableOpacity
        style={styles.followingInfo}
        onPress={() => router.push(`/places/${place.id}`)}
      >
        <View style={styles.avatarContainer}>
          {place.avatar?.full_url ? (
            <Image
              source={{ uri: place.avatar.full_url }}
              style={styles.avatar}
            />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <IconSymbol
                name='storefront.fill'
                size={24}
                color={colors.darkGrey[500]}
              />
            </View>
          )}
        </View>
        <View style={styles.followingDetails}>
          <Text variant='body' weight='bold' numberOfLines={1}>
            {place.partner.name}
          </Text>
          <Text
            variant='caption'
            color={colors.darkGrey[500]}
            numberOfLines={1}
          >
            {place.name}
          </Text>
        </View>
      </TouchableOpacity>
      <FollowButton
        isFollowing={true}
        loading={isUpdating}
        onPress={() => onToggleFollow(place.id, place.partner.name)}
        style={styles.followButton}
      />
    </View>
  );
};

const CreatorItem = ({
  creator,
  onToggleFollow,
  isUpdating,
}: CreatorItemProps) => {
  return (
    <View style={styles.followingItem}>
      <TouchableOpacity
        style={styles.followingInfo}
        onPress={() => router.push(`/creators/${creator.id}`)}
      >
        <View style={styles.avatarContainer}>
          {creator.avatar?.full_url ? (
            <Image
              source={{ uri: creator.avatar.full_url }}
              style={styles.avatar}
            />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <IconSymbol
                name='person.fill'
                size={24}
                color={colors.darkGrey[500]}
              />
            </View>
          )}
        </View>
        <View style={styles.followingDetails}>
          <Text variant='body' weight='bold' numberOfLines={1}>
            {creator.name}
          </Text>
          <Text
            variant='caption'
            color={colors.darkGrey[500]}
            numberOfLines={1}
          >
            @{creator.username}
          </Text>
        </View>
      </TouchableOpacity>
      <FollowButton
        isFollowing={true}
        loading={isUpdating}
        onPress={() => onToggleFollow(creator.id, creator.name)}
        style={styles.followButton}
      />
    </View>
  );
};

export default function FollowingScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<'places' | 'creators'>('places');
  const [updatingPlaceId, setUpdatingPlaceId] = useState<string | null>(null);
  const [updatingCreatorId, setUpdatingCreatorId] = useState<string | null>(
    null
  );

  // Handle back navigation
  const handleBack = () => {
    if (navigation.canGoBack()) {
      router.back();
    } else {
      router.replace('/(tabs)');
    }
  };

  // Fetch user's following data
  const { data: followingData, loading, refetch } = useUserFollowingQuery();

  const followingPlaces = followingData?.me?.following_places?.data || [];
  const followingCreators = followingData?.me?.following_creators?.data || [];
  const placesCount =
    followingData?.me?.following_places?.paginatorInfo?.total || 0;
  const creatorsCount =
    followingData?.me?.following_creators?.paginatorInfo?.total || 0;

  // Follow/Unfollow mutations
  const [followPlace] = useFollowPlaceMutation({
    refetchQueries: ['UserFollowing'],
    onError: (error) => {
      console.error('Error following/unfollowing place:', error);
    },
  });

  const [followCreator] = useFollowCreatorMutation({
    refetchQueries: ['UserFollowing'],
    onError: (error) => {
      console.error('Error following/unfollowing creator:', error);
    },
  });

  const handleToggleFollowPlace = (placeId: string, placeName: string) => {
    const executeUnfollow = async () => {
      setUpdatingPlaceId(placeId);

      try {
        await followPlace({
          variables: {
            input: { id: placeId },
          },
        });

        // Refetch the data to update the list
        await refetch();
      } catch {
        Alert.alert(t('common.error'), t('following.followError'), [
          { text: t('common.ok') },
        ]);
      } finally {
        setUpdatingPlaceId(null);
      }
    };

    // Show confirmation dialog for unfollowing
    showUnfollowConfirmation(t, placeName, executeUnfollow);
  };

  const handleToggleFollowCreator = (
    creatorId: string,
    creatorName: string
  ) => {
    const executeUnfollow = async () => {
      setUpdatingCreatorId(creatorId);

      try {
        await followCreator({
          variables: {
            input: { id: creatorId },
          },
        });

        // Refetch the data to update the list
        await refetch();
      } catch (error) {
        console.error('Error following/unfollowing creator:', error);
        Alert.alert(t('common.error'), t('following.unfollowCreatorError'), [
          { text: t('common.ok') },
        ]);
      } finally {
        setUpdatingCreatorId(null);
      }
    };

    // Show confirmation dialog for unfollowing
    showUnfollowConfirmation(t, creatorName, executeUnfollow);
  };

  const renderPlaceItem = ({ item }: { item: any }) => (
    <PlaceItem
      place={item}
      onToggleFollow={handleToggleFollowPlace}
      isUpdating={updatingPlaceId === item.id}
    />
  );

  const renderCreatorItem = ({ item }: { item: any }) => (
    <CreatorItem
      creator={item}
      onToggleFollow={handleToggleFollowCreator}
      isUpdating={updatingCreatorId === item.id}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <IconSymbol
        name={activeTab === 'places' ? 'storefront.fill' : 'person.2.fill'}
        size={64}
        color={colors.darkGrey[400]}
      />
      <Text variant='h3' align='center' style={styles.emptyTitle}>
        {activeTab === 'places'
          ? t('following.noFollowingPlaces')
          : t('following.noFollowingCreators')}
      </Text>
      <Text variant='caption' align='center' color={colors.darkGrey[500]}>
        {activeTab === 'places'
          ? t('following.noFollowingPlacesDescription')
          : t('following.noFollowingCreatorsDescription')}
      </Text>
    </View>
  );

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <Screen>
        {/* Custom Header */}
        <View style={styles.header}>
          <BackButton onPress={handleBack} />
          <Text variant='h2' style={styles.title}>
            {t('profile.followingList')}
          </Text>
        </View>

        {/* Tabs - Only show when not loading */}
        {!loading && (
          <View style={styles.tabsContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'places' && styles.activeTab]}
              onPress={() => setActiveTab('places')}
            >
              <Text
                variant='body'
                weight={activeTab === 'places' ? 'bold' : 'regular'}
                color={
                  activeTab === 'places'
                    ? colors.primary[950]
                    : colors.darkGrey[500]
                }
              >
                {t('following.restaurants')} ({placesCount})
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'creators' && styles.activeTab]}
              onPress={() => setActiveTab('creators')}
            >
              <Text
                variant='body'
                weight={activeTab === 'creators' ? 'bold' : 'regular'}
                color={
                  activeTab === 'creators'
                    ? colors.primary[950]
                    : colors.darkGrey[500]
                }
              >
                {t('following.creators')} ({creatorsCount})
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size='large' color={colors.primary[950]} />
          </View>
        ) : (
          <View style={styles.content}>
            {activeTab === 'places' ? (
              <FlatList
                data={followingPlaces}
                renderItem={renderPlaceItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={renderEmptyState}
              />
            ) : (
              <FlatList
                data={followingCreators}
                renderItem={renderCreatorItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={renderEmptyState}
              />
            )}
          </View>
        )}
      </Screen>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
    paddingTop: moderateScale(16),
    paddingBottom: moderateScale(8),
  },
  title: {
    flex: 1,
    textAlign: 'center',
    marginRight: moderateScale(40), // Compensate for back button width
  },
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: moderateScale(20),
    paddingVertical: moderateScale(8),
  },
  tab: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(10),
  },
  activeTab: {
    borderBottomWidth: moderateScale(2),
    borderBottomColor: colors.primary[950],
  },
  content: {
    flex: 1,
  },
  listContainer: {
    paddingTop: moderateScale(16),
    paddingBottom: moderateScale(100),
  },
  followingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: moderateScale(20),
    paddingVertical: moderateScale(16),
    borderBottomWidth: 1,
    borderBottomColor: colors.softGrey[200],
  },
  followingInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: moderateScale(12),
  },
  avatar: {
    width: moderateScale(48),
    height: moderateScale(48),
    borderRadius: moderateScale(24),
    backgroundColor: colors.softGrey[400],
  },
  avatarPlaceholder: {
    width: moderateScale(48),
    height: moderateScale(48),
    borderRadius: moderateScale(24),
    backgroundColor: colors.softGrey[400],
    alignItems: 'center',
    justifyContent: 'center',
  },
  followingDetails: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: moderateScale(40),
    paddingTop: moderateScale(100),
  },
  emptyTitle: {
    marginTop: moderateScale(16),
    marginBottom: moderateScale(8),
  },
  followButton: {
    marginLeft: moderateScale(12),
  },
});
