import { useColorScheme } from '@/hooks/useColorScheme';
import { client } from '@/apollo/client';
import { ApolloProvider } from '@apollo/client';
import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack, useRouter } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';
import '../i18n';
import { Host } from 'react-native-portalize';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StyleSheet } from 'react-native';
import { RootSiblingParent } from 'react-native-root-siblings';
import { AuthInitializer } from '@/components/AuthInitializer';
import { InviteHandler } from '@/components/InviteHandler';
import { ScreenTracker } from '@/components/ScreenTracker';
import {
  initializeBranch,
  setBranchRouter,
  processBranchParams,
} from '@/services/branch';
import { segmentService } from '@/services/segment';
import { initNetworkService } from '@/services/network';
import * as Sentry from '@sentry/react-native';
import Constants from 'expo-constants';
import * as Application from 'expo-application';

const version = Application.nativeApplicationVersion;
const buildNumber = Application.nativeBuildVersion;

Sentry.init({
  dsn: Constants.expoConfig?.extra?.SENTRY_DSN,

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  // Configure Session Replay 100% in production
  replaysSessionSampleRate:
    Constants.expoConfig?.extra?.ENV === 'production' ? 1 : 0,
  replaysOnErrorSampleRate: 1,
  integrations: [
    Sentry.mobileReplayIntegration({
      maskAllText: false,
      maskAllImages: false,
      maskAllVectors: false,
    }),
    Sentry.feedbackIntegration(),
  ],

  environment: Constants.expoConfig?.extra?.ENV,
  release: `conari@${version}`,
  dist: buildNumber || 'unknown',
});

if (__DEV__) {
  require('@/config/ReactotronConfig');
  console.log('Reactotron Configured');
}

SplashScreen.preventAutoHideAsync();

export default Sentry.wrap(function RootLayout() {
  const colorScheme = useColorScheme();
  const router = useRouter();

  const [loaded] = useFonts({
    Coolvetica: require('@/assets/fonts/CoolveticaRg-Regular.ttf'),
    Urbanist: require('@/assets/fonts/Urbanist-VariableFont_wght.ttf'),
  });

  useEffect(() => {
    if (!loaded) return;

    // Hide splash after fonts load
    setTimeout(() => SplashScreen.hideAsync(), 1000);
    // Initialize Segment
    segmentService.initialize();

    // Initialize Network Service
    initNetworkService();

    // Initialize Branch.io but don't set router yet
    // Router setup will be handled after AuthInitializer finishes
    if (__DEV__) {
      console.log('[RootLayout] Initializing Branch');
    }
    initializeBranch();
  }, [loaded, router]);

  if (!loaded) {
    return null;
  }

  // Handler to set up Branch router after AuthInitializer is done
  const handleAuthInitializerReady = () => {
    // Set up Branch router and process any pending params
    setBranchRouter(router);
    processBranchParams();
  };

  return (
    <ApolloProvider client={client}>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <GestureHandlerRootView style={styles.container}>
          <RootSiblingParent>
            <Host>
              <AuthInitializer onReady={handleAuthInitializerReady}>
                <Stack
                  screenOptions={{
                    headerShown: false,
                  }}
                >
                  <Stack.Screen
                    name='(auth)'
                    options={{
                      animation: 'none',
                    }}
                  />
                  <Stack.Screen
                    name='(tabs)'
                    options={{
                      headerShown: false,
                      gestureEnabled: false,
                      animation: 'fade',
                    }}
                  />
                  <Stack.Screen
                    name='place'
                    options={{
                      animation: 'slide_from_right',
                      animationDuration: 300,
                      presentation: 'card',
                      gestureEnabled: true,
                      gestureDirection: 'horizontal',
                    }}
                  />
                  <Stack.Screen
                    name='(modals)'
                    options={{
                      presentation: 'modal',
                      animation: 'slide_from_bottom',
                    }}
                  />
                </Stack>
                <StatusBar style='auto' />
              </AuthInitializer>
              <InviteHandler />
              <ScreenTracker />
            </Host>
          </RootSiblingParent>
        </GestureHandlerRootView>
        <StatusBar style='auto' />
      </ThemeProvider>
    </ApolloProvider>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
