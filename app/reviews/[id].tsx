import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { BackButton } from '@/components/ui/BackButton';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { ErrorScreen } from '@/components/ui/ErrorScreen';
import { Review } from '@/components/ui/Review';
import { useReviewsQuery, ReviewFragment } from '@/graphql/generated/graphql';

export default function ReviewsScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { id } = useLocalSearchParams();

  const { data, loading, error } = useReviewsQuery({
    variables: {
      placeId: id as string,
      first: 100,
    },
    fetchPolicy: 'cache-and-network',
  });

  const handleBack = () => {
    router.back();
  };

  const renderReview = ({ item }: { item: ReviewFragment }) => (
    <Review review={item} placeId={Array.isArray(id) ? id[0] : id} />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text variant='body' color={colors.darkGrey[500]} align='center'>
        {t('place.reviews.noReviews')}
      </Text>
      <Text
        variant='caption'
        color={colors.darkGrey[400]}
        align='center'
        style={styles.emptySubtext}
      >
        {t('place.reviews.noReviewsSubtext')}
      </Text>
    </View>
  );

  if (loading) {
    return <LoadingScreen onBack={handleBack} message={t('common.loading')} />;
  }

  if (error) {
    return <ErrorScreen message={t('common.error')} onBack={handleBack} />;
  }

  return (
    <Screen>
      <View style={styles.header}>
        <BackButton onPress={handleBack} />
        <Text variant='h2' style={styles.title}>
          {t('place.sections.reviews')}
        </Text>
        <View style={styles.placeholder} />
      </View>

      <FlatList
        data={data?.reviewsByPlace?.data || []}
        renderItem={renderReview}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />
    </Screen>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(12),
    backgroundColor: colors.base[100],
  },
  title: {
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: moderateScale(40),
  },
  listContainer: {
    paddingHorizontal: moderateScale(16),
    paddingTop: moderateScale(16),
    paddingBottom: moderateScale(24),
  },
  emptyContainer: {
    paddingVertical: moderateScale(48),
    alignItems: 'center',
  },
  emptySubtext: {
    marginTop: moderateScale(8),
  },
});
