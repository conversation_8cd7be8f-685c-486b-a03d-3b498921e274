import { StyleSheet, View } from 'react-native';
import { Text } from '@/components/ui/Text';
import { Screen } from '@/components/ui/Screen';
import { Button } from '@/components/ui/Button';
import { colors } from '@/constants/Colors';
import { useTranslation } from 'react-i18next';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { useRouter } from 'expo-router';
import { useAuthStore } from '@/store/auth';
import { Image } from 'expo-image';
import { DealsOutline } from '@/components/icons/tabs/DealsOutline';
import { CollectionsOutline } from '@/components/icons/tabs/CollectionsOutline';
import { showSuccessToast } from '@/utils/Toast';

export default function TrialModal() {
  const { t } = useTranslation();
  const router = useRouter();
  const { setTrialModalOpened } = useAuthStore();

  const handleStartTrial = () => {
    // Mark trial modal as opened so it won't show again
    setTrialModalOpened();

    // Show success toast
    showSuccessToast(t('trial.modal.successToast'));

    // Close the modal and continue with the original flow
    router.back();
  };

  return (
    <Screen withSafeArea={true} style={styles.screen}>
      <View style={styles.container}>
        <Image
          source={require('@/assets/images/trial.png')}
          style={styles.image}
          contentFit='contain'
        />

        <Text variant='h1_5' style={styles.title} color={colors.base[100]}>
          {t('trial.modal.title')}
        </Text>

        <Text
          variant='body'
          style={styles.subtitle}
          align='center'
          color={colors.base[100]}
        >
          {t('trial.modal.subtitle')}
        </Text>

        <View style={styles.trialContainer}>
          <Text variant='h3' color={colors.base[100]} align='center'>
            {t('trial.modal.trialDuration')}
          </Text>
          <Text variant='caption' color={colors.base[100]} align='center'>
            {t('trial.modal.noPayment')}
          </Text>
        </View>

        <View style={styles.itemsContainer}>
          <View style={styles.item}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: colors.secondary[500] },
              ]}
            >
              <DealsOutline />
            </View>
            <Text variant='body' color={colors.base[100]} weight='bold'>
              {t('trial.modal.features.deals')}
            </Text>
          </View>
          <View style={styles.item}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: colors.yellow[700] },
              ]}
            >
              <CollectionsOutline color={colors.primary[950]} />
            </View>
            <Text variant='body' color={colors.base[100]} weight='bold'>
              {t('trial.modal.features.collections')}
            </Text>
          </View>
          <View style={styles.item}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: colors.green[400] },
              ]}
            >
              <IconSymbol name='hourglass' size={24} color={colors.base[100]} />
            </View>
            <Text variant='body' color={colors.base[100]} weight='bold'>
              {t('trial.modal.features.cancel')}
            </Text>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            style={styles.button}
            variant='outline'
            title={t('trial.modal.startButton')}
            onPress={handleStartTrial}
          />

          <Text
            variant='caption1'
            style={styles.underButtonText}
            color={colors.base[100]}
          >
            {t('trial.modal.disclaimer')}
          </Text>
        </View>
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  screen: {
    backgroundColor: colors.primary[950],
  },
  container: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
  },
  image: {
    width: moderateScale(200),
    height: verticalScale(120),
    marginTop: verticalScale(30),
    marginBottom: verticalScale(30),
  },
  title: {
    marginBottom: verticalScale(16),
  },
  subtitle: {
    marginBottom: verticalScale(22),
    width: '78%',
  },
  trialContainer: {
    backgroundColor: colors.primary[800],
    width: '100%',
    height: verticalScale(72),
    borderRadius: moderateScale(18),
    justifyContent: 'center',
    alignItems: 'center',
    gap: verticalScale(8),
  },
  itemsContainer: {
    width: '100%',
    marginTop: verticalScale(20),
    gap: verticalScale(24),
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: verticalScale(16),
  },
  iconContainer: {
    width: moderateScale(40),
    height: verticalScale(40),
    borderRadius: moderateScale(10),
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    gap: verticalScale(12),
    bottom: verticalScale(65),
    position: 'absolute',
  },
  button: {
    width: '100%',
  },
  underButtonText: {
    textAlign: 'center',
    width: '95%',
  },
});
