import React, { useState, useMemo, useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity, TextInput } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { Button } from '@/components/ui/Button';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import {
  MyDeal as GraphQLMyDeal,
  ReviewFragment,
} from '@/graphql/generated/graphql';
import {
  useSubmitReviewMutation,
  useUpdateReviewMutation,
  usePlaceQuery,
} from '@/graphql/generated/graphql';
import { Image } from 'expo-image';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import AntDesign from '@expo/vector-icons/AntDesign';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { useLocationServices } from '@/services/locationServices';
import { client } from '@/apollo/client';
import { showSuccessToast } from '@/utils/Toast';
import { router, useLocalSearchParams } from 'expo-router';
import { MyDealStatusEnum } from '@/graphql/generated/graphql';

const MAX_REVIEW_LENGTH = 280;

export default function ReviewModal() {
  const { t } = useTranslation();
  const params = useLocalSearchParams<{
    myDeal?: string;
    review?: string;
    placeId?: string;
    isEditing?: string;
    isInRedeemScreen?: string;
  }>();

  const myDeal: GraphQLMyDeal | undefined = params.myDeal
    ? JSON.parse(params.myDeal)
    : undefined;
  const review: ReviewFragment | undefined = params.review
    ? JSON.parse(params.review)
    : undefined;
  const placeId = params.placeId;
  const isEditing = params.isEditing === 'true';
  const isInRedeemScreen = params.isInRedeemScreen === 'true';

  const [rating, setRating] = useState(isEditing ? review?.rating || 0 : 0);
  const [savingsAmount, setSavingsAmount] = useState(
    isEditing ? review?.savings_amount || 0 : myDeal?.deal.max_saving || 0
  );
  const [wouldVisitAgain, setWouldVisitAgain] = useState<boolean | null>(
    isEditing ? (review?.would_visit_again ?? null) : null
  );
  const [reviewText, setReviewText] = useState(
    isEditing ? review?.review_text || '' : ''
  );

  const [submitReview, { loading: submitLoading }] = useSubmitReviewMutation();
  const [updateReview, { loading: updateLoading }] = useUpdateReviewMutation();

  // Fetch place data when editing
  const { data: placeData } = usePlaceQuery({
    variables: { id: placeId || '' },
    skip: !isEditing || !placeId,
  });

  const loading = submitLoading || updateLoading;

  // Add location services
  const locationServices = useLocationServices();

  const getRatingSubtext = (rating: number) => {
    if (rating <= 2) return t('reviewModal.ratingSubtext.couldBeBetter');
    if (rating === 3) return t('reviewModal.ratingSubtext.justOkay');
    if (rating === 4) return t('reviewModal.ratingSubtext.reallyGood');
    if (rating === 5) return t('reviewModal.ratingSubtext.lovedIt');
    return '';
  };

  const renderRating = useCallback((rating: number, reviews_count: number) => {
    return (
      <View style={styles.ratingContainer}>
        <AntDesign
          name='google'
          size={moderateScale(8)}
          color={colors.base[950]}
        />
        <Text variant='tiny' color={colors.base[950]}>
          {rating}
        </Text>
        <AntDesign
          name='star'
          size={moderateScale(8)}
          color={colors.yellow[950]}
        />
        <Text variant='tiny' color={colors.base[950]}>
          ({reviews_count})
        </Text>
      </View>
    );
  }, []);

  // Get place data for both editing and creating modes
  const place = isEditing
    ? placeData?.partnerPlace
    : myDeal?.deal.partner_place;

  // Calculate distance - similar to Reel component
  const calculatedDistance = useMemo(() => {
    // If we have user location and place coordinates, calculate distance
    if (
      locationServices.locationPermissionStatus === 'granted' &&
      place?.location.lat &&
      place?.location.lng
    ) {
      const distance = locationServices.calculateDistanceToPlace(
        place.location.lat,
        place.location.lng
      );
      return distance ? distance.toFixed(1) : null;
    }

    return null;
  }, [
    locationServices.locationPermissionStatus,
    locationServices.calculateDistanceToPlace,
    place?.location.lat,
    place?.location.lng,
  ]);

  const handleSubmit = async () => {
    try {
      if (isEditing && review) {
        // Update existing review
        const input: any = {
          id: review.id,
          rating,
          savings_amount: savingsAmount,
          review_text: reviewText.trim() || undefined,
        };

        if (wouldVisitAgain !== null) {
          input.would_visit_again = wouldVisitAgain;
        }

        await updateReview({
          variables: { input },
        });
      } else if (myDeal) {
        // Create new review
        const input: any = {
          deal_id: myDeal.deal.id,
          rating,
          savings_amount: savingsAmount,
          review_text: reviewText.trim() || undefined,
        };

        if (wouldVisitAgain !== null) {
          input.would_visit_again = wouldVisitAgain;
        }

        await submitReview({
          variables: { input },
        });
      }

      // Invalidate caches to trigger refetch
      client.cache.evict({ fieldName: 'myDeals' });
      client.cache.evict({ fieldName: 'reviewsByPlace' });
      client.cache.gc();

      // Show success toast and navigate to place
      showSuccessToast(
        t(isEditing ? 'reviewModal.updateSuccess' : 'reviewModal.success')
      );

      // Navigate to place screen and scroll to reviews
      navigateToPlace();
    } catch (error) {
      console.log('Failed to submit review:', error);
    }
  };

  const resetForm = () => {
    setRating(isEditing ? review?.rating || 0 : 0);
    setSavingsAmount(
      isEditing ? review?.savings_amount || 0 : myDeal?.deal.max_saving || 0
    );
    setWouldVisitAgain(isEditing ? (review?.would_visit_again ?? null) : null);
    setReviewText(isEditing ? review?.review_text || '' : '');
  };

  const navigateToPlace = () => {
    resetForm();
    if (!place?.id) return;

    // First dismiss the modal, then navigate to place screen
    router.dismiss();

    router.replace({
      pathname: '/places/[id]',
      params: {
        id: place.id,
        scrollToReviews: 'true',
      },
    });
  };

  const handleClose = () => {
    resetForm();
    if (isInRedeemScreen) {
      // Navigate to deals screen after review modal closes
      router.replace({
        pathname: '/(tabs)/deals',
        params: {
          activeFilter: MyDealStatusEnum.Redeemed,
        },
      });
    } else {
      router.back();
    }
  };

  const handleSavingsChange = (values: number[]) => {
    setSavingsAmount(values[0]);
  };

  // Don't render if we don't have the required data
  if (!place) {
    return null;
  }

  return (
    <Screen>
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text variant='h2' align='center'>
              {t(isEditing ? 'reviewModal.editTitle' : 'reviewModal.title')}
            </Text>
          </View>
        </View>

        <KeyboardAwareScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps='handled'
          enableOnAndroid={true}
          extraHeight={100}
          extraScrollHeight={100}
        >
          {/* Partner Section */}
          <View style={styles.partnerSection}>
            <Image
              source={{ uri: place.avatar.full_url }}
              style={styles.partnerAvatar}
              contentFit='cover'
            />
            <View style={styles.partnerInfo}>
              <View style={styles.partnerHeader}>
                <Text
                  variant='caption'
                  color={colors.base[950]}
                  weight='bold'
                  numberOfLines={1}
                >
                  {place.partner.name}
                </Text>
                <Text
                  variant='caption1'
                  color={colors.base[950]}
                  numberOfLines={1}
                >
                  {place.name}
                </Text>
              </View>
              <View style={styles.partnerDetails}>
                {renderRating(
                  Number(place.rates?.google) || 0,
                  place.rates?.reviews_count || 0
                )}
                {calculatedDistance && (
                  <View style={styles.locationContainer}>
                    <FontAwesome6
                      name='location-dot'
                      size={moderateScale(8)}
                      color={colors.primary[950]}
                    />
                    <Text
                      variant='tiny'
                      color={colors.base[950]}
                      numberOfLines={1}
                    >
                      {`${calculatedDistance} ${t('common.km')} `}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          {/* Rating Section */}
          <View style={styles.section}>
            <Text variant='h2' align='center'>
              {t('reviewModal.ratingTitle')}
            </Text>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <TouchableOpacity
                  key={star}
                  onPress={() => setRating(star)}
                  style={styles.starButton}
                >
                  <AntDesign
                    name={star <= rating ? 'star' : 'staro'}
                    size={48}
                    color={
                      star <= rating ? colors.yellow[900] : colors.darkGrey[300]
                    }
                  />
                </TouchableOpacity>
              ))}
            </View>
            {rating > 0 && (
              <Text variant='caption' color={colors.darkGrey[700]}>
                {getRatingSubtext(rating)}
              </Text>
            )}
          </View>

          {/* Savings Slider Section */}
          <View style={styles.section}>
            <Text variant='h2'>{t('reviewModal.savingsTitle')}</Text>
            <View style={styles.sliderContainer}>
              <View style={styles.sliderValueContainer}>
                <Image
                  source={require('@/assets/images/dirham.avif')}
                  style={styles.currencyIcon}
                  tintColor={colors.primary[950]}
                />
                <Text variant='h1' color={colors.primary[950]}>
                  {savingsAmount}
                </Text>
              </View>

              <MultiSlider
                values={[savingsAmount]}
                min={10}
                max={
                  isEditing
                    ? (review?.savings_amount || 100) + 50
                    : (myDeal?.deal.max_saving || 100) + 50
                }
                step={1}
                sliderLength={moderateScale(280)}
                onValuesChange={handleSavingsChange}
                selectedStyle={{ backgroundColor: colors.primary[950] }}
                unselectedStyle={{ backgroundColor: colors.primary[300] }}
                containerStyle={styles.slider}
                trackStyle={styles.track}
                markerStyle={styles.marker}
              />
            </View>
          </View>

          {/* Would Visit Again Section */}
          <View style={styles.section}>
            <Text variant='h2'>{t('reviewModal.wouldVisitAgainTitle')}</Text>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[
                  styles.yesNoButton,
                  wouldVisitAgain === true && styles.yesNoButtonActive,
                ]}
                onPress={() => setWouldVisitAgain(true)}
              >
                <Text
                  variant='caption'
                  color={colors.primary[950]}
                  style={[
                    wouldVisitAgain === true && styles.yesNoButtonTextActive,
                  ]}
                >
                  {t('reviewModal.yes')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.yesNoButton,
                  wouldVisitAgain === false && styles.yesNoButtonActive,
                ]}
                onPress={() => setWouldVisitAgain(false)}
              >
                <Text
                  variant='caption'
                  color={colors.primary[950]}
                  style={[
                    wouldVisitAgain === false && styles.yesNoButtonTextActive,
                  ]}
                >
                  {t('reviewModal.no')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Review Text Section */}
          <View style={styles.section}>
            <Text variant='h2'>{t('reviewModal.reviewTextTitle')}</Text>
            <View style={styles.textInputContainer}>
              <TextInput
                style={styles.textInput}
                multiline
                numberOfLines={4}
                placeholder={t('reviewModal.reviewTextPlaceholder')}
                placeholderTextColor={colors.darkGrey[500]}
                value={reviewText}
                onChangeText={setReviewText}
                textAlignVertical='top'
                maxLength={MAX_REVIEW_LENGTH}
                scrollEnabled={false}
              />
              <Text
                variant='caption'
                color={
                  reviewText.length > MAX_REVIEW_LENGTH * 0.8
                    ? colors.info.yellow
                    : colors.darkGrey[600]
                }
                style={styles.charCounter}
              >
                {reviewText.length}/{MAX_REVIEW_LENGTH}
              </Text>
            </View>
          </View>

          {/* Submit Button */}
          <View style={styles.buttonContainer}>
            <Button
              title={
                loading
                  ? t('reviewModal.submitting')
                  : t('reviewModal.submitButton')
              }
              variant='primary'
              onPress={handleSubmit}
              disabled={loading || rating === 0}
              loading={loading}
            />
            <Button
              title={t('common.cancel')}
              variant='secondary'
              onPress={handleClose}
            />
          </View>
        </KeyboardAwareScrollView>
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: moderateScale(20),
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: moderateScale(20),
    paddingBottom: verticalScale(40),
  },
  partnerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.softGrey[400],
    padding: moderateScale(16),
    borderRadius: moderateScale(24),
    marginBottom: moderateScale(40),
    borderWidth: 1,
    borderColor: colors.softGrey[300],
    height: verticalScale(88),
  },
  partnerAvatar: {
    width: moderateScale(64),
    height: moderateScale(64),
    borderRadius: moderateScale(32),
    marginRight: moderateScale(12),
  },
  partnerInfo: {
    flex: 1,
    gap: moderateScale(4),
  },
  partnerHeader: {
    gap: moderateScale(2),
  },
  partnerDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
  },
  partnerName: {
    marginBottom: moderateScale(2),
  },
  placeName: {
    color: colors.darkGrey[600],
  },
  section: {
    marginBottom: moderateScale(32),
    alignItems: 'center',
    gap: moderateScale(8),
  },

  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  starButton: {
    padding: moderateScale(8),
  },

  sliderContainer: {
    alignItems: 'center',
    marginTop: moderateScale(18),
    width: '100%',
  },

  slider: {
    width: '100%',
    height: moderateScale(40),
    alignItems: 'center',
    justifyContent: 'center',
  },
  track: {
    height: moderateScale(8),
    borderRadius: moderateScale(8),
  },
  marker: {
    position: 'relative',
    top: moderateScale(4),
    height: moderateScale(16),
    width: moderateScale(16),
    borderRadius: moderateScale(8),
    backgroundColor: colors.base[100],
    borderWidth: moderateScale(2),
    borderColor: colors.primary[700],
    shadowColor: colors.primary[950],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sliderValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
  },
  currencyIcon: {
    width: moderateScale(20),
    height: moderateScale(20),
    resizeMode: 'contain',
    marginTop: moderateScale(2),
  },
  buttonRow: {
    marginTop: moderateScale(8),
    flexDirection: 'row',
    gap: moderateScale(16),
    justifyContent: 'center',
  },
  yesNoButton: {
    paddingHorizontal: moderateScale(28),
    paddingVertical: moderateScale(10),
    borderRadius: moderateScale(24),
    borderWidth: 1,
    borderColor: colors.primary[950],
    alignItems: 'center',
    backgroundColor: colors.base[100],
    minWidth: moderateScale(80),
  },
  yesNoButtonActive: {
    backgroundColor: colors.primary[950],
    borderColor: colors.primary[950],
  },

  yesNoButtonTextActive: {
    color: colors.base[100],
  },
  textInputContainer: {
    width: '100%',
    marginTop: moderateScale(8),
  },
  textInput: {
    backgroundColor: colors.softGrey[400],
    borderRadius: moderateScale(18),
    padding: moderateScale(16),
    fontSize: moderateScale(14),
    color: colors.base[950],
    minHeight: moderateScale(160),
    maxHeight: moderateScale(240),
    borderWidth: 1,
    borderColor: colors.softGrey[300],
    width: '100%',
  },
  buttonContainer: {
    gap: verticalScale(12),
    paddingBottom: verticalScale(60),
  },
  charCounter: {
    textAlign: 'right',
    marginTop: moderateScale(8),
  },
});

// This tells expo-router to treat this screen as a modal
export const dynamic = 'force-dynamic';

export const unstable_settings = {
  presentation: 'modal',
};
