import { useEffect, useCallback } from 'react';
import { StyleSheet, View, FlatList, Alert } from 'react-native';
import { Screen } from '@/components/ui/Screen';
import { CollectionCard } from '@/components/CollectionCard';
import {
  useCollectionsQuery,
  useAddItemToCollectionMutation,
  useRemoveItemFromCollectionMutation,
  useRemoveItemFromAllCollectionsMutation,
  CollectableType,
} from '@/graphql/generated/graphql';
import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Text } from '@/components/ui/Text';
import { Button } from '@/components/ui/Button';
// import { Image } from 'expo-image';
import { colors } from '@/constants/Colors';
// import { SaveIcon } from '@/components/icons/SaveIcon';
import { useCollectionsStore } from '@/store/collections';

export default function CollectionsScreen() {
  const {
    id,
    // avatar
  } = useLocalSearchParams<{
    id: string;
    // avatar: string;
  }>();
  const router = useRouter();
  const { t } = useTranslation();

  // Use Zustand store for collections state
  const {
    selectedCollections,
    newlyCreatedCollectionId,
    setSelectedCollections,
    toggleCollection,
    clearNewlyCreatedCollection,
    resetSelections,
  } = useCollectionsStore();

  // Query collections
  const { data, loading, refetch } = useCollectionsQuery({
    variables: {
      collectableId: id,
    },
  });

  // Refresh collections data when screen is focused
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  // Initialize collection states from API data
  useEffect(() => {
    if (data?.collections) {
      // Start with API data (what's actually saved)
      const apiState: { [collectionId: string]: boolean } = {};
      data.collections.forEach((collection) => {
        apiState[collection.id] = collection.is_item_exists || false;
      });

      // Get current selections from store
      const currentSelections = selectedCollections;

      // Merge with existing selected collections (preserves user selections)
      const mergedState = { ...apiState };
      Object.keys(currentSelections).forEach((collectionId) => {
        if (data.collections.some((c) => c.id === collectionId)) {
          mergedState[collectionId] = currentSelections[collectionId];
        }
      });

      setSelectedCollections(mergedState);
    }
  }, [data]); // Only depend on data changes

  // Handle newly created collection auto-selection
  useEffect(() => {
    if (newlyCreatedCollectionId && data?.collections) {
      if (data.collections.some((c) => c.id === newlyCreatedCollectionId)) {
        toggleCollection(newlyCreatedCollectionId, true);
        clearNewlyCreatedCollection();
      }
    }
  }, [newlyCreatedCollectionId, data?.collections]); // Separate effect for new collection handling

  // Cleanup selected collections when component unmounts (modal closes)
  useEffect(() => {
    return () => {
      resetSelections();
    };
  }, [resetSelections]);

  // Mutations with automatic refetching of ReelStatus
  const [addItemToCollection, { loading: addLoading }] =
    useAddItemToCollectionMutation({
      refetchQueries: ['ReelStatus', 'Place', 'Collection', 'Collections'],
    });
  const [removeItemFromCollection, { loading: removeLoading }] =
    useRemoveItemFromCollectionMutation({
      refetchQueries: ['ReelStatus', 'Place', 'Collection', 'Collections'],
    });
  const [removeItemFromAllCollections, { loading: removeAllLoading }] =
    useRemoveItemFromAllCollectionsMutation({
      refetchQueries: ['ReelStatus', 'Place', 'Collection', 'Collections'],
    });

  const handleNewCollection = () => {
    router.push('/(modals)/collection');
  };

  // Handle collection toggle using Zustand store
  const handleCollectionToggle = (
    collectionId: string,
    isSelected: boolean
  ) => {
    toggleCollection(collectionId, isSelected);
  };

  // Add place to collections
  const handleAddPlaceToCollection = async () => {
    if (!id) return;

    try {
      // Determine which collections to add to and which to remove from
      const addToCollections: string[] = [];
      const removeFromCollections: string[] = [];

      Object.entries(selectedCollections).forEach(
        ([collectionId, isSelected]) => {
          const initialState =
            data?.collections?.find((c) => c.id === collectionId)
              ?.is_item_exists || false;

          if (isSelected && !initialState) {
            addToCollections.push(collectionId);
          } else if (!isSelected && initialState) {
            removeFromCollections.push(collectionId);
          }
        }
      );

      // Add to collections if needed
      if (addToCollections.length > 0) {
        await addItemToCollection({
          variables: {
            input: {
              collectable_id: id,
              collectable_type: CollectableType.PartnerLocation,
              collection_ids: addToCollections,
            },
          },
        });
      }

      // Remove from collections if needed
      if (removeFromCollections.length > 0) {
        await removeItemFromCollection({
          variables: {
            input: {
              collectable_id: id,
              collectable_type: CollectableType.PartnerLocation,
              collection_ids: removeFromCollections,
            },
          },
        });
      }

      // Reset the store state and close the modal
      resetSelections();
      router.back();
    } catch (error) {
      console.error('Error updating collections:', error);
    }
  };

  const removePlaceFromAllCollections = async () => {
    if (!id) return;

    try {
      await removeItemFromAllCollections({
        variables: {
          input: id,
        },
      });

      resetSelections();
      router.back();
    } catch (error) {
      console.error('Error removing place from all collections:', error);
    }
  };

  const showConfirmRemoveFromAll = () => {
    Alert.alert(
      t('collections.removePlaceFromAllCollections.title'),
      t('collections.removePlaceFromAllCollections.description'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: removePlaceFromAllCollections,
        },
      ]
    );
  };

  // Create combined data array with collections and new collection item
  const combinedData = data?.collections
    ? [...data.collections, { id: 'new-collection', isNew: true }]
    : [];

  const renderItem = ({ item }: { item: any }) => {
    if (item.isNew) {
      return <CollectionCard isNew onPress={handleNewCollection} />;
    }
    return (
      <CollectionCard
        collection={item}
        showActions
        isInCollection={selectedCollections[item.id] || false}
        onToggle={(isSelected) => handleCollectionToggle(item.id, isSelected)}
      />
    );
  };

  const isMutationLoading = addLoading || removeLoading || removeAllLoading;

  return (
    <Screen isLoading={loading || isMutationLoading}>
      <View>
        <View style={styles.header}>
          <Text variant='h2' align='center'>
            {t('collections.title')}
          </Text>
        </View>

        <FlatList
          data={combinedData}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          numColumns={2}
          columnWrapperStyle={styles.row}
          contentContainerStyle={styles.container}
          // ListHeaderComponent={
          //   <View style={styles.allPlacesContainer}>
          //     <View style={styles.leftContainer}>
          //       <Image source={{ uri: avatar }} style={styles.avatar} />
          //       <View>
          //         <Text variant='h3'>{t('collections.saved')}</Text>
          //         <Text variant='caption1' color={colors.base[950]}>
          //           {t('collections.allPlaces')}
          //         </Text>
          //       </View>
          //     </View>
          //     <TouchableOpacity onPress={() => showConfirmRemoveFromAll()}>
          //       <SaveIcon color={colors.primary[950]} />
          //     </TouchableOpacity>
          //   </View>
          // }
        />
      </View>

      {/* save button */}
      <View style={styles.saveButton}>
        <Button
          onPress={handleAddPlaceToCollection}
          variant='primary'
          title={t('common.save')}
          loading={isMutationLoading}
        />
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: moderateScale(20),
    marginTop: moderateScale(24),
    marginBottom: moderateScale(16),
  },
  container: {
    padding: moderateScale(16),
    paddingBottom: moderateScale(200),
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: moderateScale(12),
  },
  saveButton: {
    paddingHorizontal: moderateScale(20),
    position: 'absolute',
    bottom: verticalScale(100),
    left: 0,
    right: 0,
  },
  allPlacesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: moderateScale(16),
    paddingHorizontal: moderateScale(8),
    borderBottomWidth: 1,
    borderColor: colors.softGrey[950],
    paddingBottom: moderateScale(16),
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(10),
  },
  avatar: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
  },
});

// This tells expo-router to treat this screen as a modal
export const dynamic = 'force-dynamic';

export const unstable_settings = {
  presentation: 'modal',
};
