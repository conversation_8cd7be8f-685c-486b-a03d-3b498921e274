import { useState } from 'react';
import { router, useLocalSearchParams } from 'expo-router';
import {
  View,
  StyleSheet,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { colors } from '@/constants/Colors';
import { Screen } from '@/components/ui/Screen';
import { Text } from '@/components/ui/Text';
import { Button } from '@/components/ui/Button';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { useMutation } from '@apollo/client';
import {
  CreateCollectionDocument,
  CreateCollectionMutationVariables,
  EditCollectionDocument,
  EditCollectionMutationVariables,
  useCollectionsQuery,
  useCollectionQuery,
} from '@/graphql/generated/graphql';
import { useTranslation } from 'react-i18next';
import { useCollectionsStore } from '@/store/collections';

const MAX_TITLE_LENGTH = 50;
const MAX_DESCRIPTION_LENGTH = 80;

export default function CollectionModal() {
  const { t } = useTranslation();
  const { collectionId } = useLocalSearchParams<{
    collectionId: string;
  }>();
  const { setNewlyCreatedCollection } = useCollectionsStore();
  const isEditMode = Boolean(collectionId);

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [titleFocused, setTitleFocused] = useState(false);
  const [descriptionFocused, setDescriptionFocused] = useState(false);

  // Query for editing existing collection
  useCollectionQuery({
    variables: { id: collectionId || '' },
    skip: !isEditMode,
    onCompleted: (data) => {
      if (data.collection) {
        setTitle(data.collection.title);
        setDescription(data.collection.description || '');
      }
    },
  });

  const { refetch } = useCollectionsQuery();

  // Create collection mutation
  const [createCollection, { loading: createLoading }] = useMutation(
    CreateCollectionDocument,
    {
      onCompleted: (data) => {
        refetch();
        // Store the new collection ID in Zustand store for auto-selection
        if (data.createCollection?.id) {
          setNewlyCreatedCollection(data.createCollection.id);
        }
        router.back();
      },
    }
  );

  // Edit collection mutation
  const [editCollection, { loading: editLoading }] = useMutation(
    EditCollectionDocument,
    {
      onCompleted: () => {
        router.back();
      },
    }
  );

  const loading = createLoading || editLoading;

  const handleCancel = () => {
    router.back();
  };

  const handleSave = async () => {
    try {
      if (isEditMode && collectionId) {
        // Edit existing collection
        const variables: EditCollectionMutationVariables = {
          input: {
            id: collectionId,
            title,
            description: description.trim() || null,
          },
        };

        await editCollection({ variables });
      } else {
        // Create new collection
        const variables: CreateCollectionMutationVariables = {
          input: {
            title,
            description: description.trim() || null,
          },
        };

        await createCollection({ variables });
      }
    } catch (error) {
      console.error(
        `Error ${isEditMode ? 'updating' : 'creating'} collection:`,
        error
      );
    }
  };

  const isSaveEnabled = title.trim().length > 0;

  return (
    <Screen>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
        >
          <View style={styles.header}>
            <Text variant='h2' align='center'>
              {isEditMode
                ? t('collections.editCollection')
                : t('collections.newCollection')}
            </Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Text
                variant='caption1'
                color={colors.darkGrey[800]}
                style={styles.label}
              >
                {t('collections.collectionTitle')}{' '}
                <Text color={colors.primary[700]}>*</Text>
              </Text>
              <View>
                <TextInput
                  style={[styles.input, titleFocused && styles.inputFocused]}
                  value={title}
                  onChangeText={setTitle}
                  placeholder={t('collections.collectionTitle')}
                  placeholderTextColor={colors.darkGrey[400]}
                  maxLength={MAX_TITLE_LENGTH}
                  onFocus={() => setTitleFocused(true)}
                  onBlur={() => setTitleFocused(false)}
                />
                <Text
                  variant='caption'
                  color={
                    title.length > MAX_TITLE_LENGTH * 0.8
                      ? colors.info.yellow
                      : colors.darkGrey[600]
                  }
                  style={styles.charCounter}
                >
                  {title.length}/{MAX_TITLE_LENGTH}
                </Text>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text
                variant='caption1'
                color={colors.darkGrey[800]}
                style={styles.label}
              >
                {t('collections.collectionDescription')}
              </Text>
              <View>
                <TextInput
                  style={[
                    styles.input,
                    styles.textArea,
                    descriptionFocused && styles.inputFocused,
                  ]}
                  value={description}
                  onChangeText={setDescription}
                  placeholder={t('collections.collectionDescription')}
                  placeholderTextColor={colors.darkGrey[400]}
                  multiline={true}
                  numberOfLines={4}
                  textAlignVertical='top'
                  maxLength={MAX_DESCRIPTION_LENGTH}
                  onFocus={() => setDescriptionFocused(true)}
                  onBlur={() => setDescriptionFocused(false)}
                />
                <Text
                  variant='caption'
                  color={
                    description.length > MAX_DESCRIPTION_LENGTH * 0.8
                      ? colors.info.yellow
                      : colors.darkGrey[600]
                  }
                  style={styles.charCounter}
                >
                  {description.length}/{MAX_DESCRIPTION_LENGTH}
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>

        <View style={styles.buttonContainer}>
          <Button
            title={loading ? t('common.loading') : t('common.save')}
            variant='primary'
            onPress={handleSave}
            disabled={!isSaveEnabled || loading}
            style={[styles.button]}
          />
          <Button
            title={t('common.cancel')}
            variant='secondary'
            onPress={handleCancel}
            style={styles.button}
          />
        </View>
      </KeyboardAvoidingView>
    </Screen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: moderateScale(16),
  },
  header: {
    alignItems: 'center',
    paddingVertical: moderateScale(16),
    marginBottom: moderateScale(24),
  },
  form: {
    gap: moderateScale(24),
  },
  inputContainer: {
    gap: moderateScale(8),
  },
  label: {
    marginBottom: moderateScale(4),
    marginLeft: moderateScale(4),
  },
  input: {
    backgroundColor: colors.softGrey[700],
    borderRadius: moderateScale(12),
    padding: moderateScale(14),
    fontSize: moderateScale(16),
    color: colors.darkGrey[800],
    borderWidth: 1,
    borderColor: 'transparent',
  },
  inputFocused: {
    borderColor: colors.primary[600],
    backgroundColor: colors.softGrey[600],
  },
  textArea: {
    height: moderateScale(120),
  },
  charCounter: {
    textAlign: 'right',
    marginTop: moderateScale(4),
    marginRight: moderateScale(4),
  },
  buttonContainer: {
    gap: verticalScale(12),
    padding: moderateScale(16),
    paddingBottom: verticalScale(60),
  },
  button: {
    width: '100%',
  },
});

// This tells expo-router to treat this screen as a modal
export const dynamic = 'force-dynamic';

export const unstable_settings = {
  presentation: 'modal',
};
