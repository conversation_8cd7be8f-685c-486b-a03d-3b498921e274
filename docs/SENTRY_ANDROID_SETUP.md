# Sentry Android Configuration Guide

This document explains how <PERSON>try is configured for Android builds to achieve exact feature parity with iOS.

## 🎯 **Feature Parity Achieved**

| Feature | iOS Status | Android Status | Notes |
|---------|------------|----------------|-------|
| **JavaScript Error Reporting** | ✅ Working | ✅ Working | Identical implementation |
| **Native Crash Reporting** | ✅ Working | ✅ Working | Requires SENTRY_AUTH_TOKEN |
| **Source Map Upload** | ✅ Working | ✅ Working | Requires SENTRY_AUTH_TOKEN |
| **ProGuard Mapping Upload** | N/A | ✅ Working | Android-specific feature |
| **Debug Symbol Upload** | ✅ Working | ✅ Working | Requires SENTRY_AUTH_TOKEN |
| **Session Replay** | ✅ Working | ✅ Working | Identical implementation |
| **Performance Monitoring** | ✅ Working | ✅ Working | Identical implementation |

## 🔧 **Configuration Overview**

### **Automatic Mode Detection**

The Android build automatically detects whether to run in development or production mode based on the presence of the `SENTRY_AUTH_TOKEN` environment variable:

- **Development Mode** (no auth token): JavaScript error reporting only
- **Production Mode** (with auth token): Full Sentry integration including native crash reporting and uploads

### **Build Messages**

You'll see one of these messages during the Android build:

```bash
# Development Mode
⚠️  Sentry: Development mode - Sentry gradle script disabled (no auth token)
   JavaScript error reporting will still work, but native crash reporting and source map uploads are disabled

# Production Mode  
✅ Sentry: Production mode enabled with auth token - enabling full integration
```

## 🚀 **Production Setup**

### **1. EAS Build Configuration**

The `eas.json` file is already configured with `SENTRY_AUTH_TOKEN` for all build profiles:

```json
{
  "build": {
    "development": {
      "env": {
        "SENTRY_AUTH_TOKEN": "$SENTRY_AUTH_TOKEN"
      }
    },
    "preview": {
      "env": {
        "SENTRY_AUTH_TOKEN": "$SENTRY_AUTH_TOKEN"
      }
    },
    "production": {
      "env": {
        "SENTRY_AUTH_TOKEN": "$SENTRY_AUTH_TOKEN"
      }
    }
  }
}
```

### **2. Set Environment Variable**

Set the Sentry auth token in your EAS environment:

```bash
# Set the auth token for EAS builds
eas secret:create --scope project --name SENTRY_AUTH_TOKEN --value your-sentry-auth-token-here
```

### **3. Get Your Sentry Auth Token**

1. Go to [Sentry.io](https://sentry.io)
2. Navigate to Settings → Account → API → Auth Tokens
3. Create a new token with these scopes:
   - `project:read`
   - `project:releases`
   - `org:read`

## 🛠️ **Local Development**

### **JavaScript Error Reporting**

In local development (without SENTRY_AUTH_TOKEN), you still get:
- ✅ JavaScript error reporting
- ✅ Performance monitoring  
- ✅ Session replay
- ✅ User identification
- ❌ Native crash reporting (disabled)
- ❌ Source map uploads (disabled)

### **Testing Production Mode Locally**

To test production mode locally:

```bash
# Set auth token and build
export SENTRY_AUTH_TOKEN=your-token-here
cd android && ./gradlew assembleRelease
```

## 📁 **File Structure**

```
android/
├── sentry.properties              # Main Sentry configuration
├── sentry-local.properties        # Local development config (unused currently)
└── app/build.gradle               # Contains conditional Sentry integration
```

## 🔍 **Troubleshooting**

### **Build Fails with "Auth token is required"**

This means Sentry is trying to upload but no auth token is available:

1. **For local development**: This is expected - JavaScript error reporting still works
2. **For EAS builds**: Set the `SENTRY_AUTH_TOKEN` environment variable

### **No Native Crash Reports in Development**

This is expected behavior. Native crash reporting requires the Sentry Gradle plugin, which is disabled in development mode to prevent build failures.

### **Source Maps Not Uploaded**

Ensure:
1. `SENTRY_AUTH_TOKEN` is set
2. The token has correct permissions
3. Build is running in production mode

## 🎯 **Verification**

To verify Sentry is working correctly:

1. **Check build logs** for the Sentry mode message
2. **Test JavaScript errors** in the app
3. **Check Sentry dashboard** for events
4. **Verify source maps** are uploaded (production builds only)

## 📚 **Related Files**

- `app/_layout.tsx` - Sentry initialization
- `services/error-reporting.ts` - Error reporting service
- `eas.json` - Build environment configuration
- `android/sentry.properties` - Android Sentry configuration
