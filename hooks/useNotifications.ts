import { useEffect, useState } from 'react';
import { Alert } from 'react-native';
import {
  requestNotificationPermission,
  onMessageReceived,
  onTokenRefresh,
} from '../services/notifications';
import { useAddFcmTokenMutation } from '@/graphql/generated/graphql';

export function useNotifications() {
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [addFCMToken] = useAddFcmTokenMutation();
  useEffect(() => {
    let unsubscribeMessages: (() => void) | undefined;
    let unsubscribeTokenRefresh: (() => void) | undefined;

    async function setupNotifications() {
      try {
        // Request permission and get token
        const token = await requestNotificationPermission();
        if (token) {
          setFcmToken(token);
          // console.log('FCM Token ready:', token);
          addFCMToken({ variables: { token } });
        }

        // Setup message handlers
        unsubscribeMessages = await onMessageReceived();

        // Setup token refresh handler
        unsubscribeTokenRefresh = onTokenRefresh((newToken: string) => {
          setFcmToken(newToken);
          // console.log('Token refreshed:', newToken);
        });
      } catch (error) {
        console.error('Error setting up notifications:', error);
        Alert.alert(
          'Notification Setup Failed',
          'Please make sure notifications are enabled in your device settings.'
        );
      }
    }

    setupNotifications();

    // Cleanup function
    return () => {
      if (unsubscribeMessages) {
        unsubscribeMessages();
      }
      if (unsubscribeTokenRefresh) {
        unsubscribeTokenRefresh();
      }
    };
  }, []); // Empty dependency array ensures this only runs once

  return { fcmToken };
}
