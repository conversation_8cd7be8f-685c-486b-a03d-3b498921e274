import { useState, useEffect, useRef } from 'react';
import { Alert } from 'react-native';
import { Modalize } from 'react-native-modalize';
import Constants from 'expo-constants';
import * as Updates from 'expo-updates';
import {
  setDevModeUrl,
  clearDevModeUrl,
  getStoredDevUrl,
} from '../apollo/client';

// Helper function to transform PR number to full URL
const transformPrNumberToUrl = (input: string): string | null => {
  // Check if it's just a number (PR number)
  if (/^\d+$/.test(input)) {
    return `https://pr-${input}.conari.co/graphql`;
  }
  return null;
};

// Helper function to validate URL format
const validateUrlFormat = (
  url: string
): { isValid: boolean; error?: string } => {
  // Check if it's a domain without protocol
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return {
      isValid: false,
      error: 'Please enter a complete URL starting with http:// or https://',
    };
  }
  return { isValid: true };
};

// Helper function to normalize URL (add /graphql if needed)
const normalizeUrl = (url: string): string => {
  // Add /graphql if not present and it's a base domain
  if (
    !url.includes('/graphql') &&
    (url.includes('conari.co') || url.includes('conari.net'))
  ) {
    return url.replace(/\/$/, '') + '/graphql';
  }
  return url;
};

// Helper function to process and validate input URL
const processInputUrl = (
  inputUrl: string
): { url?: string; error?: string } => {
  const trimmedInput = inputUrl.trim();

  // Try PR number transformation first
  const prUrl = transformPrNumberToUrl(trimmedInput);
  if (prUrl) {
    return { url: prUrl };
  }

  // Validate URL format
  const validation = validateUrlFormat(trimmedInput);
  if (!validation.isValid) {
    return { error: validation.error };
  }

  // Normalize the URL
  const normalizedUrl = normalizeUrl(trimmedInput);
  return { url: normalizedUrl };
};

export function useDevMode() {
  const [currentDevUrl, setCurrentDevUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const devModeModalRef = useRef<Modalize>(null);

  // Check if we're in staging environment
  const isStaging = Constants.expoConfig?.extra?.ENV === 'staging';

  // Load current dev URL on mount
  useEffect(() => {
    const loadDevUrl = async () => {
      try {
        setIsLoading(true);
        const devUrl = await getStoredDevUrl();
        setCurrentDevUrl(devUrl);
      } catch (error) {
        console.error('Failed to load dev URL:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadDevUrl();
  }, []);

  // Get display URL for modal
  const getDisplayUrl = () => {
    return currentDevUrl || Constants.expoConfig?.extra?.GRAPHQL_URL || '';
  };

  // Handle dev mode modal opening
  const handleDevModeClick = () => {
    devModeModalRef.current?.open();
  };

  // Handle setting new URL
  const handleDevModeSetUrl = async (inputUrl: string) => {
    // Process and validate the input URL
    const result = processInputUrl(inputUrl);

    if (result.error) {
      Alert.alert('Error', result.error);
      return;
    }

    const finalUrl = result.url!;

    try {
      await setDevModeUrl(finalUrl);
      setCurrentDevUrl(finalUrl);
      devModeModalRef.current?.close();

      Alert.alert(
        'URL Updated',
        `New URL set to: ${finalUrl}\n\nThe app will restart automatically to apply changes.`,
        [
          {
            text: 'Restart Now',
            onPress: async () => {
              try {
                await Updates.reloadAsync();
              } catch (error) {
                console.error('Failed to restart app:', error);
                Alert.alert(
                  'Error',
                  'Failed to restart app automatically. Please restart manually.'
                );
              }
            },
          },
          {
            text: 'Later',
            style: 'cancel',
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to save URL. Please try again.');
    }
  };

  // Handle resetting URL to default
  const handleDevModeResetUrl = async () => {
    try {
      await clearDevModeUrl();
      setCurrentDevUrl(null);
      devModeModalRef.current?.close();

      Alert.alert(
        'URL Reset',
        'URL reset to default. The app will restart automatically to apply changes.',
        [
          {
            text: 'Restart Now',
            onPress: async () => {
              try {
                await Updates.reloadAsync();
              } catch (error) {
                console.error('Failed to restart app:', error);
                Alert.alert(
                  'Error',
                  'Failed to restart app automatically. Please restart manually.'
                );
              }
            },
          },
          {
            text: 'Later',
            style: 'cancel',
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to reset URL. Please try again.');
    }
  };

  // Handle modal cancel
  const handleDevModeCancel = () => {
    devModeModalRef.current?.close();
  };

  return {
    // State
    currentDevUrl,
    isLoading,
    isStaging,

    // Modal ref
    devModeModalRef,

    // Handlers
    handleDevModeClick,
    handleDevModeSetUrl,
    handleDevModeResetUrl,
    handleDevModeCancel,

    // Utils
    getDisplayUrl,
  };
}
