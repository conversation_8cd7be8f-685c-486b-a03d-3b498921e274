import { useState, useEffect } from 'react';
import { checkForceUpdate } from '../services/remote-config';

export function useForceUpdate() {
  const [isUpdateRequired, setIsUpdateRequired] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkForUpdate();
  }, []);

  const checkForUpdate = async () => {
    try {
      setIsLoading(true);

      // Check if force update is required
      const updateRequired = await checkForceUpdate();
      setIsUpdateRequired(updateRequired);
    } catch (error) {
      console.error('[ForceUpdate] Error checking for update:', error);
      // On error, don't block the user
      setIsUpdateRequired(false);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isUpdateRequired,
    isLoading,
    checkForUpdate,
  };
}
