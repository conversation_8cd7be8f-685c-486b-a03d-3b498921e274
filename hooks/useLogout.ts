import { useCallback } from 'react';
import { useClearFcmTokenMutation } from '../graphql/generated/graphql';
import { useAuthStore } from '../store/auth';

export function useLogout() {
  const [clearFcmToken] = useClearFcmTokenMutation();
  const { logout } = useAuthStore();

  const handleLogout = useCallback(() => {
    logout(() => {
      clearFcmToken();
    });
  }, [clearFcmToken, logout]);

  return handleLogout;
}
