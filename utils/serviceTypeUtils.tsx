import React from 'react';
import { Tag } from '@/components/ui/Tag';
import { CurrencyTag } from '@/components/ui/CurrencyTag';
import { t } from 'i18next';

interface ServiceType {
  __typename?: string;
  title?: string | null;
}

interface RenderServiceTypeTagsProps {
  serviceTypes?: (ServiceType | null)[] | null;
  variant?: 'primary' | 'secondary' | 'transparent';
}

/**
 * Renders service type tags with smart logic:
 * - If both Dine-in and Takeaway are present, shows single "DineAnyway" tag
 * - Otherwise shows individual tags for Dine-in or Takeaway
 */
export const renderServiceTypeTags = ({
  serviceTypes,
  variant,
}: RenderServiceTypeTagsProps) => {
  if (!serviceTypes) return null;

  const hasDineIn = serviceTypes.some(
    (serviceType) => serviceType?.title === 'Dine-in'
  );
  const hasTakeaway = serviceTypes.some(
    (serviceType) => serviceType?.title === 'Takeaway'
  );

  // If both are present, show DineAnyway tag
  if (hasDineIn && hasTakeaway) {
    return (
      <Tag key='dine-anyway' icon='star' label='DineAnyway' variant={variant} />
    );
  }

  // Otherwise show individual tags
  return serviceTypes.map((serviceType, index) => {
    if (serviceType?.title === 'Dine-in' || serviceType?.title === 'Takeaway') {
      return (
        <Tag
          key={`${serviceType?.title}-${index}`}
          icon={
            serviceType?.title === 'Dine-in'
              ? 'fork.knife'
              : 'takeoutbag.and.cup.and.straw'
          }
          label={serviceType?.title}
          variant={variant}
        />
      );
    }
    return null;
  });
};

//render max_saving tag
export const renderMaxSavingTag = (
  maxSaving: number,
  variant?: 'primary' | 'transparent'
) => {
  if (!maxSaving) return null;
  return <CurrencyTag number={maxSaving} variant={variant} />;
};

// render reuse_limit_days tag
export const renderReuseLimitDaysTag = (
  reuseLimitDays: number,
  variant?: 'primary' | 'transparent'
) => {
  if (!reuseLimitDays) return null;
  return (
    <Tag
      icon='hourglass'
      label={`${reuseLimitDays} ${
        reuseLimitDays === 1 ? t('deals.deal.day') : t('deals.deal.days')
      }`}
      variant={variant}
    />
  );
};
