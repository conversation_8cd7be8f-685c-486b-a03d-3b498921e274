import { formatTime, DAYS, checkOpeningHours } from './time';
import { PartnerPlaceOpeningHours } from '../graphql/generated/graphql';

export interface OpeningStatus {
  status: 'open' | 'closed';
  nextOpen: PartnerPlaceOpeningHours | null;
}

/**
 * Gets the opening hours for today's day of the week
 * @param openingHours Array of opening hours objects from the API
 * @returns Opening hours object for today or null if closed
 */
export const getTodayOpeningHours = (
  openingHours: (PartnerPlaceOpeningHours | null)[] | null | undefined
): PartnerPlaceOpeningHours | null => {
  if (!openingHours?.length) return null;

  const today = new Date().getDay(); // 0 is Sunday, 1 is Monday, etc.
  const todayHours = openingHours.find((hour) => hour?.day === today);

  // Check if opening hours exist and are not empty strings
  // API format: from/to are datetime strings like "2025-06-01 09:00:00"
  if (
    !todayHours?.from ||
    !todayHours?.to ||
    todayHours.from.trim() === '' ||
    todayHours.to.trim() === ''
  ) {
    return null; // Place is closed today
  }

  return todayHours;
};

/**
 * Determines if the place is currently open and when it will next open
 * @param openingHours Array of opening hours objects from the API
 * @returns Object containing current status ('open' or 'closed') and next opening info
 */
export const getOpeningStatus = (
  openingHours: (PartnerPlaceOpeningHours | null)[] | null | undefined
): OpeningStatus => {
  const todayHours = getTodayOpeningHours(openingHours);
  const now = new Date();
  const currentDay = now.getDay();

  // Helper function to find the next day the place will be open
  // Searches up to 7 days ahead to find valid opening hours
  const findNextOpeningHours = (
    startDay: number
  ): PartnerPlaceOpeningHours | null => {
    for (let i = 1; i <= 7; i++) {
      const nextDay = (startDay + i) % 7;
      const nextDayHours = openingHours?.find((hour) => hour?.day === nextDay);
      // Check if the next day has valid opening hours (not empty or null)
      if (nextDayHours?.from && nextDayHours?.to) {
        return nextDayHours;
      }
    }
    return null; // No opening hours found in the next 7 days
  };

  // Case 1: Place is closed today (no opening hours for today)
  if (!todayHours?.from || !todayHours?.to) {
    const nextDayHours = findNextOpeningHours(currentDay);
    return { status: 'closed', nextOpen: nextDayHours };
  }

  // Case 2: Place has opening hours today - check if currently open
  // Using checkOpeningHours utility that handles datetime format
  const { isOpen, nextOpen } = checkOpeningHours(
    todayHours.from,
    todayHours.to
  );

  // Case 2a: Place is currently open
  if (isOpen) {
    return { status: 'open', nextOpen: null };
  }

  // Case 2b: Place is closed but will open again today (before opening time)
  if (nextOpen?.day === currentDay) {
    return { status: 'closed', nextOpen: todayHours };
  }

  // Case 2c: Place is closed for the day, find next opening day
  const nextDayHours = findNextOpeningHours(currentDay);
  return { status: 'closed', nextOpen: nextDayHours };
};

/**
 * Renders the main title for opening hours display
 * Shows either today's hours or "Closed Today" message
 * @param openingHours Array of opening hours objects from the API
 * @param translations Object containing translation functions
 * @returns Formatted time range string or closed message
 */
export const renderOpeningHoursTitle = (
  openingHours: (PartnerPlaceOpeningHours | null)[] | null | undefined,
  translations: {
    closed: string;
    closedToday: string;
  }
): string => {
  const { status, nextOpen } = getOpeningStatus(openingHours);

  // Case 1: Place is currently open OR will open again today (show today's hours)
  if (
    status === 'open' ||
    (status === 'closed' && nextOpen?.day === new Date().getDay())
  ) {
    const todayHours = getTodayOpeningHours(openingHours);
    return todayHours
      ? `${formatTime(todayHours.from || '')}-${formatTime(todayHours.to || '')}`
      : translations.closed;
  }

  // Case 2: Place is closed today (no opening hours for today)
  return translations.closedToday;
};

/**
 * Renders the subtitle for opening hours display
 * Shows current status and next opening information
 * @param openingHours Array of opening hours objects from the API
 * @param translations Object containing translation functions
 * @returns Status message indicating if open now, closed now, or when it opens next
 */
export const renderOpeningHoursSubtitle = (
  openingHours: (PartnerPlaceOpeningHours | null)[] | null | undefined,
  translations: {
    openNow: string;
    closedNow: string;
    opensNext: (params: { day: string; time: string }) => string;
    closed: string;
  }
): string => {
  const { status, nextOpen } = getOpeningStatus(openingHours);

  // Case 1: Place is currently open
  if (status === 'open') {
    return translations.openNow;
  }

  // Case 2: Place is closed - determine when it opens next
  if (status === 'closed' && nextOpen?.from) {
    // Case 2a: Place will open again today (currently before opening time)
    if (nextOpen.day === new Date().getDay()) {
      return translations.closedNow;
    }

    // Case 2b: Place will open on a different day
    if (
      typeof nextOpen.day === 'number' &&
      nextOpen.day >= 0 &&
      nextOpen.day < DAYS.length
    ) {
      return translations.opensNext({
        day: DAYS[nextOpen.day].slice(0, 3), // Show abbreviated day name (e.g., "Mon")
        time: formatTime(nextOpen.from), // Extract time from datetime format
      });
    }
  }

  // Case 3: Fallback for when no opening information is available
  return translations.closed;
};

/**
 * Formats opening hours for all days of the week
 * Converts raw opening hours data into display format for each day
 * @param openingHours Array of opening hours objects from the API
 * @returns Array of objects with day names and formatted time ranges
 */
export const formatOpeningHours = (
  openingHours: (PartnerPlaceOpeningHours | null)[] | null | undefined
): { day: string; hours: string }[] => {
  if (!openingHours?.length) return [];

  return DAYS.map((day, index) => {
    // Find opening hours for this specific day (0 = Sunday, 1 = Monday, etc.)
    const dayHours = openingHours.find((hour) => hour?.day === index);

    // Check if the place is open on this day
    // Both from and to must exist and not be empty strings
    // API returns datetime format: "2025-06-01 09:00:00"
    const isOpen =
      dayHours?.from &&
      dayHours.to &&
      dayHours.from.trim() !== '' &&
      dayHours.to.trim() !== '';

    return {
      day,
      hours: isOpen
        ? `${formatTime(dayHours.from || '')} - ${formatTime(dayHours.to || '')}` // Extract time from datetime format
        : 'Closed', // Show "Closed" when no opening hours exist
    };
  });
};
