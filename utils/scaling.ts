import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

// Using iPhone 14 dimensions as base
const guidelineBaseWidth = 375;
const guidelineBaseHeight = 812;

export const scale = (size: number) => (width / guidelineBaseWidth) * size;
export const verticalScale = (size: number) =>
  (height / guidelineBaseHeight) * size;
export const moderateScale = (size: number, factor = 0.5) =>
  size + (scale(size) - size) * factor;

//screen width
export const SCREEN_WIDTH = width;

//screen height
export const SCREEN_HEIGHT = height;
