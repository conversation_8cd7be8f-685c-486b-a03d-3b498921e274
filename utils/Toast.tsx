import { colors } from '@/constants/Colors';
import { StyleSheet, View } from 'react-native';
import Toast from 'react-native-root-toast';
import { PlatformIcon } from '@/components/ui/PlatformIcon';
import { Text } from '@/components/ui/Text';
import { moderateScale, verticalScale } from '@/utils/scaling';
import * as Haptics from 'expo-haptics';
interface ToastOptions {
  onHidden?: () => void;
  persistent?: boolean; // Add persistent option
}

const showToast = (
  variant: 'success' | 'error',
  message: string,
  options?: ToastOptions
) => {
  const icon =
    variant === 'success' ? 'checkmark.circle.fill' : 'xmark.circle.fill';
  const color = variant === 'success' ? colors.primary[950] : colors.info.red;

  if (variant === 'success') {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  } else {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
  }

  Toast.show(
    <View style={styles.toastContainer}>
      <PlatformIcon name={icon} size={24} color={color} />
      <Text variant='caption' style={styles.toastText}>
        {message}
      </Text>
    </View>,
    {
      duration: options?.persistent ? 0 : Toast.durations.SHORT, // Use persistent duration if requested
      shadow: true,
      animation: true,
      hideOnPress: true,
      backgroundColor: colors.base[100],
      opacity: 1,
      position: Toast.positions.TOP,
      containerStyle: styles.container,
      onHidden: options?.onHidden,
    }
  );
};

export const showSuccessToast = (message: string, options?: ToastOptions) => {
  showToast('success', message, options);
};

export const showErrorToast = (message: string, options?: ToastOptions) => {
  showToast('error', message, options);
};

const styles = StyleSheet.create({
  container: {
    borderRadius: moderateScale(32),
    paddingHorizontal: moderateScale(20),
    paddingVertical: moderateScale(8),
    marginTop: verticalScale(50),
  },
  toastContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
    overflow: 'hidden',
  },
  toastText: {
    width: '85%',
  },
});
