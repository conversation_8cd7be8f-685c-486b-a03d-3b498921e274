import { ar, enUS } from 'date-fns/locale';
import i18n from '@/i18n';
import { t } from 'i18next';
import {
  format,
  parse,
  differenceInMilliseconds,
  formatDistanceToNow,
  isPast,
} from 'date-fns';

/**
 * Formats a datetime string to 12-hour AM/PM format
 * @param datetime Datetime string in "yyyy-MM-dd HH:mm:ss" format
 * @returns Formatted time string (e.g., "9AM")
 */
export const formatTime = (datetime: string) => {
  if (!datetime) return '';

  try {
    const dateTime = parse(datetime, 'yyyy-MM-dd HH:mm:ss', new Date());
    const hour = dateTime.getHours();
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const formattedHour = hour % 12 || 12;
    return `${formattedHour}${ampm}`;
  } catch (error) {
    console.error('Error parsing datetime format:', error);
    return '';
  }
};

/**
 * Extracts time from datetime string and converts to minutes since midnight
 * @param datetime Datetime string in "yyyy-MM-dd HH:mm:ss" format
 * @returns Minutes since midnight
 */
export const timeToMinutes = (datetime: string): number => {
  if (!datetime) return 0;

  try {
    const dateTime = parse(datetime, 'yyyy-MM-dd HH:mm:ss', new Date());
    return dateTime.getHours() * 60 + dateTime.getMinutes();
  } catch (error) {
    console.error('Error parsing datetime format:', error);
    return 0;
  }
};

/**
 * Checks if current time is within opening hours
 * @param from Opening datetime in "yyyy-MM-dd HH:mm:ss" format
 * @param to Closing datetime in "yyyy-MM-dd HH:mm:ss" format
 * @param currentTime Optional current time in minutes (defaults to now)
 * @returns Object with isOpen status and next opening info
 */
export const checkOpeningHours = (
  from: string,
  to: string,
  currentTime?: number
): { isOpen: boolean; nextOpen: { day: number; time: string } | null } => {
  const now =
    currentTime ?? new Date().getHours() * 60 + new Date().getMinutes();
  const openingTime = timeToMinutes(from);
  const closingTime = timeToMinutes(to);

  // Handle cases where closing time is past midnight
  if (closingTime < openingTime) {
    if (now >= openingTime || now <= closingTime) {
      return { isOpen: true, nextOpen: null };
    }
    return {
      isOpen: false,
      nextOpen: { day: new Date().getDay(), time: from },
    };
  }

  // Normal case (closing time is on the same day)
  if (now >= openingTime && now <= closingTime) {
    return { isOpen: true, nextOpen: null };
  }
  if (now < openingTime) {
    return {
      isOpen: false,
      nextOpen: { day: new Date().getDay(), time: from },
    };
  }
  return {
    isOpen: false,
    nextOpen: { day: (new Date().getDay() + 1) % 7, time: from },
  };
};

/**
 * Formats a time range from HH:mm:ss format to "h:mm - h:mm AM/PM"
 * @param from Start time in "HH:mm:ss" format
 * @param to End time in "HH:mm:ss" format
 * @returns Formatted time range string (e.g., "10:00 - 11:00 AM")
 */
export const formatTimeRange = (from: string, to: string): string => {
  try {
    const fromDate = parse(from, 'yyyy-MM-dd HH:mm:ss', new Date());
    const toDate = parse(to, 'yyyy-MM-dd HH:mm:ss', new Date());
    return `${format(fromDate, 'h:mm')} ${format(fromDate, 'a')} - ${format(toDate, 'h:mm')} ${format(toDate, 'a')}`;
  } catch (error) {
    console.error('Error formatting time range:', error);
    return `${from} - ${to}`;
  }
};

/**
 * Calculates deal reuse progress percentage using date-fns built-in functions
 * @param reservedDate The date when the deal was reserved/redeemed
 * @param reuseDate The date when the deal can be reused again
 * @returns Object with progress percentage
 */
export const calculateDealProgress = (
  reservedDate: Date,
  reuseDate: Date
): {
  progressPercentage: number;
} => {
  const currentDate = new Date();

  // Use date-fns built-in functions for date comparisons
  const isAvailable = isPast(reuseDate);

  // Calculate progress using date-fns functions
  const totalWaitTime = differenceInMilliseconds(reuseDate, reservedDate);
  const elapsedTime = differenceInMilliseconds(currentDate, reservedDate);

  // Progress percentage with built-in bounds checking
  const progressPercentage = isAvailable
    ? 100
    : Math.min(100, Math.max(0, (elapsedTime / totalWaitTime) * 100));

  return {
    progressPercentage,
  };
};

/**
 * Formats the remaining time text for deal reuse using date-fns built-in natural language strings
 * @param reuseDate The date when the deal can be reused again
 * @returns Formatted time text using date-fns native string formatting
 */
export const formatDealRemainingTime = (reuseDate: Date): string => {
  // Use date-fns built-in function to check availability
  if (isPast(reuseDate)) {
    return t('deals.deal.reserveButton');
  }

  // Use date-fns built-in formatDistanceToNow for natural language
  return `${t('deals.deal.reuse')} ${formatDistanceToNow(reuseDate, {
    addSuffix: true,
    locale: i18n.language === 'ar' ? ar : enUS,
  })}`;
};

export const DAYS = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
];
