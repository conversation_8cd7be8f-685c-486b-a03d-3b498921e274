import { Alert } from 'react-native';
import { TFunction } from 'i18next';

/**
 * Shows a confirmation dialog for unfollowing a place
 *
 * @param t Translation function from useTranslation
 * @param placeName Name of the place to unfollow
 * @param onConfirm Function to execute when user confirms unfollowing
 */
export const showUnfollowConfirmation = (
  t: TFunction,
  placeName: string,
  onConfirm: () => void
) => {
  Alert.alert(
    t('place.unfollowConfirmation.title'),
    t('place.unfollowConfirmation.message', { placeName }),
    [
      { text: t('common.cancel'), style: 'cancel' },
      {
        text: t('place.unfollow'),
        style: 'destructive',
        onPress: onConfirm,
      },
    ]
  );
};
