import {
  SharedValue,
  withSequence,
  withSpring,
  withTiming,
  Easing,
} from 'react-native-reanimated';

/**
 * Animation utilities for the app
 * This file contains reusable animation functions to keep components clean
 */

/**
 * Animates a like action with scale and rotation
 * @param likeScale - Shared value for scale animation
 * @param likeRotate - Shared value for rotation animation
 * @param isLiked - Whether the item is being liked or unliked
 */
export const animateLike = (
  likeScale: SharedValue<number>,
  likeRotate: SharedValue<number>,
  isLiked: boolean
): void => {
  if (isLiked) {
    // Enhanced heart animation for liking
    likeScale.value = withSequence(
      withSpring(0.8, { damping: 15 }),
      withSpring(1.3, { damping: 12 }),
      withSpring(1, { damping: 10 })
    );

    // Add more rotation for a bouncy effect
    likeRotate.value = withSequence(
      withTiming(-15, {
        duration: 150,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      }),
      withTiming(15, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      }),
      withTiming(0, {
        duration: 150,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      })
    );
  } else {
    // Subtle animation for unliking
    likeScale.value = withSequence(
      withSpring(0.8, { damping: 15 }),
      withSpring(1, { damping: 10 })
    );
  }
};

/**
 * Animates a follow action with scale
 * @param followScale - Shared value for scale animation
 */
export const animateFollow = (followScale: SharedValue<number>): void => {
  followScale.value = withSequence(
    withSpring(0.8, { damping: 15 }),
    withSpring(1.2, { damping: 12 }),
    withSpring(1, { damping: 10 })
  );
};

/**
 * Animates search input expansion and element hiding
 * @param searchAnimation - Shared value for search input width animation
 * @param elementsOpacity - Shared value for elements opacity animation
 * @param isOpening - Whether the search is opening or closing
 */
export const animateSearch = (
  searchAnimation: SharedValue<number>,
  elementsOpacity: SharedValue<number>,
  isOpening: boolean
): void => {
  if (isOpening) {
    // Opening search
    searchAnimation.value = withSpring(1, {
      damping: 20,
      stiffness: 300,
    });
    elementsOpacity.value = withTiming(0, { duration: 200 });
  } else {
    // Closing search
    searchAnimation.value = withSpring(0, {
      damping: 20,
      stiffness: 300,
    });
    elementsOpacity.value = withTiming(1, { duration: 200 });
  }
};

/**
 * Animation configurations for common use cases
 */
export const AnimationPresets = {
  SPRING_CONFIG: {
    damping: 15,
    stiffness: 100,
  },
  BUTTON_PRESS: {
    scale: 0.95,
    duration: 100,
  },
  SEARCH_SPRING: {
    damping: 20,
    stiffness: 300,
  },
  SEARCH_TIMING: {
    duration: 200,
  },
};
