import { CodegenConfig } from '@graphql-codegen/cli';
import Constants from 'expo-constants';

const GRAPHQL_URL = Constants.expoConfig?.extra?.GRAPHQL_URL;

if (!GRAPHQL_URL) {
  throw new Error('GRAPHQL_URL is not defined in environment variables');
}

const config: CodegenConfig = {
  schema: GRAPHQL_URL,
  //  Path to your queries/mutations
  documents: './graphql/**/*.graphql',
  // Path to your generated types
  generates: {
    './graphql/generated/graphql.ts': {
      plugins: [
        'typescript',
        'typescript-operations',
        'typescript-react-apollo',
      ],
      config: {
        withHooks: true,
        withHOC: false,
        withComponent: false,
      },
    },
  },
  hooks: { afterAllFileWrite: ['prettier --write'] },
};

export default config;
