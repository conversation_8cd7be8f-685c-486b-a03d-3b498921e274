export default {
  login: {
    title: 'Welcome back (or aboard)!',
    subtitle: 'Enter your number to sign up or log in.',
    phoneLabel: 'Phone Number',
    termsText: 'I accept the',
    termsLink: 'terms and conditions',
    andText: 'and',
    privacyLink: 'privacy policy',
    continuePhone: 'Continue with Phone',
    continueApple: 'Continue with Apple',
    continueGoogle: 'Continue with Google',
    or: 'OR',
    termsError: {
      title: 'Terms & Conditions',
      message: 'Please accept the terms and conditions to continue',
    },
    appleError: {
      message: 'An error occurred during Apple Sign In, please try again later',
    },
    googleError: {
      message:
        'An error occurred during Google Sign In, please try again later: ',
    },
  },
  otp: {
    title: "What's the code?",
    subtitle: "We've sent a 6-digit code to",
    verify: 'Verify',
    resendText: "Didn't receive the code?",
    resendButton: 'Resend',
    resendTimer: 'Resend in {{seconds}}s',
  },
  validation: {
    phoneMin: 'Phone number must be at least 9 digits',
    phoneMax: 'Phone number must be less than 15 digits',
    acceptTerms: 'You must accept the terms and conditions',
    invalidPhone: {
      title: 'Invalid phone number',
      message: 'Please enter a valid phone number',
    },
    invalidCode: 'Please enter a valid code',
  },
  onboarding: {
    title: 'Swipe, Savor, Save.',
    subtitle:
      'Discover curated restaurants through short videos, not boring lists. Tap in for vibes, bites, and big savings.',
    getStarted: "Let's get started!",
    invitationText:
      'Conari is a members-only app. Access is by invitation only.',
  },
  pendingApproval: {
    title: 'Access pending',
    subtitle:
      "Conari is currently invite-only. If you've already been invited, you'll be notified once your access is approved!",
    description: 'We will notify you once your account has been approved.',
    enableNotifications: "Get Notified When You're In",
    notificationsEnabled: 'Notifications Enabled',
    notificationsDescription:
      "Know someone on Conari? Ask them for an invite—it's the fastest way in.",
  },
  terms: {
    title: 'Terms and Conditions',
    summary: 'Please read these terms carefully before using Conari App.',
  },
  privacy: {
    title: 'Privacy Policy',
    summary:
      'Your privacy is important to us. This policy explains how we collect, use, and protect your data.',
  },
  profile: {
    accountManagement: 'Account Management',
    profile: 'Profile',
    deleteAccount: 'Delete Account',
    deleteAccountConfirmation:
      'Are you sure you want to delete your account? This action cannot be undone.',
    logout: 'Logout',
    logoutConfirmation: 'Are you sure you want to logout?',
    termsAndConditions: 'Terms & Conditions',
    faq: 'FAQ',
    privacyPolicy: 'Privacy Policy',
    appVersion: 'App Version',
    v: 'V.',
    becomeCreator: 'Become a Creator',
    invite: {
      bannerText1:
        "You've got 5 invites to bring your friends onto Conari early.",
      bannerText2: 'Choose wisely!',
      bannerButton: 'Send an invite now!',
    },
  },
  common: {
    done: 'Done',
    ok: 'OK',
    cancel: 'Cancel',
    delete: 'Delete',
    loading: 'Loading...',
    error: 'Error',
    back: 'Back',
    notFound: 'Item Not Found',
    currency: 'AED',
    save: 'Save',
    km: 'km',
    webview: 'Web Page',
    no_url: 'No URL provided',
    edit: 'Edit',
    share: 'Share',
    today: 'Today',
    tomorrow: 'Tomorrow',
  },
  deals: {
    title: 'Bookings',
    filters: {
      redeemable: 'Active',
      upcoming: 'Upcoming',
      redeemed: 'Redeemed',
      no_show: 'Redeemed',
    },
    deal: {
      reserveButton: 'Reserve Deal',
      modifyButton: 'Modify',
      infoTooltip: {
        title: 'Deal Information',
        serviceType: 'Deals are for {{serviceType}} only as stated.',
        serviceTypeAll: 'Deals are applicable for both {{serviceType}}.',
        maxSaving:
          'This deal gives you a discount of up to {{maxSaving}} AED, depending on the items you choose.',
        reuseLimitDays:
          'After redeeming a deal, you must wait {{reuseLimitDays}} days before you can reserve the same deal again.',
      },
      reserveDate: {
        label: 'Reserve Date:',
      },
      reuse: 'Reserve again',
      days: 'Days',
      day: 'Day',
    },
    swipeButton: {
      swipeText: 'Swipe For Redeem',
      comingSoon: 'Not Redeemable Yet',
    },

    reserveSuccess: {
      title: "You're All Set!",
      subtitle:
        "You've successfully reserved your deal. Don't forget to show up during your selected time!",
      place: 'Place',
      location: 'Location',
      date: 'Date',
      time: 'Time',
      goToDeals: 'Go to My Deals',
    },
    dealScreen: {
      bookingTerms:
        'Check the Booking and Cancellation terms before confirming your reservation to ensure a smooth experience',
      date: 'Date',
      time: 'Time',
      slots: '{{count}} slots',
      bookButton: 'Book the Deal',
      termsButton: 'Terms and Conditions',
      termsContent: 'Terms and conditions for booking and cancellation...',
      bookingSuccess: 'Booking successful!',
      termsTitle: 'Terms & Conditions',
      terms: {
        reservation:
          'Reservation is for the Deal, Not a Table - Booking a time slot reserves the deal only, not a seat or table at the restaurant.',
        timeSlot:
          'Time-Slot Based Redemption - You can only redeem the deal within the selected time slot — not before and not after. Make sure to reserve your deal before arriving at the restaurant.',
        mandatory:
          "Reservation is Mandatory - Each restaurant has a limited number of deal slots per day. You must reserve the deal in the app before heading to the restaurant, even if it's last minute.",
        conditions:
          'Deal Conditions Must Be Met - The discount applies only when the specific deal conditions are fulfilled (e.g., purchasing a qualifying item).',
        combination:
          'No Combination with Other Offers - Deals cannot be combined with other promotions unless explicitly stated.',
      },
    },
    empty: {
      text: 'There are no deals',
      textStatus: 'There are no deals that can be',
      redeemable: {
        title: 'Nothing active yet.',
        message:
          "Your reserved deals live in the Upcoming tab — they'll pop up here when it's time to redeem.",
      },
      upcoming: {
        title: "You don't have any upcoming deals.",
        message: "Reserve a deal and it'll land here — waiting for its moment",
      },
      redeemed: {
        title: 'No redeemed deals yet.',
        message:
          "Once you use a deal, it'll show up here for your records — along with when you can reserve it again",
      },
    },
    cancelDealModal: {
      title: 'Are you sure you want to cancel this booking deal?',
      description: "Think twice! You're about to cancel this deal forever!",
      cancelDeal: 'Cancel Deal',
    },
  },
  place: {
    quickActions: {
      closed: 'Closed',
      closedToday: 'Closed Today',
      openNow: 'Open Now',
      closedNow: 'Closed Now',
      opensNext: 'Opens {{day}} {{time}}',
      menu: {
        title: 'Menu',
        subtitle: 'Explore',
        viewOnline: 'View',
      },
      map: {
        title: 'Map',
        subtitle: 'Navigate',
      },
    },
    sections: {
      bestDeals: 'Deals',
      reels: 'Reels',
    },
    tags: {
      google: '{{rating}} Google',
      distance: '{{distance}} km',
    },
    openingHours: {
      title: 'Opening Hours',
    },
    locations: {
      title: 'Locations',
    },
    follow: 'Follow',
    following: 'Following',
    followingPlaces: 'Following Places',
    unfollow: 'Unfollow',
    unfollowConfirmation: {
      title: 'Unfollow Place?',
      message: 'Are you sure you want to unfollow {{placeName}}?',
    },
  },
  filters: {
    title: 'Filters',
    clearAll: 'Clear All',
    showResults: 'Show Results',
    noResults: 'No results found',
    searchLocation: 'Search Location...',
    showMore: 'Show More',
    showLess: 'Show Less',
    groups: {
      sortBy: 'Sort By',
      rating: 'Rating',
      location: 'Location',
      dealType: 'Deal Type',
      serviceType: 'Service Type',
      specialties: 'Specialties',
      cuisineTypes: 'Cuisine Types',
      ambiance: 'Ambiance',
      dietary: 'Dietary',
      mealTimes: 'Meal Times',
      parking: 'Parking',
      priceRange: 'Price Range',
      retailDestination: 'Retail Destination',
      distance: 'Distance',
      cravings: 'Cravings',
      reviewsCount: 'Reviews Count',
    },
    sort: {
      rating: 'Rating',
      newest: 'Newest',
      recommended: 'Recommended',
      nearby: 'Nearby',
    },
  },
  redeem: {
    success: {
      title: "You're all set!",
      description:
        'Just show this screen to the restaurant staff to enjoy your deal.',
    },
    dealTitle: 'Deal',
    dateAndTime: 'Date & Time',
  },
  media: {
    loading: 'Loading image...',
    tapToView: 'Tap to view',
    tapToZoom: 'Tap to zoom',
    pdfViewer: {
      notAvailable: 'PDF viewer not available on this device.',
      openExternally: 'Open Externally',
    },
  },
  creator: {
    sections: {
      reels: 'Reels',
      collaborations: 'Collaborations',
      followers: 'Followers',
    },
  },
  collections: {
    title: 'Collections',
    newCollection: 'New Collection',
    editCollection: 'Edit Collection',
    collectionTitle: 'Name Your Collection',
    collectionDescription: 'Add a short description or theme',
    restaurant: 'place',
    restaurants: 'places',
    emptyState: 'Your Collection is looking empty',
    emptyStateSubtext:
      "Start curating your go-to spots, hidden gems, or places you're craving.",
    deleteCollection: 'Delete This Collection?',
    deleteCollectionConfirmation:
      "Think twice! You're about to remove this collection forever!",
    removePlaceFromAllCollections: {
      title: 'Remove This Place From All Collections?',
      description:
        'This spot will be removed from your collection. Are you sure?',
    },
    saved: 'Saved',
    allPlaces: 'All Places',
    startAdding: 'Start adding Places',
  },
  home: {
    noResults: 'No Reels found',
  },
  forceUpdate: {
    title: 'Time for an Update',
    subtitle:
      'Please update Conari to the latest version to keep discovering and saving your favorite spots!',
    updateButton: 'Update Now',
  },
  trial: {
    premiumAccess: 'Premium trial access',
    daysLeft: '{{days}} days left',
    infoMessage:
      'Your free trial ends on {{endDate}}. After this date, a subscription will be required to continue using Conari premium features.',
    infoMessageExpired:
      'Your free trial ended on {{endDate}}. A subscription is now required to continue using Conari premium features.',
    infoTitle: 'Trial Information',
    modal: {
      title: 'Premium feature unlocked',
      subtitle:
        "As an Insider, you've unlocked free access to all premium features, on us",
      trialDuration: '6-Month Trial',
      noPayment: 'No payment today, no card needed.',
      features: {
        deals: 'Reserve and redeem any deal',
        collections: 'Create and organize collections',
        cancel: 'Cancel anytime before trial ends',
      },
      startButton: 'Enjoy 6 months free',
      disclaimer:
        "You won't be charged unless you decide to stay and add your card after 6 months. No auto-renewals.",
      successToast: 'Premium trial activated! Enjoy 6 months of free access.',
    },
  },
  inviteModal: {
    title1: 'Five Invites.',
    title2: 'Make Them Count.',
    description:
      'Invite your inner circle to join you on Conari and get early access before anyone else!',
    inviteLeft: 'Invites Left:',
    invitesCount: '{{remaining}}/{{total}} Invites',
    shareButton: 'Share Your Invite',
    success: {
      title: 'Invite Sent',
      message: 'Your invite link has been shared successfully!',
    },
    error: {
      title: 'Error',
      message: 'There was an error sharing your invite. Please try again.',
    },
  },
  location: {
    permissionAlert: {
      title: 'Conari needs your location',
      message: 'Allow access to location services to find places near you.',
      cancel: 'Cancel',
      openSettings: 'Take me there',
    },
  },
  uploadReel: {
    title: 'Upload Reels',
    subtitle: 'Upload the video in standard Reels size (1080x1920)',
    browseText: 'Browse video to start uploading',
    captionPlaceholder: 'Add a caption',
    postButton: 'Post Reel',
    permissionAlert: {
      title: 'Permission Required',
      message:
        'We need access to your photo library to upload reels. Please enable it in your settings.',
      cancel: 'Cancel',
      openSettings: 'Open Settings',
    },
    progress: {
      selected: 'Selected',
      uploading: 'Uploading...',
      processing: 'Processing...',
      finalizing: 'Finalizing upload',
      timeRemaining: '{{seconds}} seconds remaining',
    },
    success: {
      title: 'Your content is being reviewed',
      subtitle:
        "We've received your content and it's under review, we will notify you as soon as it's approved",
    },
    error: {
      message: 'Failed to upload reel. Please try again.',
    },
  },
};
