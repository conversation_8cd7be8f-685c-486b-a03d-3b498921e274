import NetInfo from '@react-native-community/netinfo';
import { showErrorToast } from '@/utils/Toast';
import i18n from 'i18next';

// Network service state
let isConnected: boolean = true;
let isInitialized: boolean = false;

// Initialize the network service
export const initNetworkService = async (): Promise<void> => {
  if (isInitialized) return;

  try {
    // Check initial network state
    const networkState = await NetInfo.fetch();
    isConnected = networkState.isConnected ?? false;

    // Listen for network state changes
    NetInfo.addEventListener(handleNetworkStateChange);

    isInitialized = true;
    console.log('[NetworkService] Initialized - Connected:', isConnected);
  } catch (error) {
    console.log('[NetworkService] Error initializing:', error);
  }
};

// Handle network state changes
const handleNetworkStateChange = (networkState: any): void => {
  const wasConnected = isConnected;
  isConnected = networkState.isConnected ?? false;

  // Show toast every time connection is lost
  if (wasConnected && !isConnected) {
    showOfflineToast();
    console.log('[NetworkService] Connection lost - showing toast');
  }

  // Log when connection is restored
  if (!wasConnected && isConnected) {
    console.log('[NetworkService] Connection restored');
  }
};

// Show offline toast
const showOfflineToast = (): void => {
  const message = i18n.t('common.networkError');
  showErrorToast(message, { persistent: true });
};
