import messaging from '@react-native-firebase/messaging';
import { Platform } from 'react-native';

// Keep track of the message listener
let messageListener: (() => void) | null = null;

export async function requestNotificationPermission() {
  try {
    // Check if physical device
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('Authorization status:', authStatus);
        // Get the FCM token
        const fcmToken = await messaging().getToken();
        console.log('FCM Token:', fcmToken);
        return fcmToken;
      }
    } else {
      // On Android, permissions are granted by default
      const fcmToken = await messaging().getToken();
      console.log('FCM Token:', fcmToken);
      return fcmToken;
    }
  } catch (error) {
    console.error('Failed to get push token:', error);
    return null;
  }
}

export async function onMessageReceived() {
  // Only set up background handler once
  if (!messageListener) {
    // Handle background messages
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('Message handled in the background!', remoteMessage);
    });

    // Handle foreground messages
    messageListener = messaging().onMessage(async (remoteMessage) => {
      console.log('A new FCM message arrived!', remoteMessage);
    });
  }

  return () => {
    if (messageListener) {
      messageListener();
      messageListener = null;
    }
  };
}

// Subscribe to token refresh
export function onTokenRefresh(callback: (token: string) => void) {
  return messaging().onTokenRefresh((token) => {
    console.log('New FCM token:', token);
    callback(token);
  });
}
