import * as Device from 'expo-device';
import { Platform } from 'react-native';

/**
 * Device service that provides device detection utilities
 * Uses expo-device for accurate device information
 */
export const deviceService = {
  /**
   * Determines if the current device is considered "old" based on performance capabilities
   * Devices older than iPhone 11 and equivalent Android devices are considered old
   * @returns true if device is old, false otherwise
   */
  isOldDevice: (): boolean => {
    if (!Device.modelName) {
      // Fallback to platform version if device model is not available
      return deviceService.isOldDeviceByPlatformVersion();
    }

    if (Platform.OS === 'ios') {
      return deviceService.isOldIosDevice(Device.modelName);
    } else if (Platform.OS === 'android') {
      return deviceService.isOldAndroidDevice();
    }

    // Default to false for unknown platforms
    return false;
  },

  /**
   * Checks if iOS device is older than iPhone 11
   * iPhone 11 was released in 2019 and marked a significant performance improvement
   * @param modelName The device model name from expo-device
   * @returns true if device is older than iPhone 11
   */
  isOldIosDevice: (modelName: string): boolean => {
    // List of devices older than iPhone 11 (released September 2019)
    const oldIosDevices = [
      // iPhone models older than iPhone 11
      'iPhone 6',
      'iPhone 6 Plus',
      'iPhone 6s',
      'iPhone 6s Plus',
      'iPhone SE (1st generation)',
      'iPhone 7',
      'iPhone 7 Plus',
      'iPhone 8',
      'iPhone 8 Plus',
      'iPhone X',
      'iPhone XR',
      'iPhone XS',
      'iPhone XS Max',

      // iPad models with older processors (A10X and below)
      'iPad (5th generation)',
      'iPad (6th generation)',
      'iPad Air 2',
      'iPad Air (3rd generation)',
      'iPad mini 4',
      'iPad mini (5th generation)',
      'iPad Pro (9.7-inch)',
      'iPad Pro (10.5-inch)',
      'iPad Pro (12.9-inch) (1st generation)',
      'iPad Pro (12.9-inch) (2nd generation)',

      // iPod touch
      'iPod touch (6th generation)',
      'iPod touch (7th generation)',
    ];

    // Check if the current device model is in the old devices list
    return oldIosDevices.some((oldDevice) =>
      modelName.toLowerCase().includes(oldDevice.toLowerCase())
    );
  },

  /**
   * Checks if Android device is considered old based on API level and year
   * Devices with API level below 28 (Android 9) or released before 2019 are considered old
   * @returns true if Android device is old
   */
  isOldAndroidDevice: (): boolean => {
    // Check Android API level (Android 9 = API 28, released in 2018)
    // Devices below API 28 are considered old
    const apiLevel =
      typeof Platform.Version === 'number'
        ? Platform.Version
        : parseInt(Platform.Version, 10);
    if (apiLevel < 28) {
      return true;
    }

    // Additional check based on device year if available
    if (Device.deviceYearClass && Device.deviceYearClass < 2019) {
      return true;
    }

    return false;
  },

  /**
   * Fallback method using platform version when device model is unavailable
   * @returns true if device is old based on platform version
   */
  isOldDeviceByPlatformVersion: (): boolean => {
    if (Platform.OS === 'ios') {
      // iOS 13 and below are considered old (iPhone 11 shipped with iOS 13)
      const iosVersion = parseFloat(Platform.Version as string);
      return iosVersion < 14;
    } else if (Platform.OS === 'android') {
      // Android API level below 28 (Android 9) is considered old
      const apiLevel =
        typeof Platform.Version === 'number'
          ? Platform.Version
          : parseInt(Platform.Version, 10);
      return apiLevel < 28;
    }

    return false;
  },

  /**
   * Gets detailed device information for debugging
   * @returns object with device details
   */
  getDeviceInfo: () => {
    return {
      modelName: Device.modelName,
      deviceName: Device.deviceName,
      deviceType: Device.deviceType,
      osName: Device.osName,
      osVersion: Device.osVersion,
      platformApiLevel: Platform.Version,
      deviceYearClass: Device.deviceYearClass,
      isDevice: Device.isDevice,
      brand: Device.brand,
      manufacturer: Device.manufacturer,
    };
  },

  /**
   * Logs device information to console (useful for debugging)
   */
  logDeviceInfo: () => {
    const deviceInfo = deviceService.getDeviceInfo();
    console.log('Device Info:', deviceInfo);
    console.log('Is Old Device:', deviceService.isOldDevice());
  },
};
