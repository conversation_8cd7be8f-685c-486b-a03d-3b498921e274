import { segmentService } from './segment';
import { analyticsConfig } from '../config/analytics';
import { Creator, Reel } from '@/schemas/reel';

/**
 * Analytics service for tracking user interactions
 * This centralizes all analytics calls for easier management
 */
export const analyticsService = {
  /**
   * Track when a user clicks on a reel to view a place
   * @param reel - The full reel object
   * @param indexName - The current Algolia index name
   */
  trackPlaceClicked: (reel: any, indexName?: string) => {
    try {
      if (!analyticsConfig.isEnabled || !reel?.objectID) return;

      // Segment tracking
      segmentService.track('Place Clicked', {
        reelId: reel.objectID,
        queryId: (reel as any).__queryID || '',
        position: reel.__position || 1,
        placeName: reel.places?.[0]?.name,
        placeId: reel.places?.[0]?.id,
        creatorId: reel.creator?.id,
        partnerName: reel.partner?.name,
        indexName: indexName || '',
      });
    } catch (error) {
      console.error('Failed to track reel clicked:', error);
    }
  },

  /**
   * Track when a user views a reel
   * @param reel - The full reel object
   * @param indexName - The current Algolia index name
   */
  trackReelViewed: (reel: any, indexName?: string) => {
    try {
      if (!analyticsConfig.isEnabled || !reel?.objectID) return;

      // Segment tracking
      segmentService.track('Reel Viewed', {
        reelId: reel.objectID,
        queryId: (reel as any).__queryID || '',
        position: reel.__position || 1,
        placeName: reel.places?.[0]?.name,
        placeId: reel.places?.[0]?.id,
        creatorId: reel.creator?.id,
        partnerName: reel.partner?.name,
        duration: reel.duration,
        caption: reel.caption,
        indexName: indexName || '',
      });
    } catch (error) {
      console.error('Failed to track reel viewed:', error);
    }
  },

  /**
   * Track when a user shares a reel
   * @param reel - The full reel object
   * @param indexName - The current Algolia index name
   */
  trackReelShared: (reel: any, indexName?: string) => {
    try {
      if (!analyticsConfig.isEnabled || !reel?.objectID) return;

      // Segment tracking
      segmentService.track('Reel Shared', {
        reelId: reel.objectID,
        queryId: (reel as any).__queryID || '',
        position: reel.__position || 1,
        placeName: reel.places?.[0]?.name,
        placeId: reel.places?.[0]?.id,
        creatorId: reel.creator?.id,
        partnerName: reel.partner?.name,
        shareMethod: 'native_share',
        indexName: indexName || '',
      });
    } catch (error) {
      console.error('Failed to track reel share:', error);
    }
  },

  /**
   * Track when a user watches a reel for 10+ seconds
   * @param reel - The full reel object
   * @param indexName - The current Algolia index name
   */
  trackReelWatched: (reel: any, indexName?: string) => {
    try {
      if (!analyticsConfig.isEnabled || !reel?.objectID) return;

      // Segment tracking
      segmentService.track('Reel Watched', {
        reelId: reel.objectID,
        queryId: (reel as any).__queryID || '',
        position: reel.__position || 1,
        placeName: reel.places?.[0]?.name,
        placeId: reel.places?.[0]?.id,
        creatorId: reel.creator?.id,
        partnerName: reel.partner?.name,
        indexName: indexName || '',
      });
    } catch (error) {
      console.error('Failed to track reel watched:', error);
    }
  },

  /**
   * Track when a user clicks on a creator profile
   * @param reel - The reel object
   * @param indexName - The current Algolia index name
   */
  trackCreatorClicked: (reel: Reel, indexName?: string) => {
    try {
      if (!analyticsConfig.isEnabled || !reel?.creator?.id) return;

      // Segment tracking
      segmentService.track('Creator Clicked', {
        reelId: reel.objectID,
        creatorId: reel.creator.id,
        creatorName: reel.creator.name,
        queryId: (reel as any).__queryID || '',
        position: reel.__position || 1,
        indexName: indexName || '',
      });
    } catch (error) {
      console.error('Failed to track creator clicked:', error);
    }
  },

  /**
   * Track when a user likes or unlikes a reel
   * @param reel - The full reel object
   * @param isLiked - Whether the reel is being liked (true) or unliked (false)
   * @param indexName - The current Algolia index name
   */
  trackReelLiked: (reel: Reel, isLiked: boolean, indexName?: string) => {
    try {
      if (!analyticsConfig.isEnabled || !reel?.objectID) return;

      // Segment tracking
      segmentService.track(isLiked ? 'Reel Liked' : 'Reel Unliked', {
        reelId: reel.objectID,
        queryId: (reel as any).__queryID || '',
        position: reel.__position || 1,
        placeName: reel.places?.[0]?.name,
        placeId: reel.places?.[0]?.id,
        creatorId: reel.creator?.id,
        partnerName: reel.partner?.name,
        indexName: indexName || '',
      });
    } catch (error) {
      console.error('Failed to track reel liked/unliked:', error);
    }
  },

  /**
   * Track when a user bookmarks/saves a place
   * @param place - The place object
   * @param reel - The reel object
   * @param indexName - The current Algolia index name
   */
  trackBookmarkClicked: (place: any, reel: Reel, indexName?: string) => {
    try {
      if (!analyticsConfig.isEnabled || !place?.id) return;

      // Segment tracking
      segmentService.track('Place Bookmark Clicked', {
        reelId: reel.objectID,
        placeId: place.id,
        placeName: place.name,
        partnerName: place.partner?.name,
        queryId: (reel as any).__queryID || '',
        position: reel.__position || 1,
        indexName: indexName || '',
      });
    } catch (error) {
      console.error('Failed to track place bookmarked:', error);
    }
  },

  /**
   * Track screen views
   * @param screenName - The name of the screen
   * @param properties - Optional screen properties
   */
  trackScreen: (screenName: string, properties?: Record<string, any>) => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.screen(screenName, {
        ...properties,
      });
    } catch (error) {
      console.error('Failed to track screen:', error);
    }
  },

  /**
   * Track user login
   * @param method - Login method (email, apple, etc.)
   */
  trackUserLogin: (method: string) => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.track('User Logged In', {
        method,
      });
    } catch (error) {
      console.error('Failed to track user login:', error);
    }
  },

  /**
   * Track user logout
   */
  trackUserLogout: () => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.track('User Logged Out');
    } catch (error) {
      console.error('Failed to track user logout:', error);
    }
  },

  /**
   * Identify a user with Segment
   * @param userId - The user ID
   * @param traits - User traits
   */
  identifyUser: (userId: string, traits?: Record<string, any>) => {
    try {
      segmentService.identify(userId, traits);
    } catch (error) {
      console.error('Failed to identify user:', error);
    }
  },

  /**
   * Reset user data (on logout)
   */
  resetUser: () => {
    try {
      segmentService.reset();
    } catch (error) {
      console.error('Failed to reset user:', error);
    }
  },

  /**
   * Generic track method that automatically includes user email and ID
   * @param eventName - The name of the event
   * @param properties - Event properties
   */
  track: (eventName: string, properties?: Record<string, any>) => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.track(eventName, {
        ...properties,
      });
    } catch (error) {
      console.error(`Failed to track event "${eventName}":`, error);
    }
  },
};
