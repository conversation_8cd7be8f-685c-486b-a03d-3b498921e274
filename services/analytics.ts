import { aa } from '../components/home/<USER>';
import { segmentService } from './segment';

/**
 * Analytics service for tracking user interactions
 * This centralizes all analytics calls for easier management
 */
export const analyticsService = {
  /**
   * Track when a user clicks on a reel to view a place
   * @param reel - The full reel object
   * @param indexName - The current Algolia index name
   */
  trackReelClicked: (reel: any, indexName: string) => {
    try {
      if (__DEV__ || !reel?.objectID) return;

      // Algolia tracking
      aa('clickedObjectIDsAfterSearch', {
        index: 'reels',
        eventName: 'Reel Clicked',
        objectIDs: [reel.objectID],
        queryID: reel.__queryID || '',
        positions: [reel.__position || 1],
      });

      // Segment tracking
      segmentService.track('Reel Clicked', {
        reelId: reel.objectID,
        queryId: reel.__queryID || '',
        position: reel.__position || 1,
        placeName: reel.places?.[0]?.name,
        placeId: reel.places?.[0]?.id,
        creatorId: reel.creator?.id,
        partnerName: reel.partner?.name,
        indexName: indexName,
      });
    } catch (error) {
      console.error('Failed to track reel clicked:', error);
    }
  },

  /**
   * Track when a user views a reel
   * @param reel - The full reel object
   * @param indexName - The current Algolia index name
   */
  trackReelViewed: (reel: any, indexName: string) => {
    try {
      if (__DEV__ || !reel?.objectID) return;

      // Algolia tracking
      aa('viewedObjectIDs', {
        index: 'reels',
        eventName: 'Reel Viewed',
        objectIDs: [reel.objectID],
        queryID: reel.__queryID || '',
      } as any);

      // Segment tracking
      segmentService.track('Reel Viewed', {
        reelId: reel.objectID,
        queryId: reel.__queryID || '',
        placeName: reel.places?.[0]?.name,
        placeId: reel.places?.[0]?.id,
        creatorId: reel.creator?.id,
        partnerName: reel.partner?.name,
        duration: reel.duration,
        caption: reel.caption,
        indexName: indexName,
      });
    } catch (error) {
      console.error('Failed to track reel viewed:', error);
    }
  },

  /**
   * Track when a user shares a reel
   * @param reel - The full reel object
   * @param indexName - The current Algolia index name
   */
  trackReelShared: (reel: any, indexName: string) => {
    try {
      if (__DEV__ || !reel?.objectID) return;

      // Algolia tracking
      aa('clickedObjectIDs', {
        index: 'reels',
        eventName: 'Reel Shared',
        objectIDs: [reel.objectID],
        queryID: reel.__queryID || '',
      } as any);

      // Segment tracking
      segmentService.track('Reel Shared', {
        reelId: reel.objectID,
        queryId: reel.__queryID || '',
        placeName: reel.places?.[0]?.name,
        placeId: reel.places?.[0]?.id,
        creatorId: reel.creator?.id,
        partnerName: reel.partner?.name,
        shareMethod: 'native_share',
        indexName: indexName,
      });
    } catch (error) {
      console.error('Failed to track reel share:', error);
    }
  },

  /**
   * Track screen views
   * @param screenName - The name of the screen
   * @param properties - Optional screen properties
   */
  trackScreen: (screenName: string, properties?: Record<string, any>) => {
    try {
      segmentService.screen(screenName, {
        ...properties,
      });
    } catch (error) {
      console.error('Failed to track screen:', error);
    }
  },

  /**
   * Track user login
   * @param method - Login method (email, apple, etc.)
   */
  trackUserLogin: (method: string) => {
    try {
      segmentService.track('User Logged In', {
        method,
      });
    } catch (error) {
      console.error('Failed to track user login:', error);
    }
  },

  /**
   * Track user logout
   */
  trackUserLogout: () => {
    try {
      segmentService.track('User Logged Out');
    } catch (error) {
      console.error('Failed to track user logout:', error);
    }
  },

  /**
   * Identify a user with Segment
   * @param userId - The user ID
   * @param traits - User traits
   */
  identifyUser: (userId: string, traits?: Record<string, any>) => {
    try {
      segmentService.identify(userId, traits);
    } catch (error) {
      console.error('Failed to identify user:', error);
    }
  },

  /**
   * Reset user data (on logout)
   */
  resetUser: () => {
    try {
      segmentService.reset();
    } catch (error) {
      console.error('Failed to reset user:', error);
    }
  },

  /**
   * Generic track method that automatically includes user email and ID
   * @param eventName - The name of the event
   * @param properties - Event properties
   */
  track: (eventName: string, properties?: Record<string, any>) => {
    try {
      segmentService.track(eventName, {
        ...properties,
      });
    } catch (error) {
      console.error(`Failed to track event "${eventName}":`, error);
    }
  },
};
