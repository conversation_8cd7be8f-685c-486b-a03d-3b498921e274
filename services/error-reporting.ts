import * as Sentry from '@sentry/react-native';

/**
 * Error reporting service that centralizes error reporting functionality
 * using Sentry
 */
export const errorReportingService = {
  /**
   * Log an error when an invalid reel is encountered
   * @param reelId - The ID of the reel
   * @param validationError - The validation error
   */
  reportInvalidReel: (reelId: string | undefined, validationError: unknown) => {
    try {
      console.warn('Invalid reel data:', validationError);

      // Report to Sentry
      Sentry.addBreadcrumb({
        message: 'Invalid reel encountered',
        level: 'warning',
      });

      Sentry.withScope((scope) => {
        scope.setTag('reelId', reelId || 'unknown');
        scope.setContext('validationError', {
          error: JSON.stringify(validationError) || 'unknown error',
        });
        Sentry.captureException(
          new Error(`Invalid reel data: ${JSON.stringify(validationError)}`)
        );
      });
    } catch (error) {
      // Fail silently - we don't want error reporting to throw errors
      console.warn('Failed to report invalid reel to Sentry:', error);
    }
  },

  /**
   * Log a general error
   * @param message - Error message
   * @param error - The error object
   * @param attributes - Additional attributes to log with the error
   */
  reportError: (
    message: string,
    error: unknown,
    attributes?: Record<string, string>
  ) => {
    try {
      console.warn(message, error);

      // Report to Sentry
      Sentry.addBreadcrumb({
        message: message,
        level: 'error',
      });

      Sentry.withScope((scope) => {
        // Add any additional attributes as tags
        if (attributes) {
          Object.entries(attributes).forEach(([key, value]) => {
            scope.setTag(key, value);
          });
        }

        if (error instanceof Error) {
          Sentry.captureException(error);
        } else {
          Sentry.captureException(
            new Error(`${message}: ${JSON.stringify(error)}`)
          );
        }
      });
    } catch (reportingError) {
      console.warn('Failed to report error to Sentry:', reportingError);
    }
  },
};
