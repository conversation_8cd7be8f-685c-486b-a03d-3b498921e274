import remoteConfig from '@react-native-firebase/remote-config';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Module-level state
let isInitialized = false;

// Default minimum version
const UPDATE_CONFIG = Platform.select({
  ios: {
    min_required_version: '1.1.0',
  },
  android: {
    min_required_version: '1.1.0',
  },
});

// 1 hour in production, immediate in dev
const MINIMUM_FETCH_INTERVAL_MILLIS = 3600000;

/**
 * Initialize Remote Config with default values
 */
async function initializeRemoteConfig(): Promise<void> {
  if (isInitialized) {
    return;
  }

  try {
    // Set default values
    await remoteConfig().setDefaults({
      min_required_version: UPDATE_CONFIG?.min_required_version || '',
    });

    // Set config settings
    await remoteConfig().setConfigSettings({
      minimumFetchIntervalMillis: __DEV__ ? 0 : MINIMUM_FETCH_INTERVAL_MILLIS,
    });

    isInitialized = true;
  } catch (error) {
    console.error('[RemoteConfig] Failed to initialize:', error);
    throw error;
  }
}

/**
 * Fetch and activate the latest remote config
 */
async function fetchAndActivateRemoteConfig(): Promise<boolean> {
  try {
    await initializeRemoteConfig();

    // Fetch latest values
    const fetchedRemotely = await remoteConfig().fetchAndActivate();

    return fetchedRemotely;
  } catch (error) {
    console.error('[RemoteConfig] Failed to fetch and activate:', error);
    return false;
  }
}

/**
 * Get the minimum required version from Remote Config
 */
function getMinRequiredVersion(): string {
  try {
    const minVersion = remoteConfig()
      .getValue('min_required_version')
      .asString();
    return minVersion;
  } catch (error) {
    console.error('[RemoteConfig] Failed to get min required version:', error);
    return UPDATE_CONFIG?.min_required_version || ''; // Fallback to default
  }
}

/**
 * Compare two version strings
 * Returns: -1 if version1 < version2, 0 if equal, 1 if version1 > version2
 */
function compareVersions(version1: string, version2: string): number {
  const v1parts = version1.split('.').map(Number);
  const v2parts = version2.split('.').map(Number);

  const maxLength = Math.max(v1parts.length, v2parts.length);

  for (let i = 0; i < maxLength; i++) {
    const v1part = v1parts[i] || 0;
    const v2part = v2parts[i] || 0;

    if (v1part < v2part) return -1;
    if (v1part > v2part) return 1;
  }

  return 0;
}

/**
 * Check if the current app version meets the minimum required version
 */
function isUpdateRequired(): boolean {
  try {
    const currentVersion =
      Constants.expoConfig?.version ||
      UPDATE_CONFIG?.min_required_version ||
      '';
    const minRequiredVersion = getMinRequiredVersion();

    return compareVersions(currentVersion, minRequiredVersion) < 0;
  } catch (error) {
    console.error(
      '[RemoteConfig] Failed to check if update is required:',
      error
    );
    return false; // Default to no update required on error
  }
}

/**
 * Main function to check for force updates
 * This is the primary public API for the force update feature
 */
export async function checkForceUpdate(): Promise<boolean> {
  try {
    await initializeRemoteConfig();
    await fetchAndActivateRemoteConfig();
    return isUpdateRequired();
  } catch (error) {
    console.error('[RemoteConfig] Error during force update check:', error);
    return false;
  }
}
