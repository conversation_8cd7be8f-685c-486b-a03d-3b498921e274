import branch from 'react-native-branch';
import { Share } from 'react-native';
import { Router } from 'expo-router';

import Constants from 'expo-constants';
import {
  PartnerPlace,
  Creator,
  CollectionFragment,
} from '@/graphql/generated/graphql';
import { Reel } from '@/schemas/reel';
import { useAuthStore } from '@/store/auth';

const BRANCH_URL = Constants.expoConfig?.extra?.BRANCH_URL;
// Last received Branch parameters
let lastBranchParams: any = null;

// Reference to the router that will be set by the caller
let routerRef: Router | null = null;

/**
 * Set the router reference for navigation
 */
export const setBranchRouter = (router: Router) => {
  routerRef = router;

  // Process any pending params if we have them
  if (lastBranchParams) {
    handleBranchParams(lastBranchParams);
    lastBranchParams = null;
  }
};

/**
 * Handle Branch parameters by extracting IDs and navigating
 */
const handleBranchParams = (params: any) => {
  if (!routerRef) {
    console.log('[Branch] Router not ready yet, storing params for later');
    lastBranchParams = params;
    return;
  }

  // Extract relevant data for navigation
  const customParams = params['~customMetadata'] || {};

  // Look for IDs in both places (root params take precedence)
  const reelId = params.reelId || (customParams as any).reelId;
  const placeId = params.placeId || (customParams as any).placeId;
  const creatorId = params.creatorId || (customParams as any).creatorId;
  const collectionId =
    params.collectionId || (customParams as any).collectionId;
  const inviteToken = params.inviteToken || (customParams as any).inviteToken;

  console.log('[Branch] Extracted IDs:', {
    reelId,
    placeId,
    creatorId,
    collectionId,
    inviteToken,
  });

  // Navigate based on content type
  try {
    if (reelId) {
      console.log('[Branch] Navigating to reel:', reelId);
      routerRef.replace(`/reel/${reelId}`);
    } else if (placeId) {
      console.log('[Branch] Navigating to place:', placeId);
      routerRef.replace(`/places/${placeId}`);
    } else if (creatorId) {
      console.log('[Branch] Navigating to creator:', creatorId);
      routerRef.replace(`/creators/${creatorId}`);
    } else if (collectionId) {
      console.log('[Branch] Navigating to collection:', collectionId);
      routerRef.replace(`/collections/${collectionId}`);
    } else if (inviteToken) {
      // Store the invite token for the auth system to handle
      useAuthStore.getState().setPendingInviteToken(inviteToken);
      // Navigate to the main app where the auth system can process the invite
      routerRef.replace('/' as any);
    }
  } catch (error) {
    console.error('[Branch] Navigation error:', error);
  }
};

/**
 * Initialize Branch.io with deep link handling
 */
export const initializeBranch = async () => {
  try {
    branch.subscribe({
      onOpenStart: ({ uri, cachedInitialEvent }) => {
        console.log('[Branch] onOpenStart:', uri);
      },
      onOpenComplete: ({ error, params, uri }) => {
        if (error) {
          console.error('[Branch] Error:', error);
          return;
        }

        if (params) {
          console.log('[Branch] Received params', params);

          // Store the latest params
          lastBranchParams = params;

          // Check if this is a Branch link
          if (params['+clicked_branch_link']) {
            console.log('[Branch] Branch link clicked');
            handleBranchParams(params);
          }
        }
      },
    });

    console.log('[Branch] Initialized successfully');
  } catch (error) {
    console.error('[Branch] Initialization error:', error);
  }
};

/**
 * Process Branch parameters manually
 */
export const processBranchParams = () => {
  // If we already have params, process them
  if (lastBranchParams && lastBranchParams['+clicked_branch_link']) {
    handleBranchParams(lastBranchParams);
    return;
  }

  // Otherwise try to get latest params
  branch
    .getLatestReferringParams()
    .then((params) => {
      if (
        params &&
        Object.keys(params).length > 0 &&
        params['+clicked_branch_link']
      ) {
        handleBranchParams(params);
      }
    })
    .catch((err) => {
      console.error('[Branch] Error getting latest params:', err);
    });
};

/**
 * Creates a Branch Universal Object for sharing
 * @param id Unique identifier for the content
 * @param title Title for the shared link
 * @param description Description for the shared link
 * @param metadata Additional metadata for the link
 * @returns Branch Universal Object
 */
export const createBranchObject = async (
  id: string,
  title: string,
  description: string,
  metadata: Record<string, any> = {}
) => {
  return await branch.createBranchUniversalObject(id, {
    title,
    contentDescription: description,
    contentMetadata: {
      customMetadata: metadata,
    },
  });
};

/**
 * Shares content using Branch.io
 * @param objectId Unique identifier for the content (e.g., 'reel_123', 'place_456')
 * @param title Title for the shared link
 * @param description Description for the shared link
 * @param shareMessage optional Message to show in the share dialog
 * @param path Path for the deep link (e.g., '/reel/123', '/places/456') - optional for invites
 * @param metadata Additional metadata for the link
 */

export const shareWithBranch = async (
  objectId: string,
  title: string,
  description: string,
  path?: string,
  metadata: Record<string, any> = {},
  shareMessage?: string
) => {
  try {
    // Create Branch Universal Object
    const branchUniversalObject = await createBranchObject(
      objectId,
      title,
      description,
      metadata
    );

    // Create link properties
    const linkProperties = {
      feature: 'sharing',
      channel: 'app_share',
      campaign: 'content_share',
    };

    // Create link control params - only set URLs if path is provided
    const controlParams: any = {};

    if (path) {
      controlParams.$canonical_url = `${BRANCH_URL}${path}`;
      controlParams.$desktop_url = `${BRANCH_URL}${path}`;
      controlParams.$ios_url = `${BRANCH_URL}${path}`;
      controlParams.$android_url = `${BRANCH_URL}${path}`;
    }

    // Generate link
    const { url } = await branchUniversalObject.generateShortUrl(
      linkProperties,
      controlParams
    );

    // Share the link
    await Share.share({
      message: shareMessage || '',
      url: url,
    });

    return true;
  } catch (error) {
    console.error('[Branch] Error sharing:', error);

    // Fallback to regular sharing - only if path is provided
    if (path) {
      try {
        await Share.share({
          message: shareMessage || '',
          url: `${BRANCH_URL}${path}`,
        });
        return true;
      } catch (shareError) {
        console.error('[Branch] Error with fallback sharing:', shareError);
        return false;
      }
    } else {
      // For invites without path, just share the title/description
      try {
        await Share.share({
          message: shareMessage || title,
        });
        return true;
      } catch (shareError) {
        console.error('[Branch] Error with fallback sharing:', shareError);
        return false;
      }
    }
  }
};

/**
 * Share a reel using Branch.io
 */
export const shareReel = async (reel: Reel) => {
  const title = `Conari - ${reel.partner?.name}`;
  const description = reel?.caption ?? '';

  const path = `/reel/${reel.objectID}`;

  return shareWithBranch(`reel_${reel.objectID}`, title, description, path, {
    reelId: reel.objectID,
  });
};

/**
 * Share a place using Branch.io
 */
export const sharePlace = async (place: PartnerPlace) => {
  const title = `Conari - ${place?.partner?.name}`;
  const description = place?.name ?? '';
  const path = `/places/${place?.id}`;

  return shareWithBranch(`place_${place?.id}`, title, description, path, {
    placeId: place?.id,
  });
};

/**
 * Share a creator using Branch.io
 */
export const shareCreator = async (creator: Creator) => {
  const title = `Conari - ${creator?.user?.name || creator?.name}`;
  const description = creator?.bio ?? '';
  const path = `/creators/${creator?.id}`;

  return shareWithBranch(`creator_${creator?.id}`, title, description, path, {
    creatorId: creator?.id,
  });
};

/**
 * Share a collection using Branch.io
 */
export const shareCollection = async (collection: CollectionFragment) => {
  const title = `Conari - ${collection?.title}`;
  const description = collection?.description ?? '';
  const path = `/collections/${collection?.id}`;

  return shareWithBranch(
    `collection_${collection?.id}`,
    title,
    description,
    path,
    {
      collectionId: collection?.id,
    }
  );
};

/**
 * get the invite link
 */
export const getInviteLink = async (token: string) => {
  const title = `Conari - Invite`;
  const description = `Join me on Conari!`;

  return shareWithBranch(`invite`, title, description, undefined, {
    inviteToken: token,
  });
};

/**
 * Get pending invite token from auth store
 */
export const getPendingInviteToken = (): string | null => {
  return useAuthStore.getState().getPendingInviteToken();
};

/**
 * Clear pending invite token from auth store
 */
export const clearPendingInviteToken = (): void => {
  useAuthStore.getState().clearPendingInviteToken();
};
