import { segmentService } from './segment';
import { analyticsConfig } from '../config/analytics';

export interface AlgoliaData {
  index?: string;
  queryId?: string;
  position?: number;
  reelId?: string;
}

/**
 * Place-specific analytics service for tracking place interactions
 * This centralizes all place-related analytics calls for easier management
 */
export const placeAnalyticsService = {
  /**
   * Track when a user shares a place
   * @param place - The place object
   * @param algoliaData - Optional Algolia-related data (index, queryId, position, reelId)
   */
  trackPlaceShared: (place: any, algoliaData?: AlgoliaData) => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.track('Place Shared', {
        placeId: place.id,
        placeName: place.name,
        partnerName: place.partner?.name,
        partnerId: place.partner?.id,
        ...(algoliaData && {
          indexName: algoliaData.index,
          queryId: algoliaData.queryId,
          position: algoliaData.position,
          reelId: algoliaData.reelId,
        }),
      });
    } catch (error) {
      console.error('Failed to track place shared:', error);
    }
  },

  /**
   * Track when a user clicks on place working hours
   * @param place - The place object
   * @param algoliaData - Optional Algolia-related data (index, queryId, position, reelId)
   */
  trackPlaceWorkingHoursClicked: (place: any, algoliaData?: AlgoliaData) => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.track('Place Working Hours Clicked', {
        placeId: place.id,
        placeName: place.name,
        partnerName: place.partner?.name,
        partnerId: place.partner?.id,
        ...(algoliaData && {
          indexName: algoliaData.index,
          queryId: algoliaData.queryId,
          position: algoliaData.position,
          reelId: algoliaData.reelId,
        }),
      });
    } catch (error) {
      console.error('Failed to track place working hours clicked:', error);
    }
  },

  /**
   * Track when a user clicks on place menu
   * @param place - The place object
   * @param menuType - Type of menu (image/pdf/url)
   * @param menuItemsCount - Number of menu items
   * @param algoliaData - Optional Algolia-related data (index, queryId, position, reelId)
   */
  trackPlaceMenuClicked: (
    place: any,
    menuType?: string,
    menuItemsCount?: number,
    algoliaData?: AlgoliaData
  ) => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.track('Place Menu Clicked', {
        placeId: place.id,
        placeName: place.name,
        partnerName: place.partner?.name,
        partnerId: place.partner?.id,
        menuType: menuType || (place.menu_url ? 'url' : 'media'),
        menuItemsCount: menuItemsCount || place.menu?.length || 0,
        hasMenuUrl: !!place.menu_url,
        ...(algoliaData && {
          indexName: algoliaData.index,
          queryId: algoliaData.queryId,
          position: algoliaData.position,
          reelId: algoliaData.reelId,
        }),
      });
    } catch (error) {
      console.error('Failed to track place menu clicked:', error);
    }
  },

  /**
   * Track when a user clicks on place map/location
   * @param place - The place object
   * @param algoliaData - Optional Algolia-related data (index, queryId, position, reelId)
   */
  trackPlaceMapClicked: (place: any, algoliaData?: AlgoliaData) => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.track('Place Map Clicked', {
        placeId: place.id,
        placeName: place.name,
        partnerName: place.partner?.name,
        partnerId: place.partner?.id,
        latitude: place.location?.lat,
        longitude: place.location?.lng,
        ...(algoliaData && {
          indexName: algoliaData.index,
          queryId: algoliaData.queryId,
          position: algoliaData.position,
          reelId: algoliaData.reelId,
        }),
      });
    } catch (error) {
      console.error('Failed to track place map clicked:', error);
    }
  },

  /**
   * Track when a user clicks on place branches
   * @param place - The place object
   * @param branchesCount - Number of branches available
   * @param algoliaData - Optional Algolia-related data (index, queryId, position, reelId)
   */
  trackPlaceBranchesClicked: (
    place: any,
    branchesCount?: number,
    algoliaData?: AlgoliaData
  ) => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.track('Place Branches Clicked', {
        placeId: place.id,
        placeName: place.name,
        partnerName: place.partner?.name,
        partnerId: place.partner?.id,
        branchesCount:
          branchesCount || place.partner?.partner_places?.data?.length || 0,
        ...(algoliaData && {
          indexName: algoliaData.index,
          queryId: algoliaData.queryId,
          position: algoliaData.position,
          reelId: algoliaData.reelId,
        }),
      });
    } catch (error) {
      console.error('Failed to track place branches clicked:', error);
    }
  },

  /**
   * Track when a user clicks on a place image
   * @param place - The place object
   * @param position - Position/index of the image clicked
   * @param totalImages - Total number of images available
   * @param algoliaData - Optional Algolia-related data (index, queryId, position, reelId)
   */
  trackPlaceImageClicked: (
    place: any,
    position: number,
    totalImages?: number,
    algoliaData?: AlgoliaData
  ) => {
    try {
      if (!analyticsConfig.isEnabled) return;

      segmentService.track('Place Image Clicked', {
        placeId: place.id,
        placeName: place.name,
        partnerName: place.partner?.name,
        partnerId: place.partner?.id,
        imagePosition: position,
        totalImages: totalImages || place.images?.length || 0,
        ...(algoliaData && {
          indexName: algoliaData.index,
          queryId: algoliaData.queryId,
          position: algoliaData.position,
          reelId: algoliaData.reelId,
        }),
      });
    } catch (error) {
      console.error('Failed to track place image clicked:', error);
    }
  },

  /**
   * Track when a user follows or unfollows a place
   * @param place - The place object
   * @param isFollowed - Whether the place is being followed (true) or unfollowed (false)
   * @param algoliaData - Optional Algolia-related data (index, queryId, position, reelId)
   */
  trackPlaceFollowed: (
    place: any,
    isFollowed: boolean,
    algoliaData?: AlgoliaData
  ) => {
    try {
      if (!analyticsConfig.isEnabled || !place?.id) return;

      let trackingData: any = {
        placeId: place.id,
        placeName: place.name,
        partnerName: place.partner?.name,
        partnerId: place.partner?.id,
      };

      // Add algolia data if provided
      if (algoliaData) {
        trackingData = {
          ...trackingData,
          reelId: algoliaData.reelId || '',
          queryId: algoliaData.queryId || '',
          position: algoliaData.position || 1,
          indexName: algoliaData.index || '',
        };
      }

      // Segment tracking
      segmentService.track(
        isFollowed ? 'Place Followed' : 'Place Unfollowed',
        trackingData
      );
    } catch (error) {
      console.error('Failed to track place followed/unfollowed:', error);
    }
  },
};
