import { useState, useEffect, useCallback } from 'react';
import * as Location from 'expo-location';
import { Alert, Linking } from 'react-native';
import { useTranslation } from 'react-i18next';
import { getDistance } from 'geolib';

export type LocationPermissionStatus = 'granted' | 'denied' | 'pending';

export interface LocationServices {
  requestLocation: () => Promise<boolean>;
  getCurrentLocation: () => string | undefined;
  shouldDisableGeoFeatures: () => boolean;
  calculateDistanceToPlace: (
    placeLat: number,
    placeLng: number
  ) => number | null;
  locationPermissionStatus: LocationPermissionStatus;
  isRequestingLocation: boolean;
  userLocation: string | null;
}

// Module-level state (singleton pattern)
let userLocation: string | null = null;
let locationPermissionStatus: LocationPermissionStatus = 'pending';
let isRequestingLocation: boolean = false;
let hasInitialized: boolean = false;
const listeners: Set<() => void> = new Set();

// Helper functions
const addListener = (callback: () => void) => {
  listeners.add(callback);
};

const removeListener = (callback: () => void) => {
  listeners.delete(callback);
};

const notifyListeners = () => {
  listeners.forEach((callback) => callback());
};

const getState = () => ({
  userLocation,
  locationPermissionStatus,
  isRequestingLocation,
});

// Internal utility function to format coordinates
const formatCoordinates = (
  coords: string
): { latitude: number; longitude: number } | null => {
  try {
    const [lat, lng] = coords.split(', ').map(Number);
    return { latitude: lat, longitude: lng };
  } catch {
    return null;
  }
};

// Initialize location services
const initialize = async () => {
  if (hasInitialized) return;

  hasInitialized = true;

  try {
    // Check existing permission status first
    const { status } = await Location.getForegroundPermissionsAsync();
    if (status === 'granted') {
      // If already granted, get location immediately
      locationPermissionStatus = 'granted';
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });
      const locationString = `${location.coords.latitude}, ${location.coords.longitude}`;
      userLocation = locationString;
      notifyListeners();
    } else if (status === 'denied') {
      locationPermissionStatus = 'denied';
      notifyListeners();
    }
    // If 'undetermined', leave as 'pending' and let user interactions trigger the request
  } catch (error) {
    console.log('Failed to initialize location:', error);
  }
};

// Request location permission
const requestLocationPermission = async (
  showAlert: () => void
): Promise<boolean> => {
  if (isRequestingLocation) {
    return false;
  }

  isRequestingLocation = true;
  notifyListeners();

  try {
    // Check current permission status
    const { status: existingStatus } =
      await Location.getForegroundPermissionsAsync();

    if (existingStatus !== 'granted') {
      // Request permission
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        locationPermissionStatus = 'denied';
        notifyListeners();
        showAlert();
        return false;
      }
    }

    // Permission granted, get location
    locationPermissionStatus = 'granted';

    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced,
    });

    const locationString = `${location.coords.latitude}, ${location.coords.longitude}`;
    userLocation = locationString;
    notifyListeners();
    return true;
  } catch (error) {
    console.error('Error getting location:', error);
    locationPermissionStatus = 'denied';
    notifyListeners();
    showAlert();
    return false;
  } finally {
    isRequestingLocation = false;
    notifyListeners();
  }
};

// Main request location function
const requestLocation = async (showAlert: () => void): Promise<boolean> => {
  if (locationPermissionStatus === 'granted' && userLocation) {
    console.log('📍 Location already available, returning true');
    return true;
  }

  // Request location permission and get location
  return await requestLocationPermission(showAlert);
};

// Get current location
const getCurrentLocation = (): string | undefined => {
  return locationPermissionStatus === 'granted' && userLocation
    ? userLocation
    : undefined;
};

// Check if geo features should be disabled
const shouldDisableGeoFeatures = (): boolean => {
  return locationPermissionStatus === 'denied';
};

// Calculate distance to place
const calculateDistanceToPlace = (
  placeLat: number,
  placeLng: number
): number | null => {
  // Only calculate if permission is granted and we have user location
  if (locationPermissionStatus !== 'granted' || !userLocation) {
    return null;
  }

  try {
    const coordinates = formatCoordinates(userLocation);
    if (!coordinates) {
      return null;
    }

    const distanceInMeters = getDistance(
      { latitude: coordinates.latitude, longitude: coordinates.longitude },
      { latitude: placeLat, longitude: placeLng }
    );

    // Convert from meters to kilometers and round to 1 decimal place
    return Math.round((distanceInMeters / 1000) * 10) / 10;
  } catch (error) {
    console.error('Error calculating distance:', error);
    return null;
  }
};

// Hook to use location services
export function useLocationServices(): LocationServices {
  const { t } = useTranslation();
  const [, forceUpdate] = useState({});

  // Force re-render when location state changes
  useEffect(() => {
    const callback = () => forceUpdate({});
    addListener(callback);

    // Initialize on first use
    initialize();

    return () => removeListener(callback);
  }, []);

  const showLocationDeniedAlert = useCallback(() => {
    Alert.alert(
      t('location.permissionAlert.title'),
      t('location.permissionAlert.message'),
      [
        {
          text: t('location.permissionAlert.cancel'),
          style: 'cancel',
        },
        {
          text: t('location.permissionAlert.openSettings'),
          onPress: () => {
            Linking.openSettings();
          },
        },
      ]
    );
  }, [t]);

  const state = getState();

  return {
    requestLocation: () => requestLocation(showLocationDeniedAlert),
    getCurrentLocation,
    shouldDisableGeoFeatures,
    calculateDistanceToPlace,
    locationPermissionStatus: state.locationPermissionStatus,
    isRequestingLocation: state.isRequestingLocation,
    userLocation: state.userLocation,
  };
}
