import { createClient } from '@segment/analytics-react-native';
import Constants from 'expo-constants';

const { SEGMENT_WRITE_KEY, JITSU_DOMAIN } = Constants.expoConfig?.extra as {
  SEGMENT_WRITE_KEY?: string;
  JITSU_DOMAIN?: string;
};

// Validate Segment configuration
if (!SEGMENT_WRITE_KEY || !JITSU_DOMAIN) {
  console.warn(
    'SEGMENT_WRITE_KEY or JITSU_DOMAIN is not configured. Segment tracking will be disabled.'
  );
}

// Create Segment client
export const segmentClient = SEGMENT_WRITE_KEY
  ? createClient({
      writeKey: SEGMENT_WRITE_KEY,
      proxy: `${JITSU_DOMAIN}/v1/batch`,
      cdnProxy: `${JITSU_DOMAIN}/v1/projects`,
      // Optional: Configure additional settings
      trackAppLifecycleEvents: true,
      trackDeepLinks: true, // Enable deep link tracking
      flushAt: 1, // Send events immediately in development
      debug: __DEV__, // Enable debug logs in development
      collectDeviceId: true, // Collect device ID
    })
  : null;

/**
 * Segment analytics service wrapper
 * Provides a consistent interface for tracking events
 */
export const segmentService = {
  /**
   * Initialize Segment analytics
   */
  initialize: () => {
    if (!segmentClient) {
      console.warn('Segment client not available - skipping initialization');
      return;
    }

    console.log('Segment initialized successfully');
  },

  /**
   * Identify a user
   * @param userId - The user ID
   * @param traits - Optional user traits
   */
  identify: (userId: string, traits?: Record<string, any>) => {
    if (!segmentClient) return;

    try {
      segmentClient.identify(userId, traits);
    } catch (error) {
      console.error('Segment identify error:', error);
    }
  },

  /**
   * Track an event
   * @param event - The event name
   * @param properties - Optional event properties
   */
  track: (event: string, properties?: Record<string, any>) => {
    if (!segmentClient) return;

    try {
      segmentClient.track(event, properties);
    } catch (error) {
      console.error('Segment track error:', error);
    }
  },

  /**
   * Track a screen view
   * @param name - The screen name
   * @param properties - Optional screen properties
   */
  screen: (name: string, properties?: Record<string, any>) => {
    if (!segmentClient) return;

    try {
      segmentClient.screen(name, properties);
    } catch (error) {
      console.error('Segment screen error:', error);
    }
  },

  /**
   * Reset the user (logout)
   */
  reset: () => {
    if (!segmentClient) return;

    try {
      segmentClient.reset();
    } catch (error) {
      console.error('Segment reset error:', error);
    }
  },
};
