import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import Constants from 'expo-constants';
import { useAuthStore } from '@/store/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

if (!Constants.expoConfig?.extra?.GRAPHQL_URL) {
  throw new Error('GRAPHQL_URL is not defined in environment variables');
}

// Store the current GraphQL URL
let currentGraphQLUrl = Constants.expoConfig.extra.GRAPHQL_URL;

// Function to get the GraphQL URL (either from dev mode or default)
const getGraphQLUrl = async (): Promise<string> => {
  try {
    const devUrl = await AsyncStorage.getItem('DEV_GRAPHQL_URL');
    if (devUrl) {
      return devUrl;
    }
  } catch (error) {
    console.warn('Failed to get dev URL from storage:', error);
  }
  return Constants.expoConfig?.extra?.GRAPHQL_URL || '';
};

// Initialize the current URL
const initializeUrl = async () => {
  currentGraphQLUrl = await getGraphQLUrl();
};

// Call initialization
initializeUrl();

const httpLink = createHttpLink({
  uri: () => currentGraphQLUrl,
});

// Create a conditional auth link that only adds the token for operations that need it
const authLink = setContext((operation, { headers }) => {
  // Define a list of operations that require authentication
  const authRequiredOperations = [
    'Me',
    'myDeals',
    'CancelDeal',
    'RedeemDeal',
    'ReserveDeal',
    'Place',
    'UserFollowing',
    'FollowPlace',
    'ReelStatus',
    'Like',
    'FollowCreator',
    'Creator',
    'Collections',
    'Collection',
    'CreateCollection',
    'EditCollection',
    'DeleteCollection',
    'AddItemToCollection',
    'RemoveItemFromCollection',
    'RemoveItemFromAllCollections',
    'InviteUser',
    'AcceptInvitation',
    'GetReelUploadUrl',
    'CreateReel',
    'AddFCMToken',
    'ClearFCMToken',
    'SubmitReview',
    'UpdateReview',
    'Reviews',
  ];

  // Check if the current operation requires authentication
  const operationName = operation.operationName || '';
  const requiresAuth = operationName
    ? authRequiredOperations.includes(operationName)
    : false;

  // Only get the token if this operation requires auth
  let token = null;
  if (requiresAuth) {
    if (__DEV__) {
      // In development mode, first check auth store (for dev login simulation)
      // then fall back to environment token
      const authStoreToken = useAuthStore.getState().token;
      token = authStoreToken || Constants.expoConfig?.extra?.TOKEN;
    } else {
      // In production, always use auth store token
      token = useAuthStore.getState().token;
    }
  }

  // Add the Authorization header only if the token exists and the operation requires auth
  return {
    headers: {
      ...headers,
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  };
});

export const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
});

// Helper functions for dev mode URL management
export const setDevModeUrl = async (url: string) => {
  try {
    await AsyncStorage.setItem('DEV_GRAPHQL_URL', url);
    // Update the current URL
    currentGraphQLUrl = url;
  } catch (error) {
    console.error('Failed to save dev URL:', error);
    throw error;
  }
};

export const clearDevModeUrl = async () => {
  try {
    await AsyncStorage.removeItem('DEV_GRAPHQL_URL');
    // Reset to default URL
    currentGraphQLUrl = Constants.expoConfig?.extra?.GRAPHQL_URL || '';
  } catch (error) {
    console.error('Failed to clear dev URL:', error);
    throw error;
  }
};

export const getStoredDevUrl = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem('DEV_GRAPHQL_URL');
  } catch (error) {
    console.error('Failed to get dev URL:', error);
    return null;
  }
};
