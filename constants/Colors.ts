/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

export const colors = {
  // ---------------------------------------------
  // PRIMARY
  // ---------------------------------------------

  primary: {
    100: '#F0EFFE',
    200: '#E2E0FC',
    300: '#D4D0FB',
    400: '#C6C1FA',
    500: '#B8B2F9',
    600: '#AAA3F7',
    700: '#9C94F6',
    800: '#8E85F5',
    900: '#8177F4',
    950: '#7569F3',
  },

  // ---------------------------------------------
  // SECONDARY
  // ---------------------------------------------

  secondary: {
    100: '#fdf2fa',
    200: '#fbe8f7',
    300: '#fad0f1',
    400: '#FA7BDA',
    500: '#f071d0',
    600: '#d62c9e',
    700: '#ba1c82',
    800: '#991b6a',
    900: '#801b5b',
    950: '#4e0934',
  },

  // ---------------------------------------------
  // ACCENT
  // ---------------------------------------------

  yellow: {
    100: '#FFFBE6',
    200: '#FFF7CC',
    300: '#FFF2B3',
    400: '#FFEE99',
    500: '#FFEA80',
    600: '#FFE666',
    700: '#FFE14D',
    800: '#FFDD33',
    900: '#FCC13E',
    950: '#F1A804', // (From the PDF's gradient note; might be used as well)
  },
  green: {
    100: '#F2FBF9',
    200: '#D3F4ED',
    300: '#A6E9DB',
    400: '#4FC6B6',
    500: '#45BCAC',
    600: '#2CA093',
    // TODO: Add the correct color
    700: '#B9FF99',
    800: '#208177',
    900: '#1B4642',
    950: '#0a2928',
  },

  // ---------------------------------------------
  // BASE
  // ---------------------------------------------

  darkGrey: {
    100: '#E7E7E9',
    200: '#CFCFD2',
    300: '#B8B8BC',
    400: '#A1A1A5',
    500: '#8A8A8F',
    600: '#737378',
    700: '#5D5D62',
    800: '#47474B',
    900: '#323235',
    950: '#1C1C1E',
  },
  softGrey: {
    100: '#FEFDFE',
    200: '#FCFCFD',
    300: '#FBFAFC',
    400: '#F9F9FB',
    500: '#F8F7FA',
    600: '#F6F6F9',
    700: '#F5F4F8',
    800: '#F4F3F7',
    900: '#F2F1F6',
    950: '#F2F1F6', // (Same as 900, per PDF)
  },
  base: {
    100: '#FFFFFF',
    950: '#131314',
  },

  // ---------------------------------------------
  // INFORMATION
  // ---------------------------------------------
  info: {
    green: '#56F859', // e.g., success color
    yellow: '#FFB03A', // e.g., warning color
    red: '#F7354C', // e.g., error color
  },
  // ---------------------------------------------
  // black transparent
  // ---------------------------------------------
  blackTransparent: {
    black01: 'rgba(0, 0, 0, 0.1)',
    black02: 'rgba(0, 0, 0, 0.2)',
    black03: 'rgba(0, 0, 0, 0.3)',
    black04: 'rgba(0, 0, 0, 0.4)',
    black05: 'rgba(0, 0, 0, 0.5)',
    black06: 'rgba(0, 0, 0, 0.6)',
    black07: 'rgba(0, 0, 0, 0.7)',
    black08: 'rgba(0, 0, 0, 0.8)',
    black09: 'rgba(0, 0, 0, 0.9)',
  },
};

/**
 * Theme-dependent colors
 */
export const ThemeColors = {
  light: {
    text: colors.darkGrey[100],
    background: colors.base[950],
    tint: colors.primary[950],
    icon: colors.darkGrey[200],
    tabIconDefault: colors.darkGrey[100],
    tabIconSelected: colors.base[100],
  },
  dark: {
    text: colors.darkGrey[100],
    background: colors.base[950],
    tint: colors.primary[950],
    icon: colors.darkGrey[200],
    tabIconDefault: colors.darkGrey[100],
    tabIconSelected: colors.base[100],
  },
};
