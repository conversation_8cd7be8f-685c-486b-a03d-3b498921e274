// Food & Cravings Emojis
export const CRAVING_EMOJIS: Record<string, string> = {
  Burger: '🍔',
  Pasta: '🍝',
  Pizza: '🍕',
  Cake: '🎂',
  Steak: '🥩',
  Seafood: '🦞',
  Grills: '🔥',
  Tiramisu: '🍰',
  'Fried Chicken': '🍗',
  Pancake: '🥞',
  Tacos: '🌮',
};

// Cuisine Type Emojis
export const CUISINE_EMOJIS: Record<string, string> = {
  Italian: '🇮🇹',
  Japanese: '🇯🇵',
  Chinese: '🇨🇳',
  Mexican: '🇲🇽',
  French: '🇫🇷',
  Indian: '🇮🇳',
  Thai: '🇹🇭',
  Korean: '🇰🇷',
  Greek: '🇬🇷',
  Spanish: '🇪🇸',
  American: '🇺🇸',
  Turkish: '🇹🇷',
  Lebanese: '🇱🇧',
  Vietnamese: '🇻🇳',
  Brazilian: '🇧🇷',
  Egyptian: '🇪🇬',
  Persian: '🇮🇷',
  Pakistani: '🇵🇰',
  Mediterranean: '🇮🇹',
  Argentinian: '🇦🇷',
  Asian: '🇯🇵',
  Georgian: '🇬🇪',
};

// Dietary Emojis
export const DIETARY_EMOJIS: Record<string, string> = {
  Vegetarian: '🥬',
  Vegan: '🌱',
  'Gluten-Free': '🌾',
  Halal: '☪️',
  Kosher: '✡️',
  Keto: '🥑',
  'Dairy-Free': '🥛',
  'Nut-Free': '🥜',
  Healthy: '🥗',
};

// Ambiance Emojis
export const AMBIANCE_EMOJIS: Record<string, string> = {
  Romantic: '💕',
  'Family-Friendly': '👨‍👩‍👧‍👦',
  Casual: '😌',
  'Fine Dining': '🍾',
  Outdoor: '🌳',
  Rooftop: '🏙️',
  Cozy: '🕯️',
  Lively: '🎉',
  Quiet: '🤫',
  Modern: '✨',
  Traditional: '🏛️',
  'Good for groups': '👥',
  'Good for kids': '🧒',
  'Live Music': '🎵',
  'Pets friendly': '🐕',
  'Laptop-Friendly': '💻',
  'Sports Viewing': '🏟️',
  Beachfront: '🏖️',
};

// Meal Times Emojis
export const MEAL_TIME_EMOJIS: Record<string, string> = {
  Breakfast: '🌅',
  Brunch: '🥐',
  Lunch: '☀️',
  'Business Lunch': '💼',
  Dinner: '🌙',
  'Late Night': '🌃',
  'All Day Breakfast': '🥞',
};

// Specialities Emojis
export const SPECIALITY_EMOJIS: Record<string, string> = {
  Delivery: '🚚',
  Takeaway: '🥡',
  'Dine-in': '🍽️',
  Buffet: '🍴',
  'Drive-through': '🚗',
  Catering: '🎪',
  'Casual Dining': '🍽️',
  Bakery: '🥖',
  'Fast Food': '🍟',
  'Fine Dining': '🍾',
  'Dessert Shop': '🧁',
  'Board games': '🎲',
  'Food Truck': '🚚',
  'Hotel Restaurant': '🏨',
};

// Helper function to get emoji for any food-related item
export const getEmojiForItem = (item: string, category?: string): string => {
  // Try to find emoji in specific category first
  switch (category) {
    case 'cravings':
      return CRAVING_EMOJIS[item] || '🍽️';
    case 'cuisine_types':
      return CUISINE_EMOJIS[item] || '🍽️';
    case 'dietary':
      return DIETARY_EMOJIS[item] || '🥗';
    case 'ambiance':
      return AMBIANCE_EMOJIS[item] || '🏪';
    case 'meal_times':
      return MEAL_TIME_EMOJIS[item] || '⏰';
    case 'specialities':
      return SPECIALITY_EMOJIS[item] || '🍽️';
    case 'area':
      return '📍';
    case 'retail_destination':
      return '🛍️';
    default:
      // Try all categories
      return (
        CRAVING_EMOJIS[item] ||
        CUISINE_EMOJIS[item] ||
        DIETARY_EMOJIS[item] ||
        AMBIANCE_EMOJIS[item] ||
        MEAL_TIME_EMOJIS[item] ||
        SPECIALITY_EMOJIS[item] ||
        '🍽️'
      );
  }
};
