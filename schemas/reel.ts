import { z } from 'zod';
import { DealType } from '../graphql/generated/graphql';

// Deal schema matching GraphQL type
export const dealSchema = z.object({
  id: z.string(),
  title: z.string(),
  deal_type: z.nativeEnum(DealType),
});

// rates schema {google: 4.5, self: 4.0}
const ratesSchema = z.object({
  google: z.number().min(0).max(5),
  reviews_count: z.number().optional(),
});

// placesSchema schema array of objects should be required
const placesSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    deals: z
      .array(dealSchema)
      .min(2, 'Deals array must contain at least 2 deals'),
    rates: ratesSchema,
    city: z.string().optional(),
    avatar: z.string().url().optional(),
    ambiance: z.array(z.string()).optional(),
    parking: z.array(z.string()).optional(),
    specialities: z.array(z.string()).optional(),
    cuisine_types: z.array(z.string()).optional(),
    dietary: z.array(z.string()).optional(),
    service_options: z.array(z.string()).optional(),
    meal_times: z.array(z.string()).optional(),
    cravings: z.array(z.string()).optional(),
    retail_destination: z.string().optional(),
    area: z.string().optional(),
  })
);

//_geoloc schema [{latitude: 40.712776, longitude: -74.005974}]
const geolocSchema = z.array(
  z.object({
    lat: z.number(),
    lng: z.number(),
  })
);

//creator schema
const creatorSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().url(),
});

//partner schema
const partnerSchema = z.object({
  name: z.string(),
});

// Main Reel schema
export const ReelSchema = z.object({
  objectID: z.string(),
  places: placesSchema.min(1, 'Places array must not be empty'),
  //caption could be null
  caption: z.string().nullable().optional(),
  _geoloc: geolocSchema,
  url: z.string().url(),
  creator: creatorSchema.optional(),
  partner: partnerSchema,
  _rankingInfo: z.object({
    geoDistance: z.number(),
  }),
  __position: z.number().optional(),
});

// Type inference
export type Reel = z.infer<typeof ReelSchema>;

export type Creator = z.infer<typeof creatorSchema>;

// Validation function with detailed error handling
export const validateReel = (
  data: unknown
): { success: boolean; data?: Reel; error?: string } => {
  try {
    const validatedData = ReelSchema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors
        .map((err) => `${err.path.join('.')}: ${err.message}`)
        .join(', ');
      return { success: false, error: errorMessage };
    }
    return { success: false, error: 'Unknown validation error' };
  }
};

// Partial validation for updates
export const PartialReelSchema = ReelSchema.partial();
export type PartialReel = z.infer<typeof PartialReelSchema>;
