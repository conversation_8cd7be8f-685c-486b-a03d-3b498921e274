export type FilterType = 'Restaurants' | 'Creators';

export interface FilterValue {
  value: string;
  count: number;
  isSelected: boolean;
  originalValue?: string;
  indexName?: string;
}

export interface HomeContentProps {
  setCurrentIndex: (index: string) => void;
  currentIndex: string;
}

export interface RatingMenuItem {
  value: string;
  count: number;
  isRefined: boolean;
  stars: boolean[];
}

export interface NumericMenuItem {
  label: string;
  value: string;
  isRefined: boolean;
  // count is not returned by Algolia's NumericMenu connector
}

export interface RatingMenuProps {
  attribute: string;
  [key: string]: unknown; // Add index signature for flexibility
}

export interface RangeValue {
  min: number;
  max: number;
}

export interface FilterGroup {
  title: string;
  attribute: string;
  isSort?: boolean;
  type?: 'range' | 'singleRange';
  values?: FilterValue[];
  range?: RangeValue;
  currentRefinement?: RangeValue;
  refine?: (value: RangeValue | [number, number]) => void;
}
