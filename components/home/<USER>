import { useCallback, useMemo, useState, useEffect } from 'react';
import { useConnector } from 'react-instantsearch-core';
import connectNumericMenu from 'instantsearch.js/es/connectors/numeric-menu/connectNumericMenu';
import {
  useRefinementList,
  useRange,
  useInfiniteHits,
  useStats,
  useConfigure,
} from 'react-instantsearch-core';
import { useTranslation } from 'react-i18next';
import { DealTypeToLabel } from '@/components/ui/types/deal';
import { DealType } from '@/graphql/generated/graphql';
import { FilterValue, RangeValue, NumericMenuItem } from './types';
import type { Reel as ReelType } from '@/schemas/reel';
import { DEFAULT_RADIUS } from './constants';
import { useLocationServices } from '@/services/locationServices';

const LIMIT = 25;
const DEFAULT_MAX_REVIEWS = 1000;
// The initial load timeout was increased from 500ms to 1000ms to account for slower network conditions
// and ensure a smoother user experience during the initial data fetch.
const INITIAL_LOAD_TIMEOUT = 1000;

// Custom hook for distance filtering and geo sorting
export function useDistance(initialRadius = DEFAULT_RADIUS) {
  const [radius, setRadius] = useState(initialRadius);
  const [hasDistanceChanged, setHasDistanceChanged] = useState(false);
  const [currentSort, setCurrentSort] = useState<
    'recommended' | 'nearby' | 'rating' | 'newest'
  >('recommended');

  // Use the separated location services
  const locationServices = useLocationServices();

  // When radius changes, update the changed flag
  useEffect(() => {
    setHasDistanceChanged(radius !== initialRadius);
  }, [radius, initialRadius]);

  // Request location when geo features are needed
  const ensureLocationPermission = useCallback(async () => {
    return await locationServices.requestLocation();
  }, [locationServices.requestLocation]);

  // Configure Algolia based on current sort and distance filter
  const currentLocation = locationServices.getCurrentLocation();
  const shouldUseGeoLocation =
    (currentSort === 'nearby' || hasDistanceChanged) &&
    !locationServices.shouldDisableGeoFeatures() &&
    currentLocation;

  useConfigure({
    // Set location only when we have a valid location and geo features are enabled
    aroundLatLng: shouldUseGeoLocation ? currentLocation : undefined,
    // Use specific radius when distance filter is applied and we have location, otherwise 'all'
    aroundRadius:
      hasDistanceChanged &&
      !locationServices.shouldDisableGeoFeatures() &&
      currentLocation
        ? radius
        : 'all',
    getRankingInfo: true,
  });

  // Enhanced setRadius that requests location permission
  const setRadiusWithLocation = useCallback(
    async (newRadius: number) => {
      // If user is setting a distance filter, ensure we have location permission
      if (newRadius !== initialRadius) {
        const hasPermission = await ensureLocationPermission();
        if (!hasPermission) {
          return; // Don't set radius if location permission denied
        }
      }
      setRadius(newRadius);
    },
    [initialRadius, ensureLocationPermission]
  );

  // Enhanced setCurrentSort that requests location permission for nearby sort
  const setCurrentSortWithLocation = useCallback(
    async (newSort: 'recommended' | 'nearby' | 'rating' | 'newest') => {
      // If user selects nearby sort, ensure we have location permission
      if (newSort === 'nearby') {
        console.log(
          '📍 Nearby sort selected, requesting location permission...'
        );
        const hasPermission = await ensureLocationPermission();
        console.log('📍 Location permission result:', hasPermission);

        if (!hasPermission) {
          console.log(
            '📍 Location permission denied, not changing to nearby sort'
          );
          return; // Don't change to nearby sort if location permission denied
        }
      }

      console.log('📍 Setting current sort to:', newSort);
      setCurrentSort(newSort);
    },
    [ensureLocationPermission]
  );

  // Reset radius to initial value
  const resetRadius = useCallback(() => {
    setRadius(initialRadius);
    setHasDistanceChanged(false);
  }, [initialRadius]);

  return {
    radius,
    setRadius: setRadiusWithLocation,
    resetRadius,
    hasDistanceChanged,
    currentSort,
    setCurrentSort: setCurrentSortWithLocation,
    // Location-related properties from the service
    shouldDisableGeoFeatures: locationServices.shouldDisableGeoFeatures,
    isRequestingLocation: locationServices.isRequestingLocation,
    ensureLocationPermission,
    locationPermissionStatus: locationServices.locationPermissionStatus,
  };
}

// Custom NumericMenu hook for ratings
export function useNumericMenu(props: {
  attribute: string;
  items: { label: string; start?: number; end?: number }[];
}): {
  items: NumericMenuItem[];
  refine: (value: string) => void;
} {
  const connector = useConnector(connectNumericMenu, props);

  // Map Algolia's items to our expected format
  return {
    items: connector.items.map((item) => ({
      label: item.label,
      value: item.value,
      isRefined: item.isRefined,
    })),
    refine: connector.refine,
  };
}

// Custom hook for handling tag filters (Creator or Partner)
export function useTagFilters() {
  const [activeTagFilter, setActiveTagFilter] = useState<
    'by_creator' | 'by_place' | null
  >('by_place'); // Default to Restaurants

  // Configure Algolia to use tag filters based on selection
  useConfigure({
    tagFilters: activeTagFilter ? [activeTagFilter] : undefined,
  });

  // Apply initial filter
  useEffect(() => {
    // Default to by_place (Restaurants) on first render
    setActiveTagFilter('by_place');
  }, []);

  const handleFilterTypeChange = useCallback(
    (filterType: 'Creators' | 'Restaurants') => {
      if (filterType === 'Creators') {
        setActiveTagFilter('by_creator');
      } else if (filterType === 'Restaurants') {
        setActiveTagFilter('by_place');
      }
    },
    []
  );

  return {
    activeTagFilter,
    setActiveTagFilter,
    handleFilterTypeChange,
  };
}

// Custom hook for all filter facets
export function useFilterFacets(currentIndex: string) {
  const { t } = useTranslation();
  const [hasReviewsCountChanged, setHasReviewsCountChanged] = useState(false);

  // Use the distance hook to access the sorting state and distance filtering
  const {
    currentSort,
    setCurrentSort,
    radius,
    setRadius,
    resetRadius,
    hasDistanceChanged,
    shouldDisableGeoFeatures,
    isRequestingLocation,
    ensureLocationPermission,
    locationPermissionStatus,
  } = useDistance();

  // Use numeric menu for custom rating ranges
  const { items: numericRatingItems, refine: numericRefineRating } =
    useNumericMenu({
      attribute: 'places.rates.google',
      items: [
        { label: '4+', start: 4, end: 5 },
        { label: '4.5+', start: 4.5, end: 5 },
      ],
    });

  // Simplified logging
  // console.log('Rating Items:', numericRatingItems);

  // Define faceted search hooks
  const {
    items: locationFacets,
    refine: refineLocation,
    searchForItems: searchLocation,
    toggleShowMore,
    canToggleShowMore,
    isShowingMore,
  } = useRefinementList({
    attribute: 'places.area',
    operator: 'or',
    limit: 20,
  });

  const { items: dealTypeFacets, refine: refineDealTypes } = useRefinementList({
    attribute: 'places.deals.deal_type',
    sortBy: ['name'],
  });

  const { items: diningModesFacets, refine: refineDiningModes } =
    useRefinementList({
      attribute: 'places.specialities',
      sortBy: ['name'],
      limit: LIMIT,
    });

  const { items: cuisineTypesFacets, refine: refineCuisineTypes } =
    useRefinementList({
      attribute: 'places.cuisine_types',
      sortBy: ['name'],
      limit: LIMIT,
    });

  const { items: ambianceFacets, refine: refineAmbiance } = useRefinementList({
    attribute: 'places.ambiance',
    sortBy: ['name'],
    limit: LIMIT,
  });

  const { items: serviceTypeFacets, refine: refineServiceType } =
    useRefinementList({
      attribute: 'places.deals.service_types',
      sortBy: ['name'],
      limit: LIMIT,
    });

  const { items: dietaryFacets, refine: refineDietary } = useRefinementList({
    attribute: 'places.dietary',
    sortBy: ['name'],
    limit: LIMIT,
  });

  const { items: mealTimesFacets, refine: refineMealTimes } = useRefinementList(
    {
      attribute: 'places.meal_times',
      sortBy: ['name'],
      limit: LIMIT,
    }
  );

  const { items: parkingFacets, refine: refineParking } = useRefinementList({
    attribute: 'places.parking',
    sortBy: ['name'],
    limit: LIMIT,
  });

  const { items: cravingsFacets, refine: refineCravings } = useRefinementList({
    attribute: 'places.cravings',
    sortBy: ['name'],
    limit: LIMIT,
  });

  const {
    range,
    refine: refinePriceRange,
    start,
  } = useRange({
    attribute: 'places.price_per_person',
  });

  // Add reviews count range filter
  const {
    range: reviewsCountRange,
    refine: refineReviewsCount,
    start: reviewsCountStart,
  } = useRange({
    attribute: 'places.rates.reviews_count',
  });

  const { items: retailDestinationFacets, refine: refineRetailDestination } =
    useRefinementList({
      attribute: 'places.retail_destination',
      sortBy: ['name'],
      limit: LIMIT,
    });

  // Check if reviews count filter has changed from default
  useEffect(() => {
    if (
      reviewsCountRange &&
      reviewsCountStart &&
      typeof reviewsCountRange.min === 'number' &&
      reviewsCountStart.length > 0 &&
      typeof reviewsCountStart[0] === 'number'
    ) {
      const hasChanged = reviewsCountStart[0] > reviewsCountRange.min;
      setHasReviewsCountChanged(hasChanged);
    }
  }, [reviewsCountRange, reviewsCountStart]);

  // Transform facets into the format expected by FilterModal
  const filters = useMemo(
    () => [
      {
        title: t('filters.groups.sortBy'),
        attribute: 'sort',
        isSort: true,
        values: [
          {
            value: t('filters.sort.recommended'),
            count: 0,
            isSelected: currentSort === 'recommended',
            indexName: 'reels',
          },
          {
            value: t('filters.sort.nearby'),
            count: 0,
            isSelected: currentSort === 'nearby',
            indexName: 'reels',
          },
          {
            value: t('filters.sort.rating'),
            count: 0,
            isSelected: currentSort === 'rating',
            indexName: 'reels_by_rating',
          },
          {
            value: t('filters.sort.newest'),
            count: 0,
            isSelected: currentSort === 'newest',
            indexName: 'reels_by_created',
          },
        ],
      },
      // 1. Rating
      {
        title: t('filters.groups.rating'),
        attribute: 'places.rates.google',
        values: numericRatingItems.map((item) => ({
          value: item.label,
          count: 0, // We don't have counts for numeric menu
          isSelected: item.isRefined,
          originalValue: item.value,
        })),
      },
      // 2. Deal Type
      {
        title: t('filters.groups.dealType'),
        attribute: 'places.deals.deal_type',
        values: dealTypeFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: DealTypeToLabel[facet.value as DealType],
              count: facet.count,
              isSelected: facet.isRefined,
              originalValue: facet.value,
            })
          ),
      },
      // 3. Cuisine
      {
        title: t('filters.groups.cuisineTypes'),
        attribute: 'places.cuisine_types',
        values: cuisineTypesFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
      // 4. Cravings
      {
        title: t('filters.groups.cravings'),
        attribute: 'places.cravings',
        values: cravingsFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
      // 5. Ambiance
      {
        title: t('filters.groups.ambiance'),
        attribute: 'places.ambiance',
        values: ambianceFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
      // 6. Specialties
      {
        title: t('filters.groups.specialties'),
        attribute: 'places.specialities',
        values: diningModesFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
      // 7. Dietary
      {
        title: t('filters.groups.dietary'),
        attribute: 'places.dietary',
        values: dietaryFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
      // 8. Parking
      {
        title: t('filters.groups.parking'),
        attribute: 'places.parking',
        values: parkingFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
      // 9. Retail Destination
      {
        title: t('filters.groups.retailDestination'),
        attribute: 'places.retail_destination',
        values: retailDestinationFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
      // 10. Location
      {
        title: t('filters.groups.location'),
        attribute: 'places.area',
        values: locationFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
      // 11. Price Range
      {
        title: t('filters.groups.priceRange'),
        attribute: 'places.price_per_person',
        type: 'range' as const,
        range:
          range &&
          typeof range.min === 'number' &&
          typeof range.max === 'number' &&
          isFinite(range.min) &&
          isFinite(range.max)
            ? {
                min: range.min,
                max: range.max,
              }
            : undefined,
        currentRefinement:
          start &&
          start.length === 2 &&
          typeof start[0] === 'number' &&
          typeof start[1] === 'number' &&
          isFinite(start[0]) &&
          isFinite(start[1])
            ? {
                min: start[0],
                max: start[1],
              }
            : undefined,
        refine: (value: RangeValue | [number, number]) => {
          if (Array.isArray(value)) {
            refinePriceRange([value[0], value[1]]);
          } else {
            refinePriceRange([value.min, value.max]);
          }
        },
      },
      // Add Reviews Count Range
      {
        title: t('filters.groups.reviewsCount'),
        attribute: 'places.rates.reviews_count',
        type: 'singleRange' as const, // New type for single value slider
        range:
          reviewsCountRange &&
          typeof reviewsCountRange.min === 'number' &&
          typeof reviewsCountRange.max === 'number' &&
          isFinite(reviewsCountRange.min) &&
          isFinite(reviewsCountRange.max)
            ? {
                min: reviewsCountRange.min,
                max: reviewsCountRange.max,
              }
            : undefined,
        currentRefinement:
          reviewsCountStart &&
          reviewsCountStart.length === 2 &&
          typeof reviewsCountStart[0] === 'number' &&
          isFinite(reviewsCountStart[0])
            ? {
                min: reviewsCountStart[0],
                max: reviewsCountRange?.max || DEFAULT_MAX_REVIEWS, // Use max from range or default
              }
            : undefined,
        refine: (value: RangeValue | [number, number]) => {
          if (Array.isArray(value)) {
            refineReviewsCount([
              value[0],
              reviewsCountRange?.max || DEFAULT_MAX_REVIEWS,
            ]);
          } else {
            refineReviewsCount([
              value.min,
              reviewsCountRange?.max || DEFAULT_MAX_REVIEWS,
            ]);
          }
        },
      },
      // 12. Service Type
      {
        title: t('filters.groups.serviceType'),
        attribute: 'places.deals.service_types',
        values: serviceTypeFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
      // 13. Meal Times
      {
        title: t('filters.groups.mealTimes'),
        attribute: 'places.meal_times',
        values: mealTimesFacets
          .filter((facet) => facet.count > 0)
          .map(
            (facet): FilterValue => ({
              value: facet.value,
              count: facet.count,
              isSelected: facet.isRefined,
            })
          ),
      },
    ],
    [
      t,
      locationFacets,
      dealTypeFacets,
      diningModesFacets,
      currentSort,
      numericRatingItems,
      serviceTypeFacets,
      cuisineTypesFacets,
      ambianceFacets,
      dietaryFacets,
      mealTimesFacets,
      parkingFacets,
      cravingsFacets,
      range,
      refinePriceRange,
      retailDestinationFacets,
      start,
      hasDistanceChanged,
      reviewsCountRange,
      reviewsCountStart,
      refineReviewsCount,
    ]
  );

  const { nbHits } = useStats();

  return {
    filters,
    nbHits,
    refineLocation,
    refineDealTypes,
    refineDiningModes,
    refineRating: numericRefineRating,
    ratingItems: numericRatingItems,
    refineCuisineTypes,
    refineAmbiance,
    refineServiceType,
    refineDietary,
    refineMealTimes,
    refineParking,
    refineCravings,
    refinePriceRange,
    refineReviewsCount,
    refineRetailDestination,
    searchLocation,
    toggleShowMore,
    canToggleShowMore,
    isShowingMore,
    currentSort,
    setCurrentSort,
    hasReviewsCountChanged,
    setHasReviewsCountChanged,
    radius,
    setRadius,
    resetRadius,
    hasDistanceChanged,
    // Location-related properties
    shouldDisableGeoFeatures,
    isRequestingLocation,
    ensureLocationPermission,
    locationPermissionStatus,
  };
}

// Custom hook for handling filter toggles
export function useFilterToggle(
  setCurrentIndex: (index: string) => void,
  currentIndex: string,
  facets: ReturnType<typeof useFilterFacets>
) {
  const { t } = useTranslation();
  const {
    refineLocation,
    refineDealTypes,
    refineDiningModes,
    refineRating,
    ratingItems,
    refineCuisineTypes,
    refineAmbiance,
    refineServiceType,
    refineDietary,
    refineMealTimes,
    refineParking,
    refinePriceRange,
    refineReviewsCount,
    refineRetailDestination,
    refineCravings,
    filters,
    currentSort,
    setCurrentSort,
    setHasReviewsCountChanged,
    resetRadius,
    shouldDisableGeoFeatures,
  } = facets;

  // Handle filter toggle
  const handleFilterToggle = useCallback(
    async (attribute: string, value: string, indexName?: string) => {
      if (attribute === 'sort') {
        // Handle sort option changes
        if (value === t('filters.sort.recommended')) {
          await setCurrentSort('recommended');
          setCurrentIndex('reels');
        } else if (value === t('filters.sort.nearby')) {
          console.log('📍 User selected nearby sort');
          // Allow nearby sort to be selected - setCurrentSort will handle location request
          await setCurrentSort('nearby');
          setCurrentIndex('reels');
        } else if (value === t('filters.sort.rating')) {
          await setCurrentSort('rating');
          setCurrentIndex('reels_by_rating');
        } else if (value === t('filters.sort.newest')) {
          await setCurrentSort('newest');
          setCurrentIndex('reels_by_created');
        }
        return;
      }

      switch (attribute) {
        case 'places.rates.google':
          // For numeric menu, find the item by label or value
          const ratingItem = ratingItems.find(
            (item) => item.label === value || item.value === value
          );

          // Only toggle if the item exists
          if (ratingItem) {
            // If it's already refined, we want to clear it by passing empty string
            // Otherwise, apply the refinement
            if (ratingItem.isRefined) {
              // Use full range 0-5 instead of empty string for clearing
              refineRating('0');
            } else {
              refineRating(ratingItem.value);
            }
          }
          break;
        case 'places.area':
          refineLocation(value);
          break;
        case 'places.deals.deal_type':
          refineDealTypes(value);
          break;
        case 'places.specialities':
          refineDiningModes(value);
          break;
        case 'places.cuisine_types':
          refineCuisineTypes(value);
          break;
        case 'places.ambiance':
          refineAmbiance(value);
          break;
        case 'places.deals.service_types':
          refineServiceType(value);
          break;
        case 'places.dietary':
          refineDietary(value);
          break;
        case 'places.meal_times':
          refineMealTimes(value);
          break;
        case 'places.parking':
          refineParking(value);
          break;
        case 'places.cravings':
          refineCravings(value);
          break;
        case 'places.price_per_person':
          if (typeof value === 'object' && 'min' in value && 'max' in value) {
            const rangeValue = value as RangeValue;
            refinePriceRange([rangeValue.min, rangeValue.max]);
          }
          break;
        case 'places.rates.reviews_count':
          if (typeof value === 'object' && 'min' in value) {
            const rangeValue = value as RangeValue;
            refineReviewsCount([rangeValue.min, rangeValue.max || 1000]);
          }
          break;
        case 'places.retail_destination':
          refineRetailDestination(value);
          break;
      }
    },
    [
      refineLocation,
      refineDealTypes,
      refineDiningModes,
      refineRating,
      ratingItems,
      setCurrentIndex,
      currentIndex,
      refineCuisineTypes,
      refineAmbiance,
      refineServiceType,
      refineDietary,
      refineMealTimes,
      refineParking,
      refinePriceRange,
      refineReviewsCount,
      refineRetailDestination,
      refineCravings,
      filters,
      currentSort,
      setCurrentSort,
      setHasReviewsCountChanged,
      resetRadius,
      shouldDisableGeoFeatures,
    ]
  );

  // Handle clear all filters
  const handleClearAllFilters = useCallback(async () => {
    // Reset sort and index to default
    await setCurrentSort('recommended');
    setCurrentIndex('reels');
    setHasReviewsCountChanged(false);

    // Reset distance filter
    resetRadius();

    // Clear other filters
    filters.forEach((group) => {
      if (!group.isSort && group.values) {
        group.values.forEach((item: FilterValue) => {
          if (item.isSelected) {
            switch (group.attribute) {
              case 'places.area':
                refineLocation(item.value);
                break;
              case 'places.deals.deal_type':
                refineDealTypes(item.originalValue || item.value);
                break;
              case 'places.specialities':
                refineDiningModes(item.value);
                break;
              case 'places.rates.google':
                // With the rating menu, just clear by directly passing the value
                if (item.originalValue) {
                  // Use '0' instead of empty string to show full range (0-5)
                  refineRating('0');
                }
                break;
              case 'places.cuisine_types':
                refineCuisineTypes(item.value);
                break;
              case 'places.ambiance':
                refineAmbiance(item.value);
                break;
              case 'places.dietary':
                refineDietary(item.value);
                break;
              case 'places.meal_times':
                refineMealTimes(item.value);
                break;
              case 'places.parking':
                refineParking(item.value);
                break;
              case 'places.cravings':
                refineCravings(item.value);
                break;
              case 'places.retail_destination':
                refineRetailDestination(item.value);
                break;
              case 'places.deals.service_types':
                refineServiceType(item.value);
                break;
            }
          }
        });
      } else if (
        group.type === 'singleRange' &&
        group.attribute === 'places.rates.reviews_count' &&
        group.range
      ) {
        // Reset reviews count filter to min value
        refineReviewsCount([
          group.range.min,
          group.range.max || DEFAULT_MAX_REVIEWS,
        ]);
      }
    });
  }, [
    filters,
    refineLocation,
    refineDealTypes,
    refineDiningModes,
    refineRating,
    setCurrentIndex,
    refineCuisineTypes,
    refineAmbiance,
    refineServiceType,
    refineDietary,
    refineMealTimes,
    refineParking,
    refineRetailDestination,
    refineCravings,
    refineReviewsCount,
    setCurrentSort,
    setHasReviewsCountChanged,
    resetRadius,
  ]);

  return {
    handleFilterToggle,
    handleClearAllFilters,
  };
}

// Custom hook for handling infinite hits
export function useReels() {
  const { items, isLastPage, showMore } = useInfiniteHits<ReelType>();
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);

  // Set hasInitiallyLoaded to true after initial render to allow empty state
  useEffect(() => {
    const timer = setTimeout(() => {
      setHasInitiallyLoaded(true);
    }, INITIAL_LOAD_TIMEOUT); // Wait 0.5 second after mount before allowing empty state

    return () => clearTimeout(timer);
  }, []);

  // Also set it to true when we get items
  useEffect(() => {
    if (items.length > 0) {
      setHasInitiallyLoaded(true);
    }
  }, [items.length]);

  // We're loading if we haven't initially loaded yet
  const isLoading = !hasInitiallyLoaded;

  const loadMore = useCallback(() => {
    if (!isLastPage) {
      showMore();
    }
  }, [isLastPage, showMore]);

  return {
    items,
    isLastPage,
    loadMore,
    isLoading,
  };
}

// Custom hook for text search
export function useTextSearch() {
  const [textQuery, setTextQuery] = useState('');

  // Configure Algolia with text query
  useConfigure({
    query: textQuery,
  });

  const applyTextSearch = useCallback((query: string) => {
    setTextQuery(query);
  }, []);

  const clearTextSearch = useCallback(() => {
    setTextQuery('');
  }, []);

  return {
    textQuery,
    applyTextSearch,
    clearTextSearch,
    hasTextSearch: !!textQuery.trim(),
  };
}
