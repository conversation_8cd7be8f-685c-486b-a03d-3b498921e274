import React, {
  useCallback,
  useRef,
  useState,
  useMemo,
  useEffect,
} from 'react';
import { View, StyleSheet } from 'react-native';
import { useIsFocused } from '@react-navigation/native';
import { Modalize } from 'react-native-modalize';
import * as Haptics from 'expo-haptics';
import { FilterModal } from '@/components/ui/FilterModal';
import { colors } from '@/constants/Colors';
import { Header } from './Header';
import { ReelList } from './ReelList';
import {
  useFilterFacets,
  useFilterToggle,
  useReels,
  useTagFilters,
  useTextSearch,
} from './hooks';
import { HomeContentProps, FilterType } from './types';
import { Configure } from 'react-instantsearch-core';

const TAG_FILTER_APPLY_DELAY_MS = 300;

export function HomeContent({
  setCurrentIndex,
  currentIndex,
}: HomeContentProps) {
  const [visibleIndex, setVisibleIndex] = useState(0);
  const [selectedFilter, setSelectedFilter] =
    useState<FilterType>('Restaurants');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const filterModalRef = useRef<Modalize>(null);
  const isFocused = useIsFocused();

  // Track if location permission has been requested to avoid multiple requests
  const [hasRequestedLocation, setHasRequestedLocation] = useState(false);

  // Use our custom hooks
  const facets = useFilterFacets(currentIndex);
  const { handleFilterToggle, handleClearAllFilters } = useFilterToggle(
    setCurrentIndex,
    currentIndex,
    facets
  );
  const { items, isLastPage, loadMore, isLoading } = useReels();

  // Use tag filters hook
  const { handleFilterTypeChange } = useTagFilters();

  // Use text search hook
  const { textQuery, applyTextSearch, clearTextSearch } = useTextSearch();

  // Apply initial filter on mount
  useEffect(() => {
    handleFilterTypeChange('Restaurants');
  }, [handleFilterTypeChange]);

  // Request location permission when user reaches the fifth reel (index 4)
  useEffect(() => {
    if (
      !hasRequestedLocation &&
      visibleIndex >= 4 &&
      items.length > 4 &&
      facets.ensureLocationPermission &&
      facets.locationPermissionStatus === 'pending' // Only request if permission has never been asked
    ) {
      setHasRequestedLocation(true);
      // Request location permission in background (non-blocking)
      facets.ensureLocationPermission().catch(() => {
        // Silently handle rejection - user can still use the app
        console.log('📍 Location permission declined by user at fifth reel');
      });
    }
  }, [
    visibleIndex,
    hasRequestedLocation,
    items.length,
    facets.ensureLocationPermission,
    facets.locationPermissionStatus,
  ]);

  // Check if any filters are selected
  const hasSelectedFilters = useMemo(() => {
    // Include distance filter and reviews count filter in the selection check
    return (
      facets.hasDistanceChanged ||
      facets.hasReviewsCountChanged ||
      facets.filters.some((group) => {
        // Check for range filters with custom refinement
        if (
          group.type === 'range' &&
          group.currentRefinement &&
          group.range &&
          (group.currentRefinement.min !== group.range.min ||
            group.currentRefinement.max !== group.range.max)
        ) {
          return true;
        }

        // Check for normal filters
        if (group.values && !group.isSort) {
          return group.values.some((item) => item.isSelected);
        }

        return false;
      })
    );
  }, [
    facets.filters,
    facets.hasDistanceChanged,
    facets.hasReviewsCountChanged,
  ]);

  const handleFilterSelect = useCallback(
    (filter: FilterType) => {
      // Update the UI state
      setSelectedFilter(filter);
      setIsDropdownOpen(false);

      // Apply the tag filter
      handleFilterTypeChange(filter);
    },
    [handleFilterTypeChange]
  );

  const openFilterModal = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    filterModalRef.current?.open();
  }, []);

  const handleShowResults = useCallback(() => {
    filterModalRef.current?.close();
  }, []);

  // Custom clear all function that explicitly resets the distance filter
  const handleClearAll = useCallback(async () => {
    // Call the original clear function from the hook
    await handleClearAllFilters();

    // Explicitly reset the distance filter
    facets.resetRadius();

    // Clear text search
    clearTextSearch();
  }, [handleClearAllFilters, facets.resetRadius, clearTextSearch]);

  const handleApplyTagFilter = useCallback(
    (attribute: string, value: string) => {
      // First clear all existing filters to ensure clean state
      handleClearAll();

      // Then apply the specific tag filter
      // Use a small delay to ensure the clear operation completes first
      setTimeout(() => {
        handleFilterToggle(attribute, value);
      }, TAG_FILTER_APPLY_DELAY_MS);
    },
    [handleFilterToggle, handleClearAll]
  );

  const handleApplyTextSearch = useCallback(
    (query: string) => {
      if (query.trim()) {
        // Apply text search
        applyTextSearch(query);
      } else {
        // Clear text search
        clearTextSearch();
      }
    },
    [applyTextSearch, clearTextSearch]
  );

  // Create a key that changes when any filter state changes
  const filterStateKey = useMemo(() => {
    // Get all selected filters
    const selectedFilters = facets.filters
      .filter((group) => group.values && !group.isSort)
      .flatMap(
        (group) =>
          group.values
            ?.filter((item) => item.isSelected)
            .map((item) => `${group.attribute}:${item.value}`) || []
      )
      .sort()
      .join('|');

    // Combine all filter-related state
    return `${facets.currentSort}-${textQuery}-${facets.radius}-${selectedFilters}`;
  }, [facets.currentSort, facets.filters, textQuery, facets.radius]);

  return (
    <View style={styles.container}>
      <Configure hitsPerPage={20} page={0} />
      <Header
        selectedFilter={selectedFilter}
        isDropdownOpen={isDropdownOpen}
        setIsDropdownOpen={setIsDropdownOpen}
        openFilterModal={openFilterModal}
        hasSelectedFilters={hasSelectedFilters}
        onSelect={handleFilterSelect}
        onApplyTagFilter={handleApplyTagFilter}
        onApplyTextSearch={handleApplyTextSearch}
        currentTextSearch={textQuery}
        onClearFilters={handleClearAll}
      />

      <ReelList
        key={`reel-list-${filterStateKey}`}
        items={items}
        visibleIndex={visibleIndex}
        setVisibleIndex={setVisibleIndex}
        isLastPage={isLastPage}
        loadMore={loadMore}
        isFocused={isFocused}
        loading={isLoading}
        currentIndex={currentIndex}
      />

      <FilterModal
        ref={filterModalRef}
        filters={facets.filters}
        onFilterToggle={handleFilterToggle}
        onClearAll={handleClearAll}
        onShowResults={handleShowResults}
        totalResults={facets.nbHits}
        searchLocation={facets.searchLocation}
        toggleShowMore={facets.toggleShowMore}
        canToggleShowMore={facets.canToggleShowMore}
        isShowingMore={facets.isShowingMore}
        radius={facets.radius}
        setRadius={facets.setRadius}
        resetRadius={facets.resetRadius}
        hasDistanceChanged={facets.hasDistanceChanged}
        shouldDisableGeoFeatures={facets.shouldDisableGeoFeatures()}
        ensureLocationPermission={facets.ensureLocationPermission}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.base[950],
  },
});
