import React, { useCallback, useState, useEffect, useRef } from 'react';
import {
  View,
  TouchableWithoutFeedback,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  Keyboard,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { FilterIcon } from '@/components/icons/FilterIcon';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { FilterDropdown } from '@/components/ui/FilterDropdown';
import { RightHeaderButton } from '@/components/ui/RightHeaderButton';
import type { FilterType } from './types';
import { Text } from '../ui/Text';
import { PlatformIcon } from '../ui/PlatformIcon';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
} from 'react-native-reanimated';
import { animateSearch } from '@/utils/animations';
import { liteClient as algoliasearch } from 'algoliasearch/lite';
import Constants from 'expo-constants';
import { useRouter } from 'expo-router';
import { getEmojiForItem } from '@/constants/Emojis';

const { ALGOLIA_APP_ID, ALGOLIA_SEARCH_KEY } = Constants.expoConfig?.extra as {
  ALGOLIA_APP_ID: string;
  ALGOLIA_SEARCH_KEY: string;
};
const ITEM_HEIGHT = moderateScale(42);

// Search result interface
interface SearchResult {
  id: string;
  title: string;
  type: 'place' | 'creator' | 'tag';
  icon: 'storefront' | 'person' | string; // Allow string for emojis
  attribute?: string; // For tags, store which attribute they belong to
  query?: string;
  emoji?: string; // For tags, store the emoji
}

// Custom hook for Algolia search
function useAlgoliaSearch(query: string) {
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    const searchClient = algoliasearch(ALGOLIA_APP_ID, ALGOLIA_SEARCH_KEY);

    const performSearch = async () => {
      setLoading(true);
      try {
        // Use search method with multiple queries for federated search
        const searchResponse = await searchClient.search([
          // First query: Search for partners
          {
            indexName: 'reels',
            params: {
              query: query.trim(),
              hitsPerPage: 20,
              attributesToRetrieve: ['places', 'partner'],
              restrictSearchableAttributes: ['partner.name'],
            },
          },
          // Second query: Search for creators
          {
            indexName: 'reels',
            params: {
              query: query.trim(),
              hitsPerPage: 20,
              attributesToRetrieve: ['creator'],
              restrictSearchableAttributes: ['creator.name'],
            },
          },
          // Third query: Search for some tags
          {
            indexName: 'reels',
            params: {
              query: query.trim(),
              hitsPerPage: 20,
              attributesToRetrieve: ['places'],
              restrictSearchableAttributes: [
                'places.cuisine_types',
                'places.ambiance',
                'places.cravings',
                'places.dietary',
                'places.specialities',
                'places.meal_times',
                'places.area',
                'places.retail_destination',
              ],
            },
          },
        ]);

        const searchResults: SearchResult[] = [];

        // Process partners from first query (index 0)
        const partnersResult = searchResponse.results[0] as any;
        const seenPartners = new Set<string>();

        if (partnersResult.hits) {
          partnersResult.hits.forEach((hit: any) => {
            if (hit.partner && hit.partner.name) {
              if (
                hit.partner.name &&
                hit.places[0].id &&
                hit.partner.id &&
                !seenPartners.has(hit.partner.id) &&
                hit.partner.name.toLowerCase().includes(query.toLowerCase()) &&
                seenPartners.size < 3
              ) {
                seenPartners.add(hit.partner.id);
                searchResults.push({
                  id: hit.places[0].id,
                  title: hit.partner.name,
                  type: 'place',
                  icon: 'storefront',
                  query: query.trim(),
                });
              }
            }
          });
        }

        // Process creators from second query (index 1)
        const creatorsResult = searchResponse.results[1] as any;
        const seenCreators = new Set<string>();

        if (creatorsResult.hits) {
          creatorsResult.hits.forEach((hit: any) => {
            if (
              hit.creator &&
              hit.creator.name &&
              hit.creator.id &&
              !seenCreators.has(hit.creator.id) &&
              hit.creator.name.toLowerCase().includes(query.toLowerCase()) &&
              seenCreators.size < 3
            ) {
              seenCreators.add(hit.creator.id);
              searchResults.push({
                id: hit.creator.id,
                title: hit.creator.name,
                type: 'creator',
                icon: 'person',
                query: query.trim(),
              });
            }
          });
        }

        // Process some tags from third query (index 2)
        const tagsResult = searchResponse.results[2] as any;
        const seenTags = new Set<string>();

        if (tagsResult.hits) {
          tagsResult.hits.forEach((hit: any) => {
            if (
              hit.places &&
              Array.isArray(hit.places) &&
              hit.places.length > 0
            ) {
              // Only process the first place
              const place = hit.places[0];

              // Helper function to process array attributes
              const processArrayAttribute = (
                attributeArray: string[] | undefined,
                attributeName: string
              ) => {
                if (attributeArray && Array.isArray(attributeArray)) {
                  attributeArray.forEach((item: string) => {
                    if (
                      item &&
                      !seenTags.has(item) &&
                      item.toLowerCase().includes(query.toLowerCase()) &&
                      seenTags.size < 3
                    ) {
                      seenTags.add(item);
                      const emoji = getEmojiForItem(item, attributeName);
                      searchResults.push({
                        id: item,
                        title: item,
                        type: 'tag',
                        icon: 'tag', // Keep for fallback
                        attribute: `places.${attributeName}`, // Store the full attribute path
                        query: query.trim(),
                        emoji: emoji,
                      });
                    }
                  });
                }
              };

              // Process all place attributes
              processArrayAttribute(place.cuisine_types, 'cuisine_types');
              processArrayAttribute(place.ambiance, 'ambiance');
              processArrayAttribute(place.cravings, 'cravings');
              processArrayAttribute(place.dietary, 'dietary');
              processArrayAttribute(place.specialities, 'specialities');
              processArrayAttribute(place.meal_times, 'meal_times');

              // Process string attributes (area and retail_destination)
              const processStringAttribute = (
                attributeValue: string | undefined,
                attributeName: string
              ) => {
                if (
                  attributeValue &&
                  typeof attributeValue === 'string' &&
                  !seenTags.has(attributeValue) &&
                  attributeValue.toLowerCase().includes(query.toLowerCase()) &&
                  seenTags.size < 3
                ) {
                  seenTags.add(attributeValue);
                  const emoji = getEmojiForItem(attributeValue, attributeName);
                  searchResults.push({
                    id: attributeValue,
                    title: attributeValue,
                    type: 'tag',
                    icon: 'tag', // Keep for fallback
                    attribute: `places.${attributeName}`, // Store the full attribute path
                    query: query.trim(),
                    emoji: emoji,
                  });
                }
              };

              processStringAttribute(place.area, 'area');
              processStringAttribute(
                place.retail_destination,
                'retail_destination'
              );
            }
          });
        }

        setResults(searchResults);
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
      } finally {
        setLoading(false);
      }
    };

    // Debounce search
    const timeoutId = setTimeout(performSearch, 300);
    return () => clearTimeout(timeoutId);
  }, [query]);

  return { results, loading };
}

interface HeaderProps {
  selectedFilter: FilterType;
  isDropdownOpen: boolean;
  setIsDropdownOpen: React.Dispatch<React.SetStateAction<boolean>>;
  openFilterModal: () => void;
  hasSelectedFilters?: boolean;
  onSelect: (filter: FilterType) => void;
  onApplyTagFilter?: (attribute: string, value: string) => void;
  onApplyTextSearch?: (query: string) => void;
  onClearFilters?: () => void;
  currentTextSearch?: string;
}

interface SearchResultItemProps {
  item: SearchResult;
  onPress: (item: SearchResult) => void;
  isLast?: boolean;
}

const SearchResultItem: React.FC<SearchResultItemProps> = ({
  item,
  onPress,
  isLast = false,
}) => {
  // Helper function to highlight search query in text
  const highlightText = (text: string, query: string) => {
    if (!query.trim()) return text;

    const parts = text.split(new RegExp(`(${query})`, 'gi'));
    return parts.map((part, index) => {
      const isMatch = part.toLowerCase() === query.toLowerCase();
      return (
        <Text
          key={index}
          variant='caption'
          color={isMatch ? colors.base[950] : colors.darkGrey[700]}
          weight={isMatch ? 'bold' : 'regular'}
        >
          {part}
        </Text>
      );
    });
  };

  return (
    <TouchableOpacity
      style={[styles.searchResultItem, isLast && styles.searchResultItemLast]}
      onPress={() => onPress(item)}
      activeOpacity={0.7}
    >
      {item.type === 'tag' && item.emoji ? (
        <Text style={styles.searchResultEmoji}>{item.emoji}</Text>
      ) : (
        <PlatformIcon
          name={item.icon as string}
          color={colors.darkGrey[400]}
          size={moderateScale(24)}
          style={styles.searchResultIcon}
        />
      )}
      <Text variant='caption' color={colors.darkGrey[700]}>
        {highlightText(item.title, item.query || '')}
      </Text>
    </TouchableOpacity>
  );
};

export function Header({
  selectedFilter,
  isDropdownOpen,
  setIsDropdownOpen,
  openFilterModal,
  hasSelectedFilters = false,
  onSelect,
  onApplyTagFilter,
  onApplyTextSearch,
  onClearFilters,
  currentTextSearch,
}: HeaderProps) {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const searchInputRef = useRef<TextInput>(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  //define state for plan text search state
  const [isPlanTextSearch, setIsPlanTextSearch] = useState(!!currentTextSearch);

  // Update local state when currentTextSearch prop changes
  useEffect(() => {
    setIsPlanTextSearch(!!currentTextSearch);
  }, [currentTextSearch]);

  // Animation values
  const searchAnimation = useSharedValue(0);
  const elementsOpacity = useSharedValue(1);

  // Use Algolia search hook
  const { results: algoliaResults, loading: searchLoading } =
    useAlgoliaSearch(searchQuery);

  // Only use Algolia results
  const searchResults = algoliaResults;

  const handleSearchResultPress = useCallback(
    (item: SearchResult) => {
      console.log('Selected search result:', item);

      // Dismiss keyboard and blur input
      Keyboard.dismiss();
      searchInputRef.current?.blur();

      // Navigate to the appropriate screen based on type
      if (item.type === 'place') {
        router.push(`/places/${item.id}`);
      } else if (item.type === 'creator') {
        router.push(`/creators/${item.id}`);
      } else if (item.type === 'tag' && onApplyTagFilter && item.attribute) {
        // For tags, apply the filter using the stored attribute
        onApplyTagFilter(item.attribute, item.title);
      }

      // Close search and clear query
      setIsSearchOpen(false);
      setSearchQuery('');
      animateSearch(searchAnimation, elementsOpacity, false);
    },
    [router, searchAnimation, elementsOpacity, onApplyTagFilter]
  );

  const handleInputBlur = useCallback(() => {
    // Close search if there's no query when input loses focus
    if (!searchQuery.trim()) {
      setIsSearchOpen(false);
      animateSearch(searchAnimation, elementsOpacity, false);
    }
  }, [searchQuery, searchAnimation, elementsOpacity]);

  const toggleDropdown = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setIsDropdownOpen((prev) => !prev);
  }, [setIsDropdownOpen]);

  const handleFilterSelect = useCallback(
    (filter: FilterType) => {
      setIsDropdownOpen(false);
      if (filter === 'Creators' || filter === 'Restaurants') {
        onSelect(filter);
      }
    },
    [setIsDropdownOpen, onSelect]
  );

  const toggleSearch = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const newSearchState = !isSearchOpen;
    setIsSearchOpen(newSearchState);

    // Use the animation utility
    animateSearch(searchAnimation, elementsOpacity, newSearchState);

    if (newSearchState) {
      // Opening search - focus input after animation starts
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    } else {
      // Closing search - dismiss keyboard and clear query
      Keyboard.dismiss();
      searchInputRef.current?.blur();
      setSearchQuery('');
      setIsPlanTextSearch(false);
      onApplyTextSearch?.('');
    }
  }, [isSearchOpen, searchAnimation, elementsOpacity, onApplyTextSearch]);

  const handleTextSearch = useCallback(() => {
    if (searchQuery.trim() && onApplyTextSearch) {
      // Clear any selected filters before applying text search
      if (onClearFilters) {
        onClearFilters();
      }

      // Apply the text search filter after clearing filters
      setTimeout(() => {
        onApplyTextSearch(searchQuery.trim());
      }, 100);

      // Close keyboard and search interface
      Keyboard.dismiss();
      searchInputRef.current?.blur();
      setIsSearchOpen(false);
      animateSearch(searchAnimation, elementsOpacity, false);
      setIsPlanTextSearch(true);
    }
  }, [
    searchQuery,
    onApplyTextSearch,
    onClearFilters,
    searchAnimation,
    elementsOpacity,
  ]);

  const headerStyle = [
    styles.header,
    { paddingTop: insets.top + verticalScale(10) },
  ];

  // Pre-calculate scaled values outside of animated styles to avoid worklet errors
  const buttonWidth = moderateScale(48);
  const searchWidth = moderateScale(280);

  // Animated styles
  const searchContainerStyle = useAnimatedStyle(() => {
    const width = interpolate(
      searchAnimation.value,
      [0, 1],
      [buttonWidth, searchWidth] // Use pre-calculated values
    );

    return {
      width,
      opacity: searchAnimation.value > 0 ? 1 : 0,
    };
  });

  const elementsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: elementsOpacity.value,
    transform: [
      {
        translateX: interpolate(elementsOpacity.value, [1, 0], [0, 20]),
      },
    ],
  }));

  return (
    <>
      {isDropdownOpen && (
        <TouchableWithoutFeedback onPress={() => setIsDropdownOpen(false)}>
          <View style={styles.overlay} />
        </TouchableWithoutFeedback>
      )}

      <LinearGradient
        colors={[colors.blackTransparent.black08, 'transparent']}
        style={headerStyle}
      >
        {/* Filter Dropdown - Hidden when search is open */}
        <Animated.View style={elementsAnimatedStyle}>
          <FilterDropdown
            selectedFilter={selectedFilter}
            isOpen={isDropdownOpen}
            onToggle={toggleDropdown}
            onSelect={handleFilterSelect}
          />
        </Animated.View>

        <View style={styles.headerButtons}>
          {/* Filter Button - Hidden when search is open */}
          <Animated.View style={elementsAnimatedStyle}>
            <RightHeaderButton
              onPress={openFilterModal}
              variant={hasSelectedFilters ? 'white' : 'light'}
            >
              <FilterIcon
                color={
                  hasSelectedFilters ? colors.primary[950] : colors.base[100]
                }
              />
            </RightHeaderButton>
          </Animated.View>

          {/* Search Input Container */}
          <Animated.View
            style={[styles.searchContainerBaseStyle, searchContainerStyle]}
          >
            <PlatformIcon
              name='magnifyingglass'
              color={colors.darkGrey[400]}
              size={moderateScale(20)}
              style={styles.searchInputIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder='Search...'
              placeholderTextColor={colors.darkGrey[400]}
              value={searchQuery}
              onChangeText={setSearchQuery}
              ref={searchInputRef}
              onBlur={handleInputBlur}
              returnKeyType='search'
              onSubmitEditing={handleTextSearch}
            />
          </Animated.View>

          {/* Search/Close Button */}
          {isSearchOpen ? (
            <RightHeaderButton variant='white' onPress={toggleSearch}>
              <PlatformIcon
                name='xmark'
                color={colors.primary[950]}
                size={moderateScale(24)}
              />
            </RightHeaderButton>
          ) : (
            <RightHeaderButton
              variant={isPlanTextSearch ? 'white' : 'light'}
              onPress={toggleSearch}
            >
              <PlatformIcon
                name='magnifyingglass'
                color={
                  isPlanTextSearch ? colors.primary[950] : colors.base[100]
                }
                size={moderateScale(24)}
              />
            </RightHeaderButton>
          )}
        </View>
      </LinearGradient>

      {/* Search Results List */}
      {isSearchOpen && searchResults.length > 0 && (
        <View
          style={[
            styles.searchResultsContainer,
            {
              top: insets.top + verticalScale(70),
              height: Math.min(
                searchResults.length * ITEM_HEIGHT,
                moderateScale(450)
              ), // Dynamic height with max limit
            },
          ]}
        >
          <FlatList
            data={searchResults}
            keyExtractor={(item) => item.id}
            renderItem={({ item, index }) => (
              <SearchResultItem
                item={item}
                onPress={handleSearchResultPress}
                isLast={index === searchResults.length - 1}
              />
            )}
            style={styles.searchResultsList}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps='handled'
            scrollEnabled={searchResults.length > 5} // Only enable scroll if more than 5 items
          />
        </View>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 100,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 100,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: moderateScale(16),
    paddingBottom: verticalScale(10),
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(16),
  },
  searchContainerBaseStyle: {
    position: 'absolute',
    right: moderateScale(64),
    height: moderateScale(48),
    backgroundColor: colors.base[100],
    borderRadius: moderateScale(24),
    justifyContent: 'center',
    paddingHorizontal: moderateScale(16),
    shadowColor: colors.primary[950],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInput: {
    color: colors.base[950],
    fontFamily: 'Urbanist',
    width: '100%',
    paddingLeft: moderateScale(28),
  },
  searchInputIcon: {
    position: 'absolute',
    left: moderateScale(16),
    top: moderateScale(14),
  },
  searchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    height: ITEM_HEIGHT,
    paddingHorizontal: moderateScale(16),
  },
  searchResultItemLast: {
    borderBottomWidth: 0,
  },
  searchResultIcon: {
    marginRight: moderateScale(16),
  },
  searchResultEmoji: {
    fontSize: moderateScale(20),
    marginRight: moderateScale(16),
    width: moderateScale(24),
    textAlign: 'center',
  },
  searchResultsContainer: {
    position: 'absolute',
    left: moderateScale(16),
    right: moderateScale(16),
    zIndex: 100,
    backgroundColor: colors.base[100],
    borderRadius: moderateScale(16),
    shadowColor: colors.primary[950],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  searchResultsList: {
    flex: 1,
  },
});
