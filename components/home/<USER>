import { useLayoutEffect } from 'react';
import { useInstantSearch } from 'react-instantsearch-core';
import { createInsightsMiddleware } from 'instantsearch.js/es/middlewares';
import aa from 'search-insights';
import { useAuthStore } from '@/store/auth';
import Constants from 'expo-constants';

const { ALGOLIA_APP_ID, ALGOLIA_SEARCH_KEY } = Constants.expoConfig?.extra as {
  ALGOLIA_APP_ID: string;
  ALGOLIA_SEARCH_KEY: string;
};

// Validate Algolia configuration
if (!ALGOLIA_APP_ID || !ALGOLIA_SEARCH_KEY) {
  console.error(
    'Algolia configuration is missing. Please provide both ALGOLIA_APP_ID and ALGOLIA_SEARCH_KEY.'
  );
  throw new Error('Algolia configuration is invalid.');
}
// Initialize the insights client
aa('init', { appId: ALGOLIA_APP_ID, apiKey: ALGOLIA_SEARCH_KEY });

// Export the aa client for use in the analytics service
export { aa };

/**
 * InsightsMiddleware component integrates Algolia Insights tracking
 * with InstantSearch and sets the user token for personalization.
 */
function InsightsMiddleware() {
  const { addMiddlewares } = useInstantSearch();
  const user = useAuthStore((state: any) => state.user);

  useLayoutEffect(() => {
    // Set user token if user is available
    if (user?.id) {
      aa('setUserToken', user.id);
    }

    // Create and add the insights middleware
    const middleware = createInsightsMiddleware({
      insightsClient: aa,
    });

    return addMiddlewares(middleware);
  }, [addMiddlewares, user]);

  return null;
}

export default InsightsMiddleware;
