import React, { useCallback, useMemo, useState } from 'react';
import {
  View,
  ViewToken,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { FlashList, ListRenderItem } from '@shopify/flash-list';
import Reel from '@/components/Reel';
import { Text } from '@/components/ui/Text';
import { colors } from '@/constants/Colors';
import { verticalScale } from '@/utils/scaling';
import type { Reel as ReelType } from '@/schemas/reel';
import { useReelStatusQuery } from '@/graphql/generated/graphql';
import { useTranslation } from 'react-i18next';

const { height: WINDOW_HEIGHT, width: WINDOW_WIDTH } = Dimensions.get('window');

// Memoize the FlashList for better performance
const MemoizedFlashList = React.memo(FlashList<ReelType>);

interface ReelListProps {
  items: ReelType[];
  visibleIndex: number;
  setVisibleIndex: (index: number) => void;
  isLastPage: boolean;
  loadMore: () => void;
  isFocused: boolean;
  initialScrollIndex?: number; // Optional prop for initial scroll position
  isStandalone?: boolean; // New prop to indicate if reels are shown outside of home (without bottom tabs)
  loading?: boolean; // Add loading prop to prevent empty state flash
  currentIndex?: string; // Add currentIndex prop for analytics
}

export function ReelList({
  items,
  visibleIndex,
  setVisibleIndex,
  isLastPage,
  loadMore,
  isFocused,
  initialScrollIndex,
  isStandalone = false, // Default to false (home screen mode)
  loading = false, // Default to false (not loading)
  currentIndex,
}: ReelListProps) {
  const { t } = useTranslation();
  // Add shared mute state
  const [isMuted, setIsMuted] = useState(true);
  // Add flag to prevent initial onEndReached trigger
  const [hasScrolled, setHasScrolled] = useState(false);

  // Toggle mute function
  const toggleMute = useCallback(() => {
    setIsMuted((prev) => !prev);
  }, []);

  // Extract all reel IDs for batch querying
  const reelIds = useMemo(() => items.map((item) => item.objectID), [items]);

  // Fetch reel statuses for all visible reels in one query
  const { data: reelStatusData } = useReelStatusQuery({
    variables: {
      ids: reelIds,
    },
    skip: reelIds.length === 0,
  });

  // Create a map of reel statuses for easy lookup
  const reelStatusMap = useMemo(() => {
    if (!reelStatusData?.reelsMetaStatuses) return new Map();

    const statusMap = new Map();
    reelStatusData.reelsMetaStatuses.forEach((status) => {
      statusMap.set(status.id, {
        isLiked: status.is_liked,
        likesCount: status.likes_count,
        isPlaceFollowed: status.is_place_followed,
        isCreatorFollowed: status.is_creator_followed,
        isPlaceInCollection: status.is_place_in_collection,
        savedPlacesCount: status.saved_places_count,
      });
    });
    return statusMap;
  }, [reelStatusData]);

  const onViewableItemsChanged = useCallback(
    ({ viewableItems }: { viewableItems: ViewToken[] }) => {
      if (viewableItems.length > 0) {
        setVisibleIndex(viewableItems[0].index ?? 0);
        // Set hasScrolled to true after first viewable items change
        if (!hasScrolled) {
          setHasScrolled(true);
        }
      }
    },
    [setVisibleIndex, hasScrolled]
  );

  const viewabilityConfig = useMemo(
    () => ({
      itemVisiblePercentThreshold: 50,
    }),
    []
  );

  const renderItem: ListRenderItem<ReelType> = useCallback(
    ({ item, index }) => (
      <Reel
        reel={item}
        index={index}
        isVisible={index === visibleIndex && isFocused}
        reelStatus={reelStatusMap.get(item.objectID)}
        isStandalone={isStandalone}
        isMuted={isMuted}
        toggleMute={toggleMute}
        currentIndex={currentIndex}
      />
    ),
    [
      visibleIndex,
      isFocused,
      reelStatusMap,
      isStandalone,
      isMuted,
      toggleMute,
      currentIndex,
    ]
  );

  const keyExtractor = useCallback((item: ReelType) => item.objectID, []);

  const renderFooter = useCallback(() => {
    if (isLastPage) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator size='small' color={colors.base[100]} />
      </View>
    );
  }, [isLastPage]);

  const handleEndReached = useCallback(() => {
    // Only trigger loadMore if user has scrolled and we're not loading
    if (hasScrolled && !loading) {
      loadMore();
    }
  }, [hasScrolled, loading, loadMore]);

  return (
    <MemoizedFlashList
      ListEmptyComponent={
        !loading && items.length === 0 ? (
          <View style={styles.noResultsContainer}>
            <Text variant='h2' color={colors.base[100]}>
              {t('home.noResults')}
            </Text>
          </View>
        ) : null
      }
      data={items}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      extraData={{
        isMuted,
        visibleIndex,
        isFocused,
        reelStatusMap,
        loading,
        isStandalone,
        currentIndex,
      }}
      pagingEnabled
      showsVerticalScrollIndicator={false}
      snapToInterval={WINDOW_HEIGHT}
      snapToAlignment='start'
      decelerationRate={0.985}
      drawDistance={WINDOW_HEIGHT * 2}
      onViewableItemsChanged={onViewableItemsChanged}
      viewabilityConfig={viewabilityConfig}
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      estimatedItemSize={WINDOW_HEIGHT}
      estimatedListSize={{ height: WINDOW_HEIGHT, width: WINDOW_WIDTH }}
      disableAutoLayout={true}
      {...(initialScrollIndex !== undefined && {
        initialScrollIndex,
      })}
    />
  );
}

const styles = StyleSheet.create({
  noResultsContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: WINDOW_HEIGHT,
  },
  footer: {
    height: verticalScale(100),
    justifyContent: 'center',
    alignItems: 'center',
  },
});
