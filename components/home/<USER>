import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import {
  FlatList,
  View,
  ViewToken,
  ListRenderItem,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
} from 'react-native';
import Reel from '@/components/Reel';
import { Text } from '@/components/ui/Text';
import { colors } from '@/constants/Colors';
import { verticalScale } from '@/utils/scaling';
import type { Reel as ReelType } from '@/schemas/reel';
import { useReelStatusQuery } from '@/graphql/generated/graphql';
import { useTranslation } from 'react-i18next';

const { height: WINDOW_HEIGHT } = Dimensions.get('window');

// Memoize the FlatList for better performance
const MemoizedFlatList = React.memo(FlatList<ReelType>);

interface ReelListProps {
  items: ReelType[];
  visibleIndex: number;
  setVisibleIndex: (index: number) => void;
  isLastPage: boolean;
  loadMore: () => void;
  isFocused: boolean;
  initialScrollIndex?: number; // Optional prop for initial scroll position
  isStandalone?: boolean; // New prop to indicate if reels are shown outside of home (without bottom tabs)
  loading?: boolean; // Add loading prop to prevent empty state flash
  currentIndex?: string; // Add currentIndex prop for analytics
}

export function ReelList({
  items,
  visibleIndex,
  setVisibleIndex,
  isLastPage,
  loadMore,
  isFocused,
  initialScrollIndex,
  isStandalone = false, // Default to false (home screen mode)
  loading = false, // Default to false (not loading)
  currentIndex,
}: ReelListProps) {
  const { t } = useTranslation();
  // Add shared mute state
  const [isMuted, setIsMuted] = useState(false);
  // Add flag to prevent initial onEndReached trigger
  const [hasScrolled, setHasScrolled] = useState(false);

  // Toggle mute function
  const toggleMute = useCallback(() => {
    setIsMuted((prev) => !prev);
  }, []);

  // Extract all reel IDs for batch querying
  const reelIds = useMemo(() => items.map((item) => item.objectID), [items]);

  // Fetch reel statuses for all visible reels in one query
  const { data: reelStatusData } = useReelStatusQuery({
    variables: {
      ids: reelIds,
    },
    skip: reelIds.length === 0,
  });

  // Create a map of reel statuses for easy lookup
  const reelStatusMap = useMemo(() => {
    if (!reelStatusData?.reelsMetaStatuses) return new Map();

    const statusMap = new Map();
    reelStatusData.reelsMetaStatuses.forEach((status) => {
      statusMap.set(status.id, {
        isLiked: status.is_liked,
        likesCount: status.likes_count,
        isPlaceFollowed: status.is_place_followed,
        isCreatorFollowed: status.is_creator_followed,
        isPlaceInCollection: status.is_place_in_collection,
        savedPlacesCount: status.saved_places_count,
      });
    });
    return statusMap;
  }, [reelStatusData]);

  const onViewableItemsChanged = useCallback(
    ({ viewableItems }: { viewableItems: ViewToken[] }) => {
      if (viewableItems.length > 0) {
        setVisibleIndex(viewableItems[0].index ?? 0);
        // Set hasScrolled to true after first viewable items change
        if (!hasScrolled) {
          setHasScrolled(true);
        }
      }
    },
    [setVisibleIndex, hasScrolled]
  );

  const viewabilityConfig = useMemo(
    () => ({
      itemVisiblePercentThreshold: 50,
    }),
    []
  );

  const renderItem: ListRenderItem<ReelType> = useCallback(
    ({ item, index }) => (
      <Reel
        reel={item}
        index={index}
        isVisible={index === visibleIndex && isFocused}
        reelStatus={reelStatusMap.get(item.objectID)}
        isStandalone={isStandalone}
        isMuted={isMuted}
        toggleMute={toggleMute}
        currentIndex={currentIndex}
      />
    ),
    [
      visibleIndex,
      isFocused,
      reelStatusMap,
      isStandalone,
      isMuted,
      toggleMute,
      currentIndex,
    ]
  );

  const keyExtractor = useCallback((item: ReelType) => item.objectID, []);

  const renderFooter = useCallback(() => {
    if (isLastPage) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator size='small' color={colors.base[100]} />
      </View>
    );
  }, [isLastPage]);

  const handleEndReached = useCallback(() => {
    // Only trigger loadMore if user has scrolled and we're not loading
    if (hasScrolled && !loading) {
      loadMore();
    }
  }, [hasScrolled, loading, loadMore]);

  return (
    <MemoizedFlatList
      ListEmptyComponent={
        !loading && items.length === 0 ? (
          <View style={styles.noResultsContainer}>
            <Text variant='h2' color={colors.base[100]}>
              {t('home.noResults')}
            </Text>
          </View>
        ) : null
      }
      data={items}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      pagingEnabled
      showsVerticalScrollIndicator={false}
      snapToInterval={WINDOW_HEIGHT}
      snapToAlignment='start'
      decelerationRate='fast'
      onViewableItemsChanged={onViewableItemsChanged}
      viewabilityConfig={viewabilityConfig}
      //detach off-screen views
      removeClippedSubviews={true}
      // limit the number of items rendered per batch
      maxToRenderPerBatch={2}
      //keep only 3 items in memory
      windowSize={3}
      //render only one item initially
      initialNumToRender={1}
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      {...(initialScrollIndex !== undefined
        ? {
            initialScrollIndex,
            getItemLayout: (_, index) => ({
              length: WINDOW_HEIGHT,
              offset: WINDOW_HEIGHT * index,
              index,
            }),
          }
        : {})}
    />
  );
}

const styles = StyleSheet.create({
  noResultsContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: WINDOW_HEIGHT,
  },
  footer: {
    height: verticalScale(100),
    justifyContent: 'center',
    alignItems: 'center',
  },
});
