import { useEffect, useRef } from 'react';
import { usePathname, useSegments } from 'expo-router';
import { analyticsService } from '../services/analytics';

/**
 * ScreenTracker component automatically tracks screen navigation
 * using Expo Router's usePathname and useSegments hooks
 */
export function ScreenTracker() {
  const pathname = usePathname();
  const segments = useSegments();
  const previousPathnameRef = useRef<string>('');

  useEffect(() => {
    // Don't track on first render or if pathname hasn't changed
    if (!pathname || pathname === previousPathnameRef.current) {
      return;
    }

    // Update the previous pathname
    previousPathnameRef.current = pathname;

    try {
      // Generate a clean screen name from the pathname
      const screenName = getScreenNameFromPathname(pathname, segments);

      // Extract additional properties from the route
      const screenProperties = getScreenProperties(pathname, segments);

      // Track the screen view
      analyticsService.trackScreen(screenName, screenProperties);

      if (__DEV__) {
        console.log('[ScreenTracker] Screen tracked:', {
          screenName,
          pathname,
          segments,
          properties: screenProperties,
        });
      }
    } catch (error) {
      console.error('[ScreenTracker] Error tracking screen:', error);
    }
  }, [pathname, segments]);

  // This component doesn't render anything
  return null;
}

/**
 * Generate a clean screen name from pathname and segments
 */
function getScreenNameFromPathname(
  pathname: string,
  segments: string[]
): string {
  // Handle root path
  if (pathname === '/') {
    return 'Home';
  }

  // Use segments to build a more meaningful name
  if (segments.length > 0) {
    const cleanSegments = segments
      .filter(
        (segment) =>
          segment !== '(tabs)' && segment !== '(auth)' && segment !== '(modals)'
      )
      .map((segment) => {
        // Clean up dynamic segments [id] -> ID
        if (segment.startsWith('[') && segment.endsWith(']')) {
          return segment.slice(1, -1).toUpperCase();
        }
        // Capitalize first letter
        return segment.charAt(0).toUpperCase() + segment.slice(1);
      });

    if (cleanSegments.length > 0) {
      return cleanSegments.join(' ');
    }
  }

  // Fallback to cleaning pathname
  return (
    pathname
      .split('/')
      .filter((part) => part.length > 0)
      .map((part) => {
        // Handle dynamic segments
        if (part.startsWith('[') && part.endsWith(']')) {
          return part.slice(1, -1).toUpperCase();
        }
        // Capitalize first letter
        return part.charAt(0).toUpperCase() + part.slice(1);
      })
      .join(' ') || 'Unknown Screen'
  );
}

/**
 * Extract screen properties from pathname and segments
 */
function getScreenProperties(
  pathname: string,
  segments: string[]
): Record<string, any> {
  const properties: Record<string, any> = {
    pathname,
    segments: segments.join('/'),
  };

  // Extract IDs from dynamic routes
  const pathParts = pathname.split('/').filter((part) => part.length > 0);

  // Common ID patterns
  if (pathname.includes('/places/')) {
    const placeIndex = pathParts.indexOf('places');
    if (placeIndex !== -1 && placeIndex + 1 < pathParts.length) {
      properties.placeId = pathParts[placeIndex + 1];
    }
  }

  if (pathname.includes('/creators/')) {
    const creatorIndex = pathParts.indexOf('creators');
    if (creatorIndex !== -1 && creatorIndex + 1 < pathParts.length) {
      properties.creatorId = pathParts[creatorIndex + 1];
    }
  }

  if (pathname.includes('/reel/')) {
    const reelIndex = pathParts.indexOf('reel');
    if (reelIndex !== -1 && reelIndex + 1 < pathParts.length) {
      properties.reelId = pathParts[reelIndex + 1];
    }
  }

  if (pathname.includes('/collections/')) {
    const collectionIndex = pathParts.indexOf('collections');
    if (collectionIndex !== -1 && collectionIndex + 1 < pathParts.length) {
      properties.collectionId = pathParts[collectionIndex + 1];
    }
  }

  // Determine screen category
  if (segments.includes('(auth)')) {
    properties.category = 'Authentication';
  } else if (segments.includes('(tabs)')) {
    properties.category = 'Main App';
  } else if (segments.includes('(modals)')) {
    properties.category = 'Modal';
  } else {
    properties.category = 'Navigation';
  }

  // Determine if it's a tab screen
  const tabScreens = ['index', 'explore', 'profile', 'collections'];
  if (segments.some((segment) => tabScreens.includes(segment))) {
    properties.isTabScreen = true;
    properties.tabName = segments.find((segment) =>
      tabScreens.includes(segment)
    );
  }

  return properties;
}
