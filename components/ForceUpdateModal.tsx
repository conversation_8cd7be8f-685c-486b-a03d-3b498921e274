import React from 'react';
import { View, Modal, StyleSheet, Alert, Platform } from 'react-native';
import { Text } from '@/components/ui/Text';
import { Button } from '@/components/ui/Button';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import * as WebBrowser from 'expo-web-browser';
import { LinearGradient } from 'expo-linear-gradient';
const UPDATE_URL = Platform.select({
  ios: 'https://apps.apple.com/app/id6742346684',
  android: 'https://play.google.com/store/apps/details?id=com.conari.app.stage', // TODO: Update with actual Play Store URL when published
});

interface ForceUpdateModalProps {
  visible: boolean;
}

export function ForceUpdateModal({ visible }: ForceUpdateModalProps) {
  const { t } = useTranslation();

  const handleUpdatePress = async () => {
    try {
      await WebBrowser.openBrowserAsync(UPDATE_URL || '');
    } catch (error) {
      console.error('Failed to open App Store:', error);
      Alert.alert(
        'Error',
        'Unable to open the App Store. Please update the app manually.'
      );
    }
  };

  return (
    <Modal
      visible={visible}
      animationType='fade'
      statusBarTranslucent
      transparent={false}
    >
      <View style={styles.container}>
        <LinearGradient
          colors={[colors.primary[100], colors.primary[200]]}
          style={styles.background}
        >
          {/* Update Icon */}
          <View style={styles.iconContainer}>
            <Text style={styles.iconText}>📱</Text>
          </View>

          {/* Title */}
          <Text
            variant='h1'
            style={styles.title}
            align='center'
            color={colors.primary[950]}
          >
            {t('forceUpdate.title')}
          </Text>

          {/* Subtitle */}
          <Text
            variant='body'
            style={styles.subtitle}
            align='center'
            color={colors.darkGrey[700]}
          >
            {t('forceUpdate.subtitle')}
          </Text>

          {/* Update Button */}
          <Button
            title={t('forceUpdate.updateButton')}
            variant='primary'
            style={styles.updateButton}
            onPress={handleUpdatePress}
          />
        </LinearGradient>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: moderateScale(24),
    alignItems: 'center',
  },

  iconContainer: {
    width: moderateScale(120),
    height: moderateScale(120),
    borderRadius: moderateScale(100),
    marginBottom: moderateScale(32),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.base[100],
    shadowColor: colors.primary[600],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  iconText: {
    fontSize: moderateScale(70),
  },
  title: {
    marginBottom: moderateScale(16),
  },
  subtitle: {
    marginBottom: moderateScale(48),
    width: '95%',
  },
  updateButton: {
    width: '100%',
    maxWidth: moderateScale(280),
  },
});
