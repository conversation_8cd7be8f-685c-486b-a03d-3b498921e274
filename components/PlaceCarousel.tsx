import React from 'react';
import { View, StyleSheet, Dimensions, Pressable } from 'react-native';
import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { moderateScale, verticalScale } from '../utils/scaling';
import { colors } from '../constants/Colors';
import Carousel, {
  ICarouselInstance,
  Pagination,
} from 'react-native-reanimated-carousel';
import { useSharedValue } from 'react-native-reanimated';

const { width: WINDOW_WIDTH } = Dimensions.get('window');

interface PlaceCarouselProps {
  images: string[];
  onImagePress?: (index: number) => void;
}

export function PlaceCarousel({ images, onImagePress }: PlaceCarouselProps) {
  const progress = useSharedValue<number>(0);
  const ref = React.useRef<ICarouselInstance>(null);

  const onPressPagination = (index: number) => {
    ref.current?.scrollTo({
      count: index - progress.value,
      animated: true,
    });
  };

  return (
    <View style={styles.carouselContainer}>
      <Carousel
        ref={ref}
        loop
        width={WINDOW_WIDTH}
        height={verticalScale(205)}
        data={images}
        onProgressChange={progress}
        renderItem={({ item, index }) => (
          <Pressable
            style={styles.carouselItemContainer}
            onPress={() => onImagePress?.(index)}
          >
            <Image
              source={{ uri: item }}
              style={styles.carouselImage}
              contentFit='cover'
              transition={500}
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.carouselGradient}
            />
          </Pressable>
        )}
      />
      <Pagination.Custom
        progress={progress}
        data={images}
        size={5}
        dotStyle={styles.paginationDot}
        activeDotStyle={styles.paginationActiveDot}
        containerStyle={styles.paginationContainer}
        onPress={onPressPagination}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  carouselContainer: {
    height: verticalScale(205),
    width: '100%',
  },
  carouselItemContainer: {
    width: '100%',
    height: '100%',
  },
  carouselImage: {
    width: WINDOW_WIDTH,
    height: verticalScale(205),
  },
  carouselGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: verticalScale(40),
  },
  paginationContainer: {
    position: 'absolute',
    bottom: verticalScale(6),
    gap: moderateScale(5),
    height: verticalScale(10),
  },
  paginationDot: {
    borderRadius: moderateScale(10),
    backgroundColor: colors.darkGrey[500],
  },
  paginationActiveDot: {
    width: moderateScale(24),
    height: verticalScale(5),
    overflow: 'hidden',
    backgroundColor: colors.base[100],
  },
});
