import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';
import { PlatformPressable } from '@react-navigation/elements';
import * as Haptics from 'expo-haptics';
import { Platform, StyleSheet, View } from 'react-native';

export function HapticTab(props: BottomTabBarButtonProps) {
  const { children, accessibilityState } = props;
  const isSelected = accessibilityState?.selected;

  return (
    <PlatformPressable
      {...props}
      onPressIn={(ev) => {
        if (Platform.OS === 'ios') {
          // Add a soft haptic feedback when pressing down on the tabs.
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
        props.onPressIn?.(ev);
      }}
    >
      <View
        style={[
          styles.content,
          isSelected && { backgroundColor: colors.primary[950] },
        ]}
      >
        {children}
      </View>
    </PlatformPressable>
  );
}

const styles = StyleSheet.create({
  content: {
    height: moderateScale(62),
    width: moderateScale(62),
    borderRadius: moderateScale(30),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
});
