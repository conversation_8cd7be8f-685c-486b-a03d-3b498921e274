import { useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { useAuthStore } from '@/store/auth';
import {
  useAcceptInvitationMutation,
  LoginStatusCode,
} from '@/graphql/generated/graphql';
import { router } from 'expo-router';

export function InviteHandler() {
  const {
    isAuthenticated,
    user,
    getPendingInviteToken,
    clearPendingInviteToken,
    setUser,
    setStatusCode,
    pendingInviteToken,
  } = useAuthStore();
  const [acceptInvitationMutation] = useAcceptInvitationMutation();
  const [isProcessingInvite, setIsProcessingInvite] = useState(false);

  // Handle invite tokens after authentication
  useEffect(() => {
    console.log('[InviteHandler] Effect triggered:', {
      isAuthenticated,
      hasUser: !!user,
      isProcessingInvite,
      pendingInviteToken,
      userStatus: user?.status,
    });

    // Only process invites when user is authenticated, has user data, not already processing,
    // and user status is not Success (users with Success status don't need invites)
    if (
      !isAuthenticated ||
      !user ||
      isProcessingInvite ||
      user.status === LoginStatusCode.Success
    ) {
      if (user?.status === LoginStatusCode.Success) {
        console.log(
          '[InviteHandler] User already has Success status, clearing pending invite token'
        );
        clearPendingInviteToken();
      }
      return;
    }

    const inviteToken = getPendingInviteToken();
    console.log(
      '[InviteHandler] Checking for pending invite token:',
      inviteToken
    );

    if (!inviteToken) return;

    console.log('[InviteHandler] Found pending invite token:', inviteToken);

    // Prevent multiple simultaneous invite processing
    setIsProcessingInvite(true);

    // Process the invite
    acceptInvitationMutation({ variables: { token: inviteToken } })
      .then((result) => {
        console.log('[InviteHandler] Invitation accepted:', result);
        clearPendingInviteToken();

        if (result.data?.acceptInvitation?.user) {
          const updatedUser = result.data.acceptInvitation.user;
          setUser(updatedUser);

          // Handle different status codes
          switch (updatedUser.status) {
            case LoginStatusCode.Success:
              setStatusCode(LoginStatusCode.Success);
              Alert.alert(
                'Welcome!',
                'Your invitation has been accepted and you now have access to Conari!',
                [{ text: 'Continue', onPress: () => router.replace('/(tabs)') }]
              );
              break;

            case LoginStatusCode.AwaitingApproval:
              setStatusCode(LoginStatusCode.AwaitingApproval);
              Alert.alert(
                'Invitation Accepted',
                'Your invitation has been accepted! You will be notified once your access is approved.',
                [{ text: 'OK' }]
              );
              break;

            default:
              Alert.alert(
                'Invitation Accepted',
                'Your invitation has been processed.',
                [{ text: 'OK' }]
              );
          }
        }
      })
      .catch((error) => {
        console.error('[InviteHandler] Error accepting invitation:', error);
        clearPendingInviteToken();

        Alert.alert(
          'Invitation Error',
          error.message ||
            'There was an error processing your invitation. Please try again.',
          [{ text: 'OK' }]
        );
      })
      .finally(() => {
        setIsProcessingInvite(false);
      });
  }, [
    isAuthenticated,
    user,
    acceptInvitationMutation,
    isProcessingInvite,
    pendingInviteToken,
  ]);

  // This component doesn't render anything, it just handles invite logic
  return null;
}
