import Svg, { Path } from 'react-native-svg';
import { colors } from '@/constants/Colors';

interface Props {
  color?: string;
}

export function Deal3({ color = colors.base[100] }: Props) {
  return (
    <Svg width='19' height='19' viewBox='0 0 19 19' fill='none'>
      <Path
        d='M9.5 2C5.36 2 2 5.36 2 9.5C2 13.64 5.36 17 9.5 17C13.64 17 17 13.64 17 9.5C17 5.36 13.64 2 9.5 2ZM7.0475 6.245C7.655 6.245 8.1575 6.74 8.1575 7.355C8.1575 7.9625 7.6625 8.465 7.0475 8.465C6.44 8.465 5.9375 7.97 5.9375 7.355C5.9375 6.74 6.4325 6.245 7.0475 6.245ZM7.1375 12.35C7.025 12.4625 6.8825 12.515 6.74 12.515C6.5975 12.515 6.455 12.4625 6.3425 12.35C6.125 12.1325 6.125 11.7725 6.3425 11.555L11.255 6.6425C11.4725 6.425 11.8325 6.425 12.05 6.6425C12.2675 6.86 12.2675 7.22 12.05 7.4375L7.1375 12.35ZM11.9525 12.755C11.345 12.755 10.8425 12.26 10.8425 11.645C10.8425 11.0375 11.3375 10.535 11.9525 10.535C12.56 10.535 13.0625 11.03 13.0625 11.645C13.0625 12.26 12.5675 12.755 11.9525 12.755Z'
        fill={color}
      />
    </Svg>
  );
}
