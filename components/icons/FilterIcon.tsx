import Svg, { Path } from 'react-native-svg';

interface Props {
  color?: string;
}

export function FilterIcon({ color = 'transparent' }: Props) {
  return (
    <Svg width='20' height='20' viewBox='0 0 20 20' fill={'transparent'}>
      <Path
        d='M4.50016 1.74951H15.5002C16.4168 1.74951 17.1668 2.49951 17.1668 3.41618V5.24951C17.1668 5.91618 16.7502 6.74951 16.3335 7.16618L12.7502 10.3328C12.2502 10.7495 11.9168 11.5828 11.9168 12.2495V15.8328C11.9168 16.3328 11.5835 16.9995 11.1668 17.2495L10.0002 17.9995C8.91683 18.6662 7.41683 17.9162 7.41683 16.5828V12.1662C7.41683 11.5828 7.0835 10.8328 6.75016 10.4162L3.5835 7.08285C3.16683 6.66618 2.8335 5.91618 2.8335 5.41618V3.49951C2.8335 2.49951 3.5835 1.74951 4.50016 1.74951Z'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
        stroke={color}
      />
      <Path
        d='M9.10833 1.74951L5 8.33285'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
        stroke={color}
      />
    </Svg>
  );
}
