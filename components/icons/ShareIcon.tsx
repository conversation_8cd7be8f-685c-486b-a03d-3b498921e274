import Svg, { Path } from 'react-native-svg';
import { colors } from '@/constants/Colors';

interface Props {
  color?: string;
}

export function ShareIcon({ color = colors.base[100] }: Props) {
  return (
    <Svg width='22' height='19' viewBox='0 0 22 19'>
      <Path
        d='M12.1669 0.166016V4.83268C4.49605 6.03202 1.64355 12.752 0.50022 18.8327C0.457053 19.073 6.78155 11.877 12.1669 11.8327V16.4993L21.5002 8.33268L12.1669 0.166016Z'
        fill={color}
      />
    </Svg>
  );
}
