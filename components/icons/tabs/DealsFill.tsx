import { Path } from 'react-native-svg';
import { TabIcon } from './TabIcon';
import { colors } from '@/constants/Colors';

interface Props {
  color?: string;
}

export function DealsFill({ color = colors.base[100] }: Props) {
  return (
    <TabIcon>
      <Path
        d='M16.6496 3.85999H9.90963V6.87999C9.90963 7.26999 9.58963 7.57999 9.20963 7.57999C8.82963 7.57999 8.50963 7.26999 8.50963 6.87999V3.85999H7.34963C3.39963 3.85999 2.09963 5.03999 2.00963 8.72999C1.99963 8.90999 2.07963 9.09999 2.20963 9.22999C2.33963 9.36999 2.50963 9.43999 2.70963 9.43999C4.10963 9.43999 5.25963 10.6 5.25963 12C5.25963 13.4 4.10963 14.56 2.70963 14.56C2.51963 14.56 2.33963 14.63 2.20963 14.77C2.07963 14.9 1.99963 15.09 2.00963 15.27C2.09963 18.96 3.39963 20.14 7.34963 20.14H8.50963V17.12C8.50963 16.73 8.82963 16.42 9.20963 16.42C9.58963 16.42 9.90963 16.73 9.90963 17.12V20.14H16.6496C20.7496 20.14 21.9996 18.89 21.9996 14.79V9.20999C21.9996 5.10999 20.7496 3.85999 16.6496 3.85999ZM18.4696 11.9L17.5396 12.8C17.4996 12.83 17.4896 12.89 17.4996 12.94L17.7196 14.21C17.7596 14.44 17.6696 14.68 17.4696 14.82C17.2796 14.96 17.0296 14.98 16.8196 14.87L15.6696 14.27C15.6296 14.25 15.5696 14.25 15.5296 14.27L14.3796 14.87C14.2896 14.92 14.1896 14.94 14.0896 14.94C13.9596 14.94 13.8396 14.9 13.7296 14.82C13.5396 14.68 13.4396 14.45 13.4796 14.21L13.6996 12.94C13.7096 12.89 13.6896 12.84 13.6596 12.8L12.7296 11.9C12.5596 11.74 12.4996 11.49 12.5696 11.27C12.6396 11.04 12.8296 10.88 13.0696 10.85L14.3496 10.66C14.3996 10.65 14.4396 10.62 14.4696 10.58L15.0396 9.41999C15.1496 9.20999 15.3596 9.07999 15.5996 9.07999C15.8396 9.07999 16.0496 9.20999 16.1496 9.41999L16.7196 10.58C16.7396 10.63 16.7796 10.66 16.8296 10.66L18.1096 10.85C18.3496 10.88 18.5396 11.05 18.6096 11.27C18.6996 11.49 18.6396 11.73 18.4696 11.9Z'
        fill={color}
      />
    </TabIcon>
  );
}
