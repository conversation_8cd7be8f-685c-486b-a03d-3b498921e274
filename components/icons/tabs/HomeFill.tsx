import { Path } from 'react-native-svg';
import { TabIcon } from './TabIcon';
import { colors } from '@/constants/Colors';

interface Props {
  color?: string;
}

export function HomeFill({ color = colors.base[100] }: Props) {
  return (
    <TabIcon>
      <Path
        d='M9.02 2.84004L3.63 7.04004C2.73 7.74004 2 9.23004 2 10.36V17.77C2 20.09 3.89 21.99 6.21 21.99H17.79C20.11 21.99 22 20.09 22 17.78V10.5C22 9.29004 21.19 7.74004 20.2 7.05004L14.02 2.72004C12.62 1.74004 10.37 1.79004 9.02 2.84004Z'
        fill={color}
      />
      <Path
        d='M12 17.99V14.99'
        stroke={color}
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </TabIcon>
  );
}
