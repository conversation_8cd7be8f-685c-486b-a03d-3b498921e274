import { Path } from 'react-native-svg';
import { TabIcon } from './TabIcon';
import { colors } from '@/constants/Colors';
interface Props {
  color?: string;
}

export function DiscoverFill({ color = colors.base[100] }: Props) {
  return (
    <TabIcon>
      <Path
        d='M7.63004 3.57005C7.80942 3.47221 8.00004 3.62274 8.00004 3.82707V17.3829C8.00004 17.6061 7.84762 17.7949 7.65024 17.8992C7.64347 17.9028 7.63673 17.9064 7.63004 17.9101L5.28004 19.2501C3.64004 20.1901 2.29004 19.4101 2.29004 17.5101V7.78005C2.29004 7.15005 2.74004 6.37005 3.30004 6.05005L7.63004 3.57005V3.57005Z'
        fill={color}
      />
      <Path
        d='M14.7219 6.10229C14.8922 6.18664 15 6.36028 15 6.55035V19.7035C15 20.072 14.615 20.3139 14.283 20.1539L10.033 18.1063C9.85998 18.023 9.75 17.8479 9.75 17.6559V4.44559C9.75 4.07473 10.1396 3.83294 10.4719 3.99753L14.7219 6.10229Z'
        fill={color}
      />
      <Path
        d='M22 6.48994V16.2199C22 16.8499 21.55 17.6299 20.99 17.9499L17.4986 19.9509C17.1653 20.1419 16.75 19.9013 16.75 19.5171V6.33026C16.75 6.15075 16.8462 5.98501 17.0021 5.89602L19.01 4.74994C20.65 3.80994 22 4.58994 22 6.48994Z'
        fill={color}
      />
    </TabIcon>
  );
}
