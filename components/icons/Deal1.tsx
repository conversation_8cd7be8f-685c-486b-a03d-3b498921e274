import Svg, { Path } from 'react-native-svg';
import { colors } from '@/constants/Colors';

interface Props {
  color?: string;
}

export function Deal1({ color = colors.base[100] }: Props) {
  return (
    <Svg width='16' height='17' viewBox='0 0 16 17' fill='none'>
      <Path
        d='M13.3337 8.50065V12.5007C13.3337 13.974 12.1403 15.1673 10.667 15.1673H5.33366C3.86033 15.1673 2.66699 13.974 2.66699 12.5007V8.50065C2.66699 8.13398 2.96699 7.83398 3.33366 7.83398H4.64699C5.01366 7.83398 5.31366 8.13398 5.31366 8.50065V10.594C5.31366 11.0873 5.58699 11.5407 6.02033 11.774C6.21366 11.8807 6.42699 11.934 6.64699 11.934C6.90033 11.934 7.15366 11.8607 7.37366 11.714L8.00699 11.3007L8.59366 11.694C9.00033 11.9673 9.52033 12.0007 9.95366 11.7673C10.3937 11.534 10.667 11.0873 10.667 10.5873V8.50065C10.667 8.13398 10.967 7.83398 11.3337 7.83398H12.667C13.0337 7.83398 13.3337 8.13398 13.3337 8.50065Z'
        fill={color}
      />
      <Path
        d='M14.3337 5.16732V5.83398C14.3337 6.56732 13.9803 7.16732 13.0003 7.16732H3.00033C1.98033 7.16732 1.66699 6.56732 1.66699 5.83398V5.16732C1.66699 4.43398 1.98033 3.83398 3.00033 3.83398H13.0003C13.9803 3.83398 14.3337 4.43398 14.3337 5.16732Z'
        fill={color}
      />
      <Path
        d='M7.76018 3.83427H4.08018C3.85352 3.5876 3.86018 3.2076 4.10018 2.9676L5.04685 2.02094C5.29352 1.77427 5.70018 1.77427 5.94685 2.02094L7.76018 3.83427Z'
        fill={color}
      />
      <Path
        d='M11.9134 3.83427H8.2334L10.0467 2.02094C10.2934 1.77427 10.7001 1.77427 10.9467 2.02094L11.8934 2.9676C12.1334 3.2076 12.1401 3.5876 11.9134 3.83427Z'
        fill={color}
      />
      <Path
        d='M9.31329 7.83398C9.67996 7.83398 9.97996 8.13398 9.97996 8.50065V10.5873C9.97996 11.1207 9.38663 11.4407 8.94663 11.1407L8.34663 10.7407C8.12663 10.594 7.83996 10.594 7.61329 10.7407L6.98663 11.154C6.54663 11.4473 5.95996 11.1273 5.95996 10.6007V8.50065C5.95996 8.13398 6.25996 7.83398 6.62663 7.83398H9.31329Z'
        fill={color}
      />
    </Svg>
  );
}
