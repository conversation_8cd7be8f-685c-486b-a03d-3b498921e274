import Svg, { Path } from 'react-native-svg';
import { colors } from '@/constants/Colors';

interface Props {
  color?: string;
  outline?: boolean;
}

export function SaveIcon({ color = colors.base[100], outline = false }: Props) {
  return (
    <Svg width='22' height='25' viewBox='0 0 22 25' fill='none' color={color}>
      <Path
        d='M16.623 0.833984H5.37638C2.89138 0.833984 0.873047 2.86398 0.873047 5.33732V21.7757C0.873047 23.8757 2.37805 24.7623 4.22138 23.7473L9.91471 20.5857C10.5214 20.2473 11.5014 20.2473 12.0964 20.5857L17.7897 23.7473C19.633 24.774 21.138 23.8873 21.138 21.7757V5.33732C21.1264 2.86398 19.108 0.833984 16.623 0.833984ZM13.9164 11.8007H11.8747V13.9123C11.8747 14.3907 11.478 14.7873 10.9997 14.7873C10.5214 14.7873 10.1247 14.3907 10.1247 13.9123V11.8007H8.08305C7.60471 11.8007 7.20805 11.404 7.20805 10.9257C7.20805 10.4473 7.60471 10.0507 8.08305 10.0507H10.1247V8.07898C10.1247 7.60065 10.5214 7.20398 10.9997 7.20398C11.478 7.20398 11.8747 7.60065 11.8747 8.07898V10.0507H13.9164C14.3947 10.0507 14.7914 10.4473 14.7914 10.9257C14.7914 11.404 14.3947 11.8007 13.9164 11.8007Z'
        fill={outline ? 'white' : color}
        stroke={outline ? color : 'white'}
        strokeWidth={outline ? 1 : 0}
      />
    </Svg>
  );
}
