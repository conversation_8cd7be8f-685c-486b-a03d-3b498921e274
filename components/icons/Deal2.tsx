import Svg, { Path } from 'react-native-svg';
import { colors } from '@/constants/Colors';

interface Props {
  color?: string;
}

export function Deal2({ color = colors.base[100] }: Props) {
  return (
    <Svg width='18' height='19' viewBox='0 0 18 19' fill='none'>
      <Path
        d='M17.9571 13.796C17.6596 16.1939 15.6944 18.1591 13.2966 18.4566C11.8452 18.6368 10.493 18.2402 9.43833 17.465C8.83435 17.0232 8.97859 16.0857 9.69975 15.8694C12.4131 15.0491 14.5496 12.9036 15.3789 10.1902C15.5953 9.47807 16.5328 9.33384 16.9745 9.9288C17.7407 10.9925 18.1374 12.3447 17.9571 13.796Z'
        fill='white'
      />
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.21164 14.9233C11.1945 14.9233 14.4233 11.6945 14.4233 7.71164C14.4233 3.72876 11.1945 0.5 7.21164 0.5C3.22876 0.5 0 3.72876 0 7.71164C0 11.6945 3.22876 14.9233 7.21164 14.9233ZM5.99082 6.99668C6.09107 6.97148 6.177 6.90091 6.24861 6.78498C6.28203 6.73458 6.31067 6.68165 6.33454 6.62621C6.36319 6.56572 6.39183 6.50776 6.42048 6.45231C6.51596 6.30614 6.63531 6.19525 6.77853 6.11964C6.92652 6.04403 7.08406 6.00623 7.25115 6.00623C7.4803 6.00623 7.66888 6.0768 7.81687 6.21793C7.96487 6.35402 8.03886 6.5506 8.03886 6.80766C8.03886 6.92359 8.02215 7.03448 7.98874 7.14033C7.95532 7.24114 7.9028 7.34447 7.83119 7.45032C7.75958 7.55617 7.6641 7.66958 7.54475 7.79055C7.43018 7.91152 7.28934 8.0451 7.12225 8.19127C6.99336 8.3072 6.8573 8.42313 6.71408 8.53906C6.57086 8.65499 6.43718 8.77092 6.31306 8.88685C6.22713 8.96246 6.13404 9.04059 6.03378 9.12123C5.93353 9.20188 5.83805 9.29009 5.74734 9.38586C5.66141 9.47659 5.5898 9.5774 5.53251 9.68829C5.47522 9.79414 5.44658 9.91007 5.44658 10.0361C5.44658 10.1117 5.44419 10.1848 5.43942 10.2553C5.43942 10.3259 5.48239 10.3738 5.56832 10.399C5.61128 10.4141 5.65902 10.4192 5.71154 10.4141C5.76883 10.4091 5.82134 10.4066 5.86908 10.4066H8.43988C8.52104 10.4066 8.60697 10.4091 8.69767 10.4141C8.78838 10.4141 8.86476 10.3763 8.92683 10.3007C8.96024 10.2654 8.98411 10.2175 8.99844 10.157C9.01276 10.0915 9.01753 10.026 9.01276 9.96047C9.01276 9.8899 9.00321 9.82438 8.98411 9.76389C8.96502 9.70341 8.93637 9.65552 8.89818 9.62024C8.85999 9.58496 8.81464 9.56227 8.76212 9.55219C8.70961 9.53707 8.65471 9.52951 8.59742 9.52951C8.54013 9.52447 8.48284 9.52447 8.42556 9.52951C8.36827 9.52951 8.31575 9.52699 8.26801 9.52195C8.14866 9.51187 8.02931 9.50935 7.90996 9.51439C7.79061 9.51943 7.66888 9.52195 7.54475 9.52195C7.43972 9.52195 7.35618 9.52447 7.29412 9.52951C7.23206 9.53455 7.14851 9.53203 7.04348 9.52195C6.95755 9.51691 6.91936 9.49675 6.92891 9.46147C6.93846 9.42618 6.96949 9.38838 7.022 9.34805C7.07451 9.30773 7.13419 9.26741 7.20103 9.22708C7.26786 9.18676 7.31321 9.15652 7.33708 9.13636L7.56624 8.93978C7.64262 8.87425 7.71662 8.80873 7.78823 8.7432C8.00306 8.5567 8.18208 8.38533 8.3253 8.22907C8.4733 8.07282 8.59026 7.91908 8.67619 7.76787C8.7669 7.61666 8.82896 7.4604 8.86238 7.29911C8.90057 7.13277 8.91967 6.94628 8.91967 6.73962C8.91967 6.30614 8.81464 5.95331 8.60458 5.68112C8.39453 5.40894 8.09854 5.20984 7.71662 5.08383C7.57817 5.03846 7.42779 5.01326 7.26547 5.00822C7.10793 5.00318 6.948 5.0183 6.78569 5.05358C6.62815 5.08887 6.47538 5.14179 6.32738 5.21236C6.18416 5.28292 6.05526 5.36609 5.94069 5.46186C5.87863 5.51731 5.81657 5.58535 5.7545 5.666C5.69722 5.74161 5.64231 5.82477 5.5898 5.9155C5.54206 6.00119 5.50148 6.09192 5.46806 6.18769C5.43465 6.28346 5.41555 6.37166 5.41078 6.45231C5.40123 6.57328 5.4251 6.68165 5.48239 6.77742C5.54445 6.86815 5.62083 6.93367 5.71154 6.974C5.80224 7.01432 5.89534 7.02188 5.99082 6.99668Z'
        fill='white'
      />
    </Svg>
  );
}
