import Svg, { Path } from 'react-native-svg';
import { colors } from '@/constants/Colors';

interface Props {
  color?: string;
}

export function Deal4({ color = colors.base[100] }: Props) {
  return (
    <Svg width='14' height='15' viewBox='0 0 14 15' fill='none'>
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1.61023 3.92195C1.48443 4.35295 1.46949 4.80874 1.5668 5.24706C1.18677 5.48756 0.873738 5.82028 0.656828 6.21425C0.439917 6.60822 0.326172 7.05065 0.326172 7.50039C0.326172 7.95013 0.439917 8.39256 0.656828 8.78653C0.873738 9.18051 1.18677 9.51322 1.5668 9.75372C1.46949 10.192 1.48443 10.6478 1.61023 11.0788C1.73604 11.5098 1.96863 11.9021 2.28644 12.2192C2.60425 12.5364 2.997 12.7682 3.42827 12.8931C3.85953 13.018 4.31535 13.0319 4.75346 12.9337C4.99429 13.3123 5.32674 13.624 5.72004 13.8399C6.11335 14.0559 6.55478 14.1691 7.00346 14.1691C7.45215 14.1691 7.89358 14.0559 8.28688 13.8399C8.68018 13.624 9.01264 13.3123 9.25346 12.9337C9.69178 13.031 10.1476 13.0161 10.5786 12.8903C11.0096 12.7645 11.4018 12.5319 11.719 12.2141C12.0361 11.8963 12.2679 11.5035 12.3928 11.0723C12.5177 10.641 12.5317 10.1852 12.4335 9.74706C12.8106 9.50592 13.121 9.17372 13.3359 8.78109C13.5509 8.38846 13.6636 7.94803 13.6636 7.50039C13.6636 7.05276 13.5509 6.61232 13.3359 6.21969C13.121 5.82706 12.8106 5.49486 12.4335 5.25372C12.5321 4.81495 12.5182 4.35834 12.393 3.92639C12.2678 3.49444 12.0354 3.10117 11.7174 2.78316C11.3994 2.46515 11.0061 2.23274 10.5741 2.10753C10.1422 1.98233 9.68557 1.96841 9.2468 2.06706C9.00566 1.68992 8.67346 1.37956 8.28083 1.16458C7.8882 0.949599 7.44776 0.836914 7.00013 0.836914C6.55249 0.836914 6.11206 0.949599 5.71943 1.16458C5.3268 1.37956 4.9946 1.68992 4.75346 2.06706C4.31535 1.96883 3.85953 1.98282 3.42827 2.10772C2.997 2.23262 2.60425 2.46439 2.28644 2.78154C1.96863 3.09869 1.73604 3.49095 1.61023 3.92195ZM6.77551 5.78571V10.5H8V4.5H6.32653L6 5.78571H6.77551Z'
        fill={color}
      />
    </Svg>
  );
}
