import Svg, { Path } from 'react-native-svg';
import { colors } from '@/constants/Colors';

interface Props {
  color?: string;
}

export function LikeIcon({ color = colors.base[100] }: Props) {
  return (
    <Svg width='24' height='21' viewBox='0 0 24 21' fill={color}>
      <Path
        d='M12.7235 20.6617C12.3268 20.8017 11.6735 20.8017 11.2768 20.6617C7.8935 19.5067 0.333496 14.6883 0.333496 6.52167C0.333496 2.91667 3.2385 0 6.82016 0C8.9435 0 10.8218 1.02667 12.0002 2.61333C13.1785 1.02667 15.0685 0 17.1802 0C20.7618 0 23.6668 2.91667 23.6668 6.52167C23.6668 14.6883 16.1068 19.5067 12.7235 20.6617Z'
        fill={color}
      />
    </Svg>
  );
}
