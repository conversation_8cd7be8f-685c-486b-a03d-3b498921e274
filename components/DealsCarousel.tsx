import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { StyleSheet, View, Dimensions } from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { Text } from './ui/Text';
import { useState } from 'react';
import Animated, {
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { TouchableOpacity } from 'react-native';
import { dealBackgrounds } from './ui/types/deal';
import { DealIcons } from './ui/types/dealIcons';
import { DealType } from '@/graphql/generated/graphql';
import type { z } from 'zod';
import { dealSchema } from '@/schemas/reel';

const { width: WINDOW_WIDTH } = Dimensions.get('window');

type Deal = z.infer<typeof dealSchema>;

interface DealsCarouselProps {
  deals: Deal[];
  onDealsPress?: () => void;
}

export function DealsCarousel({ deals, onDealsPress }: DealsCarouselProps) {
  const firstDealColor = dealBackgrounds[deals[0].deal_type as DealType];
  const [currentBgColor, setCurrentBgColor] = useState(firstDealColor);
  const [currentIndex, setCurrentIndex] = useState(0);

  const containerStyle = useAnimatedStyle(() => ({
    backgroundColor: withTiming(currentBgColor, { duration: 800 }),
  }));

  const renderItem = ({ item }: { item: Deal }) => {
    const IconComponent = DealIcons[item.deal_type];
    return (
      <View style={styles.dealContent}>
        <IconComponent color={colors.base[100]} />
        <Text
          variant='caption'
          color={colors.base[100]}
          weight='bold'
          numberOfLines={1}
        >
          {item.title}
        </Text>
      </View>
    );
  };

  const renderDots = () => (
    <View style={styles.dotsContainer}>
      {deals.map((_, index) => (
        <View
          key={index}
          style={[styles.dot, currentIndex === index && styles.activeDot]}
        />
      ))}
    </View>
  );

  if (!deals.length) return null;

  // If there's only one deal, render it without carousel
  if (deals.length === 1) {
    return (
      <TouchableOpacity activeOpacity={1} onPress={onDealsPress}>
        <Animated.View style={[styles.container, containerStyle]}>
          {renderItem({ item: deals[0] })}
        </Animated.View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity activeOpacity={1} onPress={onDealsPress}>
      <Animated.View style={[styles.container, containerStyle]}>
        <Carousel
          loop
          width={WINDOW_WIDTH * 0.8}
          data={deals}
          renderItem={renderItem}
          autoPlay={true}
          autoPlayInterval={3000}
          scrollAnimationDuration={500}
          onProgressChange={(_, absoluteProgress) => {
            const index = Math.round(absoluteProgress) % deals.length;
            setCurrentBgColor(dealBackgrounds[deals[index].deal_type]);
            setCurrentIndex(index);
          }}
          defaultIndex={0}
        />
        {/* {renderDots()} */}
      </Animated.View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    height: verticalScale(41),
    marginBottom: moderateScale(12),
    borderRadius: moderateScale(10),
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  dealContent: {
    height: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: moderateScale(8),
  },
  dotsContainer: {
    flexDirection: 'row',
    gap: moderateScale(3),
    top: moderateScale(-10),
  },
  dot: {
    width: moderateScale(4),
    height: moderateScale(4),
    borderRadius: moderateScale(2),
    backgroundColor: colors.base[100],
    opacity: 0.5,
  },
  activeDot: {
    opacity: 1,
    width: moderateScale(12),
  },
});
