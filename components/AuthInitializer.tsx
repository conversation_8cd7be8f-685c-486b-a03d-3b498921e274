import { useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { colors } from '@/constants/Colors';
import { useForceUpdate } from '@/hooks/useForceUpdate';
import { ForceUpdateModal } from './ForceUpdateModal';

interface AuthInitializerProps {
  children: React.ReactNode;
  onReady?: () => void;
}

export function AuthInitializer({ children, onReady }: AuthInitializerProps) {
  const { initAuth, isLoading: authLoading } = useAuthStore();
  const { isLoading: updateLoading, isUpdateRequired } = useForceUpdate();

  useEffect(() => {
    initAuth();
  }, []);

  // Call onReady when AuthInitializer is done loading and no update is required
  useEffect(() => {
    if (!authLoading && !updateLoading && !isUpdateRequired && onReady) {
      onReady();
    }
  }, [authLoading, updateLoading, isUpdateRequired, onReady]);

  // Show loading while checking for updates or initializing auth
  if (authLoading || updateLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size='large' color={colors.primary[950]} />
      </View>
    );
  }

  // Show force update modal if update is required
  if (isUpdateRequired) {
    return <ForceUpdateModal visible={true} />;
  }

  return children;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
