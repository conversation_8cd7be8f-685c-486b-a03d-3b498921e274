import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Text } from './ui/Text';
import { Image } from 'expo-image';
import { Tag } from './ui/Tag';
import { useRouter } from 'expo-router';
import { FEATURED_MEAL_TIMES } from '@/constants/MealTimes';
import { SaveIcon } from '@/components/icons/SaveIcon';
import {
  CollectableType,
  useRemoveItemFromCollectionMutation,
} from '@/graphql/generated/graphql';

// Import deal types and constants from shared locations
import { DealType, PlaceWithDealsFragment } from '@/graphql/generated/graphql';
import { dealBackgrounds } from './ui/types/deal';
import { DealIcons } from './ui/types/dealIcons';

type CollectionPlaceProps = {
  place: PlaceWithDealsFragment;
  collectionId: string;
};

export const CollectionPlace = ({
  place,
  collectionId,
}: CollectionPlaceProps) => {
  const router = useRouter();

  const [removeItemFromCollection, { loading }] =
    useRemoveItemFromCollectionMutation({
      refetchQueries: ['Collection'],
    });

  const removePlaceFromCollection = () => {
    removeItemFromCollection({
      variables: {
        input: {
          collectable_id: place.id,
          collectable_type: CollectableType.PartnerLocation,
          collection_ids: [collectionId],
        },
      },
    });
  };

  if (!place) return null;

  // Navigate to place details
  const navigateToPlace = () => {
    router.push(`/places/${place.id}`);
  };

  // Create tags array following the same order as place screen
  const tags: { title: string }[] = [];

  // Add tags in the order specified in place screen
  // Cuisine types
  if (place.cuisine_types?.length) {
    tags.push(...place.cuisine_types.filter((tag) => tag !== null));
  }

  // cravings
  if (place.cravings?.length) {
    tags.push(...place.cravings.filter((tag) => tag !== null));
  }

  // Specialities
  if (place.specialities?.length) {
    tags.push(...place.specialities.filter((tag) => tag !== null));
  }

  // Dietary
  if (place.dietary?.length) {
    tags.push(...place.dietary.filter((tag) => tag !== null));
  }

  // Meal times (filtered to only show featured ones)
  if (place.meal_times?.length) {
    const featuredMealTimes = place.meal_times.filter(
      (mealTime: { title: string } | null) =>
        mealTime !== null &&
        FEATURED_MEAL_TIMES.has(mealTime.title.toLowerCase())
    );
    if (featuredMealTimes.length) {
      tags.push(
        ...featuredMealTimes.filter((tag) => tag !== null).map((tag) => tag!)
      );
    }
  }

  // Ambiance
  if (place.ambiance?.length) {
    tags.push(...place.ambiance.filter((tag) => tag !== null));
  }

  // Parking
  if (place.parking?.length) {
    tags.push(...place.parking.filter((tag) => tag !== null));
  }

  return (
    <View style={styles.container}>
      {/* Header: Avatar, Name, Partner, Save Icon */}
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={navigateToPlace}
        style={styles.headerTouchable}
      >
        <View style={styles.header}>
          <Image
            source={{ uri: place.avatar?.full_url }}
            style={styles.avatar}
            contentFit='cover'
          />
          <View style={styles.nameContainer}>
            <Text
              variant='caption'
              color={colors.darkGrey[800]}
              weight='bold'
              numberOfLines={1}
            >
              {place.partner?.name}
            </Text>
            <Text variant='body' weight='bold' numberOfLines={1}>
              {place.name}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.saveButton}
            onPress={removePlaceFromCollection}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size='small' color={colors.primary[950]} />
            ) : (
              <SaveIcon color={colors.primary[950]} />
            )}
          </TouchableOpacity>
        </View>
      </TouchableOpacity>

      {/* Tags - Fixed ScrollView to ensure horizontal scrolling works properly */}
      {tags.length > 0 && (
        <View style={styles.tagsWrapper}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.tagsContainer}
            contentContainerStyle={styles.tagsContentContainer}
            nestedScrollEnabled={true}
            scrollEnabled={true}
            directionalLockEnabled={true}
            alwaysBounceHorizontal={true}
          >
            {/* Rating tag if available */}
            {place.rates?.google && (
              <Tag
                icon='star.fill'
                label={`${place.rates.google} Google`}
                customStyle={styles.tag}
                iconColor={colors.info.yellow}
              />
            )}

            {/* All other tags */}
            {tags.map((tag, index) => (
              <Tag
                key={`tag-${index}`}
                label={tag.title}
                customStyle={styles.tag}
                variant='light'
              />
            ))}
          </ScrollView>
        </View>
      )}

      {/* Horizontal Deal List - Replacing DealsCarousel */}
      {place.deals?.data && place.deals.data.length > 0 && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.dealsContainer}
          contentContainerStyle={styles.dealsContentContainer}
        >
          {place.deals?.data.map((deal, index: number) => {
            const IconComponent = DealIcons[deal.deal_type as DealType];
            const backgroundColor = dealBackgrounds[deal.deal_type as DealType];

            return (
              <TouchableOpacity
                key={`deal-${index}`}
                style={[styles.dealItem, { backgroundColor }]}
                onPress={navigateToPlace}
                activeOpacity={0.9}
              >
                {IconComponent && <IconComponent color={colors.base[100]} />}
                <Text
                  variant='caption'
                  color={colors.base[100]}
                  weight='bold'
                  numberOfLines={1}
                  style={styles.dealTitle}
                >
                  {deal.title}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      )}

      {/* Invisible touchable area that covers entire card for navigation */}
      <TouchableOpacity
        style={styles.cardTouchable}
        activeOpacity={0.9}
        onPress={navigateToPlace}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.softGrey[700],
    borderRadius: moderateScale(16),
    padding: moderateScale(16),
    marginBottom: moderateScale(16),
    position: 'relative',
  },
  headerTouchable: {
    zIndex: 10, // Higher than cardTouchable to ensure it's tappable
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: moderateScale(12),
  },
  avatar: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    marginRight: moderateScale(12),
  },
  nameContainer: {
    flex: 1,
  },
  saveButton: {
    padding: moderateScale(4),
    zIndex: 15, // Higher than other touchables to ensure it's tappable
  },
  tagsWrapper: {
    flexDirection: 'row',
    height: moderateScale(32),
    marginBottom: moderateScale(12),
    overflow: 'hidden',
    zIndex: 10, // Higher than cardTouchable to ensure scrolling works
  },
  tagsContainer: {
    flex: 1,
  },
  tagsContentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: moderateScale(16),
  },
  tag: {
    marginRight: moderateScale(8),
  },
  dealsContainer: {
    height: moderateScale(41),
    marginTop: 'auto',
    zIndex: 10, // Higher than cardTouchable to ensure scrolling works
  },
  dealsContentContainer: {
    flexDirection: 'row',
    gap: moderateScale(8),
  },
  dealItem: {
    height: verticalScale(41),
    borderRadius: moderateScale(10),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: moderateScale(16),
    gap: moderateScale(8),
    zIndex: 20, // Higher than other elements to ensure deals are tappable
  },
  dealTitle: {
    maxWidth: moderateScale(150),
  },
  cardTouchable: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 5, // Lower than other interactive elements
  },
});
