import React from 'react';
import { StyleSheet, Pressable, View } from 'react-native';
import { Image } from 'expo-image';
import { Text } from './ui/Text';
import { colors } from '../constants/Colors';
import { moderateScale, verticalScale } from '../utils/scaling';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Reel } from '@/graphql/generated/graphql';

interface ReelThumbnailProps {
  reel: Pick<Reel, 'id' | 'thumbnail' | 'caption'>;
  placeId: string;
  style?: object;
  isCreator?: boolean;
}

export const ReelThumbnail = ({
  reel,
  placeId,
  style,
  isCreator = false,
}: ReelThumbnailProps) => {
  const router = useRouter();

  const handlePress = () => {
    if (isCreator) {
      router.push({
        pathname: '/creators/[id]/reels' as const,
        params: { id: placeId, reelId: reel.id },
      });
    } else {
      router.push({
        pathname: '/places/[id]/reels' as const,
        params: { id: placeId, reelId: reel.id },
      });
    }
  };

  return (
    <Pressable style={[styles.reelItem, style]} onPress={handlePress}>
      <Image
        source={{ uri: reel.thumbnail }}
        style={styles.reelThumbnail}
        contentFit='cover'
      />
      <View style={styles.reelCaptionContainer}>
        <LinearGradient
          colors={[
            'transparent',
            colors.blackTransparent.black06,
            colors.blackTransparent.black08,
          ]}
          style={styles.reelGradient}
        >
          <Text
            variant='caption1'
            color={colors.base[100]}
            numberOfLines={2}
            style={styles.reelCaption}
          >
            {reel.caption || ''}
          </Text>
        </LinearGradient>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  reelItem: {
    width: moderateScale(150),
    height: verticalScale(216),
    borderRadius: moderateScale(16),
    overflow: 'hidden',
  },
  reelThumbnail: {
    width: '100%',
    height: '100%',
  },
  reelCaptionContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '30%',
    padding: 0,
  },
  reelGradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
    padding: moderateScale(8),
  },
  reelCaption: {
    textShadowColor: colors.blackTransparent.black08,
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});
