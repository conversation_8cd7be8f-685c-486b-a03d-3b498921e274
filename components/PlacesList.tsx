import React from 'react';
import { View, StyleSheet, Pressable, Linking } from 'react-native';
import { Text } from './ui/Text';
import { PlatformIcon } from './ui/PlatformIcon';
import { colors } from '../constants/Colors';
import { moderateScale } from '../utils/scaling';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { PlaceInsidePartnerFragment } from '../graphql/generated/graphql';

// Extracted component for rendering place content
const PlaceContent = ({ place }: { place: PlaceInsidePartnerFragment }) => (
  <>
    <Text variant='caption' weight='bold'>
      {place.name}
    </Text>
    <View style={styles.addressContainer}>
      {place.address_line_1 && (
        <Text variant='caption1' color={colors.darkGrey[500]} numberOfLines={1}>
          {place.address_line_1}
        </Text>
      )}
      {place.address_line_2 && (
        <Text variant='caption1' color={colors.darkGrey[500]} numberOfLines={1}>
          {place.address_line_2}
        </Text>
      )}
    </View>
  </>
);

export const PlacesList = ({
  places,
  currentPlaceId,
}: {
  places: PlaceInsidePartnerFragment[];
  currentPlaceId: string;
}) => {
  const { t } = useTranslation();
  const router = useRouter();

  return (
    <View style={styles.locationsContainer}>
      <Text variant='h2' style={styles.locationsTitle}>
        {t('place.locations.title')} ({places.length})
      </Text>
      {places.map((otherPlace) => (
        <View
          key={otherPlace.id}
          style={[
            styles.locationItem,
            {
              borderWidth: otherPlace.id === currentPlaceId ? 1 : 0,
              borderColor:
                otherPlace.id === currentPlaceId
                  ? colors.primary[950]
                  : 'transparent',
            },
          ]}
        >
          <View style={styles.locationLeftSection}>
            <PlatformIcon
              name='mappin.and.ellipse'
              size={16}
              color={colors.base[950]}
            />
            <View style={styles.locationTextContainer}>
              {otherPlace.id === currentPlaceId ? (
                <View>
                  <PlaceContent place={otherPlace} />
                </View>
              ) : (
                <Pressable
                  onPress={() => router.replace(`/places/${otherPlace.id}`)}
                >
                  <PlaceContent place={otherPlace} />
                </Pressable>
              )}
            </View>
          </View>

          {otherPlace.phone && (
            <Pressable
              onPress={() => {
                Linking.openURL(`tel:${otherPlace.phone}`);
              }}
              style={styles.phoneButton}
            >
              <PlatformIcon
                name='phone.fill'
                size={16}
                color={colors.primary[950]}
              />
            </Pressable>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  locationsContainer: {
    gap: moderateScale(12),
  },
  locationsTitle: {
    marginBottom: moderateScale(8),
  },
  locationItem: {
    flexDirection: 'row',
    paddingVertical: moderateScale(16),
    paddingHorizontal: moderateScale(12),
    borderRadius: moderateScale(12),
    marginBottom: moderateScale(8),
    height: moderateScale(88),
    backgroundColor: colors.softGrey[400],
  },
  locationLeftSection: {
    flexDirection: 'row',
    gap: moderateScale(8),
    flex: 1,
  },
  locationTextContainer: {
    flex: 1,
  },
  phoneButton: {
    width: moderateScale(32),
    height: moderateScale(32),
    backgroundColor: colors.primary[100],
    borderRadius: moderateScale(16),
    justifyContent: 'center',
    alignItems: 'center',
  },
  addressContainer: {
    marginTop: moderateScale(8),
  },
});
