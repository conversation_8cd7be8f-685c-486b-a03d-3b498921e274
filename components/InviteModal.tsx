import React, { useState } from 'react';
import { StyleSheet, View, Alert } from 'react-native';
import { Image } from 'expo-image';
import { useTranslation } from 'react-i18next';
import { moderateScale } from '../utils/scaling';
import { Button } from './ui/Button';
import { Text } from './ui/Text';
import { colors } from '@/constants/Colors';
import {
  useInviteUserMutation,
  UserFragment,
} from '@/graphql/generated/graphql';
import { getInviteLink } from '@/services/branch';

interface InviteModalProps {
  user?: UserFragment;
}

export const InviteModal = ({ user }: InviteModalProps) => {
  const { t } = useTranslation();
  const [isSharing, setIsSharing] = useState(false);

  // Mutation for inviting user
  const [inviteUserMutation] = useInviteUserMutation();

  // Calculate progress values
  const remaining = user?.invitations?.info?.remaining || 0;
  const total = user?.invitations?.info?.total || 5;
  const used = total - remaining;
  const progressPercentage = total > 0 ? (used / total) * 100 : 0;

  const handleShareInvite = async () => {
    if (isSharing) return;

    setIsSharing(true);

    try {
      // Call the invite user mutation first
      const { data } = await inviteUserMutation();

      // Then generate and share the invite link send the token to the backend
      await getInviteLink(data?.inviteUser || '');

      Alert.alert(
        t('inviteModal.success.title'),
        t('inviteModal.success.message'),
        [{ text: t('common.ok') }]
      );
    } catch (error) {
      console.error('Error sharing invite:', error);
      Alert.alert(
        t('inviteModal.error.title'),
        t('inviteModal.error.message'),
        [{ text: t('common.ok') }]
      );
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <View style={styles.container}>
      <Image
        source={require('@/assets/images/invite.png')}
        style={styles.image}
        contentFit='contain'
      />

      <Text variant='h1' align='center'>
        {t('inviteModal.title1')}
      </Text>
      <Text variant='h1' align='center'>
        {t('inviteModal.title2')}
      </Text>

      <Text variant='caption' style={styles.description} align='center'>
        {t('inviteModal.description')}
      </Text>

      {/* Invites left progress bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text variant='caption' color={colors.darkGrey[500]}>
            {t('inviteModal.inviteLeft')}
          </Text>
          <Text variant='caption' color={colors.base[950]} weight='bold'>
            {t('inviteModal.invitesCount', { remaining, total })}
          </Text>
        </View>

        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <View
              style={[
                styles.progressBarFill,
                { width: `${progressPercentage}%` },
              ]}
            />
          </View>
        </View>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title={t('inviteModal.shareButton')}
          onPress={handleShareInvite}
          style={styles.cancelButton}
          variant='primary'
          loading={isSharing}
          disabled={remaining <= 0}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingHorizontal: moderateScale(8),
  },
  image: {
    width: moderateScale(208),
    height: moderateScale(164),
    marginTop: moderateScale(32),
    marginBottom: moderateScale(16),
  },
  description: {
    marginTop: moderateScale(8),
    width: '88%',
  },
  progressContainer: {
    marginTop: moderateScale(54),
    width: '100%',
    marginBottom: moderateScale(32),
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: moderateScale(8),
  },
  progressBarContainer: {
    width: '100%',
  },
  progressBarBackground: {
    width: '100%',
    height: moderateScale(16),
    backgroundColor: colors.secondary[500],
    borderRadius: moderateScale(8),
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: colors.secondary[200],
    borderRadius: moderateScale(8),
  },
  buttonContainer: {
    width: '100%',
    gap: moderateScale(12),
  },
  cancelButton: {
    width: '100%',
  },
  backButton: {
    width: '100%',
  },
});
