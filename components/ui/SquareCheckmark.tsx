import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';

// SquareCheckmark Component - Displays a checkmark icon inside rotating squares
export function SquareCheckmark() {
  return (
    <View style={[styles.checkContainer]}>
      <View style={styles.rect} />
      <View style={[styles.rect, styles.rectTwo]} />
      <FontAwesome6
        name='check'
        size={moderateScale(32)}
        color={colors.primary[950]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  checkContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [
      { translateX: -moderateScale(25) },
      { translateY: -moderateScale(25) },
    ],
    justifyContent: 'center',
    alignItems: 'center',
  },
  rect: {
    position: 'absolute',
    width: moderateScale(50),
    height: moderateScale(50),
    backgroundColor: colors.base[100],
    borderRadius: moderateScale(10),
  },
  rectTwo: {
    transform: [{ rotate: '45deg' }],
  },
});
