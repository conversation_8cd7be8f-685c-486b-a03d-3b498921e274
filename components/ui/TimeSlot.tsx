import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { formatTimeRange } from '@/utils/time';

export interface TimeSlotProps {
  from: string;
  to: string;
  selected?: boolean;
  onSelect?: () => void;
}

export function TimeSlot({ from, to, selected, onSelect }: TimeSlotProps) {
  const timeRange = formatTimeRange(from, to);

  return (
    <TouchableOpacity
      onPress={onSelect}
      style={[styles.timeSlot, selected && styles.timeSlotSelected]}
    >
      <Text
        style={[styles.timeText, selected && styles.timeTextSelected]}
        variant='caption'
      >
        {timeRange}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  timeSlot: {
    width: moderateScale(150),
    height: verticalScale(40),
    borderWidth: 1,
    borderColor: colors.darkGrey[100],
    borderRadius: moderateScale(20),
    marginRight: moderateScale(8),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.base[100],
  },
  timeSlotSelected: {
    backgroundColor: colors.primary[200],
    borderColor: colors.primary[950],
  },
  timeText: {
    fontWeight: 'bold',
  },
  timeTextSelected: {
    color: colors.primary[950],
  },
});
