import { colors } from '@/constants/Colors';
import { Text as RNText, TextProps, StyleSheet } from 'react-native';
import { moderateScale } from '@/utils/scaling';

interface Props extends TextProps {
  variant?:
    | 'h1'
    | 'h1_5'
    | 'h2'
    | 'h3'
    | 'body'
    | 'caption'
    | 'caption1'
    | 'tiny'
    | 'tinier';
  weight?: 'bold' | 'medium' | 'regular';
  align?: 'left' | 'center' | 'right';
  color?: string;
}

export function Text({
  variant = 'body',
  style,
  weight,
  align,
  color,
  ...props
}: Props) {
  return (
    <RNText
      style={[
        styles.base,
        styles[variant],
        align && { textAlign: align },
        weight && { fontWeight: weight },
        color && { color },
        style,
      ]}
      {...props}
    />
  );
}

const styles = StyleSheet.create({
  base: {
    fontFamily: 'Urbanist',
  },
  h1: {
    fontFamily: 'Coolvetica',
    fontSize: moderateScale(32),
    fontWeight: 'bold',
    color: colors.base[950],
  },
  h1_5: {
    fontFamily: 'Coolvetica',
    fontSize: moderateScale(28),
    fontWeight: 'bold',
    color: colors.base[950],
  },
  h2: {
    fontFamily: 'Coolvetica',
    fontSize: moderateScale(24),
    fontWeight: '600',
    color: colors.base[950],
  },
  h3: {
    fontFamily: 'Coolvetica',
    fontSize: moderateScale(18),
    fontWeight: '600',
    color: colors.base[950],
  },
  body: {
    fontSize: moderateScale(16),
    color: colors.base[950],
  },
  caption: {
    fontSize: moderateScale(14),
    color: colors.darkGrey[700],
  },
  caption1: {
    fontSize: moderateScale(12),
    color: colors.darkGrey[950],
    fontFamily: 'Urbanist',
  },
  tiny: {
    fontSize: moderateScale(10),
    color: colors.darkGrey[950],
    fontFamily: 'Urbanist',
  },
  tinier: {
    fontSize: moderateScale(8),
    color: colors.darkGrey[950],
    fontFamily: 'Urbanist',
  },
});
