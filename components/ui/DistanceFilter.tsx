import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import { useTranslation } from 'react-i18next';
import {
  MIN_RADIUS,
  MAX_RADIUS,
  RADIUS_STEP,
} from '@/components/home/<USER>';

interface DistanceFilterProps {
  radius: number;
  setRadius: (radius: number) => void;
  min?: number;
  max?: number;
  step?: number;
  title?: string;
}

export const DistanceFilter: React.FC<DistanceFilterProps> = ({
  radius,
  setRadius,
  min = MIN_RADIUS,
  max = MAX_RADIUS,
  step = RADIUS_STEP,
  title,
}) => {
  const { t } = useTranslation();
  // Local state to track slider value during dragging without triggering API calls
  const [localRadius, setLocalRadius] = useState(radius);

  // Update local radius when the prop changes (e.g., when Clear All is clicked)
  useEffect(() => {
    setLocalRadius(radius);
  }, [radius]);

  // Update local value during dragging (without triggering API requests)
  const handleValueChange = ([value]: number[]) => {
    setLocalRadius(value);
  };

  // Only update the actual radius and trigger API request when finished dragging
  const handleValuesChangeFinish = ([value]: number[]) => {
    setRadius(value);
  };

  return (
    <View style={styles.container}>
      {title && (
        <Text
          variant='h3'
          weight='bold'
          color={colors.base[950]}
          style={styles.title}
        >
          {title}
        </Text>
      )}

      <View style={styles.sliderContainer}>
        <Text variant='caption' color={colors.base[950]}>
          {(localRadius / 1000).toFixed(0)} {t('common.km')}
        </Text>

        <MultiSlider
          values={[localRadius]}
          min={min}
          max={max}
          step={step}
          sliderLength={moderateScale(320)}
          onValuesChange={handleValueChange}
          onValuesChangeFinish={handleValuesChangeFinish}
          selectedStyle={{ backgroundColor: colors.primary[950] }}
          unselectedStyle={{ backgroundColor: colors.primary[300] }}
          containerStyle={styles.slider}
          trackStyle={styles.track}
          markerStyle={styles.marker}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.softGrey[700],
    padding: moderateScale(16),
    borderRadius: moderateScale(16),
    width: '100%',
    marginBottom: moderateScale(16),
  },
  title: {
    marginBottom: moderateScale(12),
  },
  sliderContainer: {
    alignItems: 'center',
    marginTop: moderateScale(8),
  },
  slider: {
    height: moderateScale(40),
  },
  track: {
    height: moderateScale(8),
    borderRadius: moderateScale(8),
  },
  marker: {
    position: 'relative',
    top: moderateScale(4),
    height: moderateScale(16),
    width: moderateScale(16),
    borderRadius: moderateScale(8),
    backgroundColor: colors.base[100],
    borderWidth: moderateScale(2),
    borderColor: colors.primary[700],
    shadowColor: colors.primary[950],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
