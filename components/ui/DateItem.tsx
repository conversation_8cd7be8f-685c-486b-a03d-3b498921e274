import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { format, isBefore, startOfDay, isToday, isTomorrow } from 'date-fns';
import { useTranslation } from 'react-i18next';

export interface DateItemProps {
  date: string;
  available_seats?: number;
  selected?: boolean;
  onSelect?: () => void;
}

export function DateItem({
  date,
  available_seats,
  selected,
  onSelect,
}: DateItemProps) {
  const { t } = useTranslation();
  const dateObj = new Date(date);
  const today = startOfDay(new Date());
  const seats = available_seats || 0; // Handle null/undefined by defaulting to 0
  const isDimmed = seats <= 0 || isBefore(dateObj, today);
  const day = format(dateObj, 'EEE');
  const dayNumber = format(dateObj, 'd');

  // Check if date is today or tomorrow, show Today or Tomorrow
  const getDisplayDay = () => {
    if (isToday(dateObj)) {
      return t('common.today');
    } else if (isTomorrow(dateObj)) {
      return t('common.tomorrow');
    } else {
      return day;
    }
  };

  return (
    <TouchableOpacity
      onPress={onSelect}
      disabled={isDimmed}
      style={[
        styles.dateItem,
        selected && styles.dateItemSelected,
        isDimmed && styles.dateItemDimmed,
      ]}
    >
      <Text variant='tiny' style={[isDimmed && styles.dateTextDimmed]}>
        {getDisplayDay()}
      </Text>
      <Text style={[styles.dateNumber, isDimmed && styles.dateTextDimmed]}>
        {dayNumber}
      </Text>
      <Text
        style={[
          styles.dateSlots,
          selected && styles.dateSlotsSelected,
          isDimmed && styles.dateTextDimmed,
        ]}
      >
        {t('deals.dealScreen.slots', { count: seats })}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  dateItem: {
    width: moderateScale(60),
    height: verticalScale(79),
    borderWidth: 1,
    borderColor: colors.darkGrey[100],
    borderRadius: moderateScale(20),
    marginRight: moderateScale(8),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.base[100],
  },
  dateItemSelected: {
    backgroundColor: colors.primary[200],
    borderColor: colors.primary[950],
  },
  dateItemDimmed: {
    opacity: 0.5,
  },
  dateNumber: {
    fontSize: moderateScale(16),
    fontWeight: 'bold',
    color: colors.base[950],
    marginVertical: verticalScale(4),
  },
  dateSlots: {
    fontSize: moderateScale(10),
    fontWeight: 'bold',
    color: colors.secondary[400],
  },
  dateSlotsSelected: {
    color: colors.primary[950],
  },
  dateTextDimmed: {
    color: colors.darkGrey[600],
  },
});
