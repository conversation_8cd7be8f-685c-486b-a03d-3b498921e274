import { colors } from '@/constants/Colors';
import { verticalScale } from '@/utils/scaling';
import { memo, useRef } from 'react';
import { StyleSheet, View } from 'react-native';

interface Props {
  progress: number;
  progressBarRef: React.RefObject<View>;
  isStandalone?: boolean;
}

export const ProgressBar = memo(function ProgressBar({
  progress,
  progressBarRef,
  isStandalone = false,
}: Props) {
  const containerRef = useRef<View>(null);

  const containerStyle = {
    ...styles.container,
    bottom: isStandalone ? verticalScale(40) : verticalScale(80),
  };

  return (
    <View ref={containerRef} style={containerStyle}>
      <View style={styles.progressBackground} />
      <View
        ref={progressBarRef}
        style={[styles.progressBar, { width: `${progress * 100}%` }]}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: verticalScale(24),
    justifyContent: 'center',
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  progressBackground: {
    height: verticalScale(3),
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  progressBar: {
    position: 'absolute',
    height: verticalScale(3),
    backgroundColor: colors.base[100],
  },
});
