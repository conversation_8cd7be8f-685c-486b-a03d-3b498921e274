import React from 'react';
import { StyleSheet, ActivityIndicator } from 'react-native';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { Text } from './Text';
import { PlatformIcon } from './PlatformIcon';
import { PanGestureHandler } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withSequence,
  withTiming,
  withRepeat,
  useAnimatedGestureHandler,
  interpolate,
  interpolateColor,
  runOnJS,
  cancelAnimation,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { dealBackgrounds } from './types/deal';
import { DealType } from '@/graphql/generated/graphql';
import { useTranslation } from 'react-i18next';

const ANIMATION_DURATION = 500;

interface Props {
  onRedeem?: () => void;
  type: DealType;
  disabled?: boolean;
  isLoading?: boolean;
}

export function SwipeButton({
  onRedeem,
  type,
  disabled,
  isLoading = false,
}: Props) {
  const { t } = useTranslation();
  const translateX = useSharedValue(0);
  const arrowsScale = useSharedValue(1);
  const circleScale = useSharedValue(1);
  const textScale = useSharedValue(1);
  const textTranslateX = useSharedValue(0);
  const arrowsTranslateX = useSharedValue(0);
  const checkIconScale = useSharedValue(0);
  const rightIconScale = useSharedValue(1);
  const checkIconRotate = useSharedValue(-45);

  // Cleanup animations on unmount
  React.useEffect(() => {
    return () => {
      // Cancel all ongoing animations
      cancelAnimation(translateX);
      cancelAnimation(arrowsScale);
      cancelAnimation(circleScale);
      cancelAnimation(textScale);
      cancelAnimation(textTranslateX);
      cancelAnimation(arrowsTranslateX);
      cancelAnimation(checkIconScale);
      cancelAnimation(rightIconScale);
      cancelAnimation(checkIconRotate);

      // Reset values
      translateX.value = 0;
      arrowsScale.value = 1;
      circleScale.value = 1;
      textScale.value = 1;
      textTranslateX.value = 0;
      arrowsTranslateX.value = 0;
      checkIconScale.value = 0;
      rightIconScale.value = 1;
      checkIconRotate.value = -45;
    };
  }, []);

  const handleComplete = React.useCallback(async () => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    try {
      await onRedeem?.();
      setTimeout(() => {
        translateX.value = withSpring(0);
      }, 1000);
    } catch (error) {
      translateX.value = withSpring(0);
    }
  }, [onRedeem]);

  // Animate arrows continuously
  React.useEffect(() => {
    if (!disabled) {
      arrowsScale.value = withRepeat(
        withSequence(
          withTiming(1.05, { duration: ANIMATION_DURATION }),
          withTiming(1, { duration: ANIMATION_DURATION })
        ),
        -1,
        true
      );

      arrowsTranslateX.value = withRepeat(
        withSequence(
          withTiming(3, { duration: ANIMATION_DURATION }),
          withTiming(0, { duration: ANIMATION_DURATION })
        ),
        -1,
        true
      );
    } else {
      arrowsScale.value = 1;
      arrowsTranslateX.value = 0;
    }
  }, [disabled]);

  // Animate circle continuously
  React.useEffect(() => {
    if (!disabled) {
      circleScale.value = withRepeat(
        withSequence(
          withTiming(1.1, { duration: ANIMATION_DURATION }),
          withTiming(1, { duration: ANIMATION_DURATION })
        ),
        -1,
        true
      );
    } else {
      circleScale.value = 1;
    }
  }, [disabled]);

  // Animate text continuously
  React.useEffect(() => {
    if (!disabled) {
      textScale.value = withRepeat(
        withSequence(
          withTiming(1.05, { duration: ANIMATION_DURATION }),
          withTiming(1, { duration: ANIMATION_DURATION })
        ),
        -1,
        true
      );

      textTranslateX.value = withRepeat(
        withSequence(
          withTiming(3, { duration: ANIMATION_DURATION }),
          withTiming(0, { duration: ANIMATION_DURATION })
        ),
        -1,
        true
      );
    } else {
      textScale.value = 1;
      textTranslateX.value = 0;
    }
  }, [disabled]);

  const gestureHandler = useAnimatedGestureHandler({
    onActive: (event) => {
      if (!disabled) {
        translateX.value = Math.max(0, Math.min(event.translationX, 300));
      }
    },
    onEnd: () => {
      if (!disabled && translateX.value > 200) {
        translateX.value = withSpring(270);
        runOnJS(handleComplete)();
      } else {
        translateX.value = withSpring(0);
      }
    },
  });

  const circleStyle = useAnimatedStyle(() => {
    const progress = translateX.value / 300;
    return {
      transform: [
        { translateX: translateX.value },
        // { scale: circleScale.value },
      ],
      opacity: interpolate(progress, [0, 0.8, 1], [1, 1, 0]),
      backgroundColor:
        disabled && !isLoading
          ? colors.darkGrey[200]
          : interpolateColor(
              progress,
              [0, 1],
              [dealBackgrounds[type], dealBackgrounds[type]]
            ),
    };
  });

  const arrowStyle1 = useAnimatedStyle(() => {
    const progress = translateX.value / 300;
    return {
      transform: [
        { scale: arrowsScale.value },
        { translateX: arrowsTranslateX.value + translateX.value * 0.2 },
      ],
      opacity: interpolate(progress, [0, 0.8], [1, 0]),
    };
  });

  const arrowStyle2 = useAnimatedStyle(() => {
    const progress = translateX.value / 300;
    return {
      transform: [
        { scale: arrowsScale.value },
        { translateX: arrowsTranslateX.value * 0.8 + translateX.value * 0.15 },
      ],
      opacity: interpolate(progress, [0, 0.8], [0.6, 0]),
    };
  });

  const arrowStyle3 = useAnimatedStyle(() => {
    const progress = translateX.value / 300;
    return {
      transform: [
        { scale: arrowsScale.value },
        { translateX: arrowsTranslateX.value * 0.6 + translateX.value * 0.1 },
      ],
      opacity: interpolate(progress, [0, 0.8], [0.3, 0]),
    };
  });

  const containerStyle = useAnimatedStyle(() => ({
    backgroundColor: interpolateColor(
      translateX.value / 300,
      [0, 1],
      [colors.base[100], dealBackgrounds[type]]
    ),
    // opacity: disabled ? 0.5 : 1,
  }));

  const textStyle = useAnimatedStyle(() => {
    const progress = translateX.value / 300;
    return {
      opacity: interpolate(progress, [0, 0.8], [1, 0]),
      transform: [
        { translateX: interpolate(progress, [0, 1], [0, -50]) },
        // { scale: textScale.value },
        // { translateX: textTranslateX.value },
      ],
    };
  });

  const arrowsContainerStyle = useAnimatedStyle(() => {
    const progress = translateX.value / 300;
    return {
      opacity: interpolate(progress, [0, 0.8], [1, 0]),
      transform: [{ translateX: interpolate(progress, [0, 1], [0, -20]) }],
    };
  });

  const rightIconStyle = useAnimatedStyle(() => {
    const progress = translateX.value / 300;
    const scale = interpolate(progress, [0, 0.7], [1, 0], 'clamp');
    return {
      transform: [
        { scale },
        { rotate: `${interpolate(progress, [0, 0.7], [0, 45], 'clamp')}deg` },
      ],
      opacity: scale,
      position: 'absolute',
    };
  });

  const checkIconStyle = useAnimatedStyle(() => {
    const progress = translateX.value / 300;
    const scale = interpolate(progress, [0.3, 0.7], [0, 1], 'clamp');
    return {
      transform: [
        { scale },
        {
          rotate: `${interpolate(progress, [0.3, 0.7], [-45, 0], 'clamp')}deg`,
        },
      ],
      opacity: scale,
      position: 'absolute',
    };
  });

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[styles.container, containerStyle]}>
        <Animated.View style={[styles.circle, circleStyle]}>
          {isLoading ? (
            <ActivityIndicator color={colors.base[100]} size='small' />
          ) : (
            <>
              <Animated.View style={rightIconStyle}>
                <PlatformIcon
                  name='chevron.right'
                  size={24}
                  color={colors.base[100]}
                />
              </Animated.View>
              <Animated.View style={checkIconStyle}>
                <PlatformIcon
                  name='checkmark'
                  size={24}
                  color={colors.base[100]}
                />
              </Animated.View>
            </>
          )}
        </Animated.View>

        <Animated.View style={[styles.textContainer, textStyle]}>
          <Text
            style={[
              styles.text,
              {
                color: disabled ? colors.darkGrey[400] : dealBackgrounds[type],
              },
            ]}
          >
            {isLoading
              ? t('common.loading')
              : disabled
                ? t('deals.swipeButton.comingSoon')
                : t('deals.swipeButton.swipeText')}
          </Text>
        </Animated.View>

        {!disabled && !isLoading && (
          <Animated.View style={[styles.arrows, arrowsContainerStyle]}>
            <Animated.View style={[arrowStyle1, styles.arrow]}>
              <PlatformIcon
                name='chevron.right'
                size={16}
                color={dealBackgrounds[type]}
              />
            </Animated.View>
            <Animated.View style={[arrowStyle2, styles.arrow]}>
              <PlatformIcon
                name='chevron.right'
                size={14}
                color={dealBackgrounds[type]}
              />
            </Animated.View>
            <Animated.View style={[arrowStyle3, styles.arrow]}>
              <PlatformIcon
                name='chevron.right'
                size={12}
                color={dealBackgrounds[type]}
              />
            </Animated.View>
          </Animated.View>
        )}
      </Animated.View>
    </PanGestureHandler>
  );
}

const styles = StyleSheet.create({
  container: {
    height: moderateScale(56),
    backgroundColor: colors.base[100],
    borderRadius: moderateScale(28),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  circle: {
    width: moderateScale(48),
    height: moderateScale(48),
    borderRadius: moderateScale(24),
    backgroundColor: colors.primary[950],
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    left: moderateScale(4),
    shadowColor: colors.base[950],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  textContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: moderateScale(16),
    fontFamily: 'Urbanist',

    fontWeight: 'bold',
  },
  arrows: {
    position: 'absolute',
    right: moderateScale(16),
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrow: {
    marginLeft: moderateScale(-5),
  },
});
