import { DealType } from '@/graphql/generated/graphql';
import { colors } from '@/constants/Colors';

export const dealBackgrounds: Record<DealType, string> = {
  [DealType.TwoForOne]: colors.primary[950],
  [DealType.OneAedSpecial]: colors.secondary[500],
  [DealType.FreeItemWithPurchase]: colors.green[500],
  [DealType.Aed_40Discount]: colors.yellow[950],
};

export const DealTypeToLabel: Record<DealType, string> = {
  [DealType.TwoForOne]: '2for1',
  [DealType.OneAedSpecial]: '1 AED special',
  [DealType.FreeItemWithPurchase]: 'Free Item with a purchase',
  [DealType.Aed_40Discount]: 'AED 40 Discount',
};
