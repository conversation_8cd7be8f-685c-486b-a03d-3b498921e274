import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { MyDeal as GraphQLMyDeal } from '@/graphql/generated/graphql';
import { dealBackgrounds } from './types/deal';
import { useTranslation } from 'react-i18next';
import { IconSymbol } from './IconSymbol.ios';
import { useRouter } from 'expo-router';

interface Props {
  myDeal: GraphQLMyDeal;
}

export function DealReuse({ myDeal }: Props) {
  const { t } = useTranslation();
  const router = useRouter();
  const { deal, id: myDealId } = myDeal;
  const { deal_type: type, id: dealId } = deal;

  const handleReuse = () => {
    if (!dealId) return;

    // Navigate to deal screen to select a new time slot
    router.push({
      pathname: '/deal',
      params: {
        dealId,
        myDealIdToRenew: myDealId,
      },
    });
  };

  return (
    <TouchableOpacity onPress={handleReuse} activeOpacity={0.8}>
      <View style={styles.container}>
        <IconSymbol name='repeat' size={20} color={dealBackgrounds[type]} />
        <Text
          variant='body'
          style={[{ color: dealBackgrounds[type] }, styles.text]}
          align='center'
        >
          {t('deals.deal.reuse')}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: moderateScale(56),
    borderRadius: moderateScale(28),
    backgroundColor: colors.base[100],
    gap: moderateScale(8),
  },

  text: {
    fontWeight: 'bold',
  },
});
