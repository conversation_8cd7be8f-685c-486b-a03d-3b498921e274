import { Link } from 'expo-router';
import { Pressable, StyleSheet } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';

interface Props {
  children: string;
  variant?: 'primary' | 'secondary';
  href?: string;
  onPress?: () => void;
  disabled?: boolean;
}

export function TextButton({
  children,
  variant = 'primary',
  href,
  onPress,
  disabled = false,
}: Props) {
  const textStyle = [
    styles.text,
    variant === 'secondary' && styles.secondaryText,
    disabled && styles.disabledText,
  ];

  if (href && !disabled) {
    return (
      <Link href={href as any}>
        <Text style={textStyle}>{children}</Text>
      </Link>
    );
  }

  return (
    <Pressable onPress={disabled ? undefined : onPress}>
      <Text style={textStyle}>{children}</Text>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  text: {
    fontWeight: '700',
    fontSize: moderateScale(14),
    color: colors.primary[950],
    textDecorationLine: 'underline',
  },
  secondaryText: {
    color: colors.darkGrey[700],
  },
  disabledText: {
    color: colors.darkGrey[300],
  },
});
