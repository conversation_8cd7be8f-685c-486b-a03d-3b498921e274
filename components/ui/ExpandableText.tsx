import React, { useState } from 'react';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from './Text';

interface Props {
  text: string;
  maxLines?: number;
  textColor?: string;
}

export function ExpandableText({
  text,
  maxLines = 1,
  textColor = colors.base[100],
}: Props) {
  const [isTextExpanded, setIsTextExpanded] = useState(false);
  const [fullNumberOfLines, setFullNumberOfLines] = useState(0);

  const toggleTextExpansion = () => {
    setIsTextExpanded(!isTextExpanded);
  };

  return (
    <View>
      {/* Hidden text component for measuring full height */}
      <Text
        variant='body'
        style={[styles.hiddenText, { position: 'absolute', opacity: 0 }]}
        onTextLayout={(e) => {
          if (fullNumberOfLines === 0) {
            setFullNumberOfLines(e.nativeEvent.lines.length);
          }
        }}
      >
        {text}
      </Text>

      {/* Visible text component */}
      <TouchableOpacity
        onPress={toggleTextExpansion}
        activeOpacity={0.8}
        style={styles.textButton}
      >
        <View style={styles.textContainer}>
          <Text
            variant='caption'
            color={textColor}
            numberOfLines={isTextExpanded ? 15 : maxLines}
          >
            {text}
            {/* {fullNumberOfLines > maxLines && (
              <>
                <Text variant='caption' color={textColor}>
                  {' ... '}
                </Text>
                <Text variant='caption1' color={colors.base[100]} weight='bold'>
                  {isTextExpanded ? 'less' : 'more'}
                </Text>
              </>
            )} */}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  textContainer: {
    marginBottom: moderateScale(12),
  },
  textButton: {
    width: '100%',
  },
  hiddenText: {
    marginBottom: moderateScale(4),
  },
});
