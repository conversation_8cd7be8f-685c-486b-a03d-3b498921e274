import { StyleSheet, View } from 'react-native';
import { Text } from './Text';
import { IconSymbol } from './IconSymbol';
import { colors } from '@/constants/Colors';
import { DealType, MyDealDateTimeSlot } from '@/graphql/generated/graphql';
import { moderateScale } from '@/utils/scaling';
import { format } from 'date-fns';
import { formatTimeRange } from '@/utils/time';
import { dealBackgrounds } from './types/deal';

interface Props {
  reserveSlot: MyDealDateTimeSlot;
  type: DealType;
}

export function BookingDate({ reserveSlot, type }: Props) {
  return (
    <View style={styles.container}>
      <View style={styles.dateContainer}>
        <View style={styles.iconContainer}>
          <IconSymbol
            name='calendar.circle.fill'
            size={24}
            color={dealBackgrounds[type]}
          />
        </View>

        <Text style={styles.dateTime} variant='caption' weight='bold'>
          {format(new Date(reserveSlot.date), 'EEE, d')}
        </Text>
      </View>
      <View style={styles.dateContainer}>
        <View style={styles.iconContainer}>
          <IconSymbol
            name='clock.fill'
            size={20}
            color={dealBackgrounds[type]}
          />
        </View>
        <Text style={styles.dateTime} variant='caption' weight='bold'>
          {formatTimeRange(reserveSlot.slot.from, reserveSlot.slot.to)}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(12),
    marginBottom: moderateScale(16),
    justifyContent: 'space-between',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
  },
  iconContainer: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderRadius: moderateScale(20),
    backgroundColor: colors.base[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  label: {
    color: colors.softGrey[100],
    fontSize: moderateScale(14),
  },
  dateTime: {
    color: colors.base[100],
  },
});
