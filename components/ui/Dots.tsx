import { colors } from '@/constants/Colors';
import { StyleSheet, View } from 'react-native';
import { moderateScale } from '@/utils/scaling';

interface Props {
  current: number;
  total: number;
}

export function Dots({ current, total }: Props) {
  return (
    <View style={styles.dots}>
      {[...Array(total)].map((_, index) => (
        <View
          key={index}
          style={[styles.dot, index === current && styles.activeDot]}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  dots: {
    position: 'absolute',
    right: moderateScale(20),
    flexDirection: 'row',
    gap: moderateScale(3),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  dot: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: moderateScale(4),
    backgroundColor: colors.primary[300],
  },
  activeDot: {
    backgroundColor: colors.primary[950],
    width: moderateScale(24),
  },
});
