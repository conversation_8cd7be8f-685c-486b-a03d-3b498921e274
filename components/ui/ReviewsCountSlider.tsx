import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import MultiSlider from '@ptomasroos/react-native-multi-slider';

interface ReviewsCountSliderProps {
  minReviews: number;
  setMinReviews: (minReviews: number) => void;
  min?: number;
  max?: number;
  step?: number;
  title?: string;
  isCleared?: boolean;
}

export const ReviewsCountSlider: React.FC<ReviewsCountSliderProps> = ({
  minReviews,
  setMinReviews,
  min = 0,
  max = 1000,
  step = 100,
  title,
  isCleared,
}) => {
  // Local state to track slider value during dragging without triggering API calls
  const [localMinReviews, setLocalMinReviews] = useState(minReviews);

  // Update local value when the prop changes (e.g., when Clear All is clicked)
  useEffect(() => {
    setLocalMinReviews(minReviews);
  }, [minReviews, isCleared]);

  // Update local value during dragging (without triggering API requests)
  const handleValueChange = ([value]: number[]) => {
    setLocalMinReviews(value);
  };

  // Only update the actual minReviews and trigger API request when finished dragging
  const handleValuesChangeFinish = ([value]: number[]) => {
    setMinReviews(value);
  };

  return (
    <View style={styles.container}>
      {title && (
        <Text variant='h3' style={styles.title}>
          {title}
        </Text>
      )}

      <View>
        <Text variant='caption'>{Math.round(localMinReviews)}+</Text>
        <MultiSlider
          values={[localMinReviews]}
          min={min}
          max={max}
          step={step}
          sliderLength={moderateScale(320)}
          onValuesChange={handleValueChange}
          onValuesChangeFinish={handleValuesChangeFinish}
          selectedStyle={{ backgroundColor: colors.primary[300] }}
          unselectedStyle={{ backgroundColor: colors.primary[950] }}
          containerStyle={styles.slider}
          trackStyle={styles.track}
          markerStyle={styles.marker}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.softGrey[700],
    padding: moderateScale(16),
    borderRadius: moderateScale(16),
    width: '100%',
    marginBottom: moderateScale(16),
  },
  title: {
    marginBottom: moderateScale(24),
  },
  slider: {
    height: moderateScale(40),
  },
  track: {
    height: moderateScale(8),
    borderRadius: moderateScale(8),
  },
  marker: {
    position: 'relative',
    top: moderateScale(4),
    height: moderateScale(16),
    width: moderateScale(16),
    borderRadius: moderateScale(8),
    backgroundColor: colors.base[100],
    borderWidth: moderateScale(2),
    borderColor: colors.primary[700],
    shadowColor: colors.primary[950],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
