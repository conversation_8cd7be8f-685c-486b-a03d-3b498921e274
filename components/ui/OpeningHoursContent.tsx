import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';

interface OpeningHour {
  day: string;
  hours: string;
}

interface Props {
  openingHours: OpeningHour[];
}

export function OpeningHoursContent({ openingHours }: Props) {
  const { t } = useTranslation();
  const today = new Date().getDay();

  return (
    <View>
      <Text variant='h2' style={styles.hoursTitle}>
        {t('place.openingHours.title')}
      </Text>
      {openingHours.map(({ day, hours }, index) => (
        <View key={day} style={styles.hourRow}>
          <Text
            variant='body'
            style={[styles.dayText, index === today && styles.todayText]}
          >
            {day}
          </Text>
          <Text
            variant='body'
            style={[styles.hoursText, hours === 'Closed' && styles.closedText]}
          >
            {hours}
          </Text>
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  hoursTitle: {
    marginBottom: moderateScale(16),
  },
  hourRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: moderateScale(8),
    borderBottomWidth: 1,
    borderBottomColor: colors.softGrey[400],
  },
  dayText: {
    flex: 1,
  },
  hoursText: {
    flex: 1,
    textAlign: 'right',
  },
  todayText: {
    color: colors.primary[950],
    fontWeight: '600',
  },
  closedText: {
    color: colors.info.red,
  },
});
