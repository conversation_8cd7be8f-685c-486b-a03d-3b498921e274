import React, { forwardRef, useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Modalize } from 'react-native-modalize';
import { Text } from './Text';
import { Input } from './Input';
import { Button } from './Button';
import { IconSymbol } from './IconSymbol';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { Modal } from './Modal';
import Constants from 'expo-constants';

interface Props {
  currentUrl: string;
  onSetUrl: (url: string) => void;
  onResetUrl: () => void;
  onCancel: () => void;
}

export const DevModeModal = forwardRef<Modalize, Props>(function DevModeModal(
  { currentUrl, onSetUrl, onResetUrl, onCancel },
  ref
) {
  const [inputUrl, setInputUrl] = useState(currentUrl);
  const [error, setError] = useState('');
  const [prError, setPrError] = useState('');

  // Reset input when modal opens
  useEffect(() => {
    setInputUrl(currentUrl);
    setError('');
    setPrError('');
  }, [currentUrl]);

  const handleSetUrl = () => {
    if (!inputUrl.trim()) {
      setError('Please enter a valid URL');
      return;
    }

    setError('');
    onSetUrl(inputUrl.trim());
  };

  const handleQuickSelect = (url: string) => {
    setInputUrl(url);
    setError('');
    setPrError(''); // Clear PR error when URL is set via quick select
  };

  const handlePRNumberInput = (prNumber: string) => {
    // Handle empty input
    if (prNumber.trim() === '') {
      setPrError('');
      return;
    }

    // Check if input contains only digits
    if (/^\d+$/.test(prNumber.trim())) {
      const prUrl = `https://pr-${prNumber.trim()}.conari.co/graphql`;
      setInputUrl(prUrl);
      setError('');
      setPrError('');
    } else {
      // Show error for non-numeric input
      setPrError('Please enter numbers only (e.g., 123)');
    }
  };

  const quickUrls = [
    { label: 'Production', url: 'https://conari.net/graphql' },
    { label: 'Staging', url: 'https://conari.co/graphql' },
  ];

  return (
    <Modal ref={ref}>
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <IconSymbol
            name='hammer'
            size={24}
            color={colors.primary[950]}
            style={styles.headerIcon}
          />
          <Text variant='h2' style={styles.title}>
            Dev Mode
          </Text>
          <Text variant='caption' style={styles.subtitle}>
            Change the base URL for testing different environments
          </Text>
        </View>

        {/* Current URL Display */}
        <View style={styles.currentUrlContainer}>
          <Text
            variant='caption'
            color={colors.darkGrey[700]}
            style={styles.currentUrlLabel}
          >
            Current URL:
          </Text>
          <Text
            variant='caption1'
            color={colors.primary[950]}
            style={styles.currentUrl}
          >
            {currentUrl}
          </Text>
        </View>

        {/* Quick Select Options */}
        <View style={styles.quickSelectContainer}>
          <Text
            variant='caption'
            color={colors.darkGrey[700]}
            style={styles.sectionLabel}
          >
            Quick Select:
          </Text>
          <View style={styles.quickSelectButtons}>
            {quickUrls.map((item) => (
              <TouchableOpacity
                key={item.label}
                style={[
                  styles.quickSelectButton,
                  inputUrl === item.url && styles.quickSelectButtonSelected,
                ]}
                onPress={() => handleQuickSelect(item.url)}
              >
                <Text
                  variant='caption1'
                  color={
                    inputUrl === item.url
                      ? colors.base[100]
                      : colors.primary[950]
                  }
                >
                  {item.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* PR Number Quick Input */}
        <View style={styles.prInputContainer}>
          <Text
            variant='caption'
            color={colors.darkGrey[700]}
            style={styles.sectionLabel}
          >
            Enter PR Number:
          </Text>
          <View style={styles.prInputRow}>
            <Input
              placeholder='e.g., 123'
              style={styles.prInput}
              onChangeText={handlePRNumberInput}
              keyboardType='numeric'
              placeholderTextColor={colors.darkGrey[500]}
              error={prError}
            />
            <Text
              variant='caption'
              color={colors.darkGrey[500]}
              style={styles.prSuffix}
            >
              → pr-[number].conari.co/graphql
            </Text>
          </View>
        </View>

        {/* Custom URL Input */}
        <View style={styles.urlInputContainer}>
          <Input
            label='Custom URL'
            placeholder={Constants.expoConfig?.extra?.GRAPHQL_URL}
            value={inputUrl}
            onChangeText={(text) => {
              setInputUrl(text);
              setError('');
              setPrError(''); // Clear PR error when URL is manually edited
            }}
            error={error}
            numberOfLines={2}
            placeholderTextColor={colors.darkGrey[500]}
          />
        </View>

        {/* Helper Text */}
        <View style={styles.helperContainer}>
          <Text variant='tiny' color={colors.darkGrey[500]}>
            💡 Examples:{'\n'}• Full URL: https://pr-10.conari.co/graphql{'\n'}•
            Production: https://conari.net/graphql{'\n'}• Just PR number: 123
            (auto-formats){'\n'}🔄 App will restart automatically after setting
            URL
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <View style={styles.buttonRow}>
            <Button
              title='Cancel'
              variant='secondary'
              onPress={onCancel}
              style={styles.cancelButton}
            />
            <Button
              title='Reset to Default'
              variant='outline'
              onPress={onResetUrl}
              style={styles.resetButton}
            />
          </View>
          <Button
            title='Set URL'
            variant='primary'
            onPress={handleSetUrl}
            style={styles.setButton}
          />
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: moderateScale(20),
    paddingBottom: moderateScale(20),
  },
  header: {
    alignItems: 'center',
    marginBottom: moderateScale(24),
  },
  headerIcon: {
    marginBottom: moderateScale(8),
  },
  title: {
    marginBottom: moderateScale(4),
  },
  subtitle: {
    textAlign: 'center',
    paddingHorizontal: moderateScale(20),
  },
  currentUrlContainer: {
    backgroundColor: colors.softGrey[300],
    padding: moderateScale(12),
    borderRadius: moderateScale(12),
    marginBottom: moderateScale(20),
  },
  currentUrlLabel: {
    marginBottom: moderateScale(4),
  },
  currentUrl: {
    fontFamily: 'monospace',
  },
  quickSelectContainer: {
    marginBottom: moderateScale(20),
  },
  sectionLabel: {
    marginBottom: moderateScale(8),
  },
  quickSelectButtons: {
    flexDirection: 'row',
    gap: moderateScale(8),
  },
  quickSelectButton: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(8),
    borderRadius: moderateScale(20),
    backgroundColor: colors.softGrey[400],
    borderWidth: 1,
    borderColor: colors.primary[200],
  },
  quickSelectButtonSelected: {
    backgroundColor: colors.primary[950],
    borderColor: colors.primary[950],
  },
  prInputContainer: {
    marginBottom: moderateScale(20),
  },
  prInputRow: {
    gap: moderateScale(8),
  },
  prInput: {
    flex: 1,
  },
  prSuffix: {
    flex: 2,
    fontSize: moderateScale(10),
  },
  urlInputContainer: {
    marginBottom: moderateScale(16),
  },
  helperContainer: {
    backgroundColor: colors.yellow[100],
    padding: moderateScale(12),
    borderRadius: moderateScale(8),
    borderLeftWidth: 3,
    borderLeftColor: colors.yellow[600],
    marginBottom: moderateScale(24),
  },
  actionsContainer: {
    gap: moderateScale(12),
  },
  buttonRow: {
    flexDirection: 'row',
    gap: moderateScale(12),
  },
  cancelButton: {
    flex: 1,
  },
  resetButton: {
    flex: 1,
  },
  setButton: {
    width: '100%',
  },
});
