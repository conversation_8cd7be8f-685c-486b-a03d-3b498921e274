import React from 'react';
import { colors } from '@/constants/Colors';
import Animated, {
  useAnimatedProps,
  SharedValue,
  interpolateColor,
} from 'react-native-reanimated';
import { Circle } from 'react-native-svg';

// Create an animated version of the SVG Circle component
const AnimatedCircle = Animated.createAnimatedComponent(Circle);

// AnimatedDot Props interface
interface AnimatedDotProps {
  x: number;
  y: number;
  r: number;
  ring: number;
  dotAngle: number;
  baseHue: number;
  baseDistance: number;
  animatedHue: SharedValue<number>;
  animatedTranslation: SharedValue<number>;
  animatedMargin: SharedValue<number>;
}

// AnimatedDot Component
export const AnimatedDot = React.memo(function AnimatedDot({
  x,
  y,
  r,
  ring,
  dotAngle,
  baseHue,
  baseDistance,
  animatedHue,
  animatedTranslation,
  animatedMargin,
}: AnimatedDotProps) {
  const ringIncrement = 10;
  const animatedProps = useAnimatedProps(() => {
    const ringOffset = ring * ringIncrement;
    const progress = ((baseHue + animatedHue.value + ringOffset) % 360) / 360;
    const fill = interpolateColor(
      progress,
      [0, 1],
      [colors.yellow[800], colors.green[500]]
    );

    const sinAngle = Math.sin(dotAngle);
    const cosAngle = Math.cos(dotAngle);

    const offsetX = -sinAngle * animatedTranslation.value;
    const offsetY = cosAngle * animatedTranslation.value;
    const marginOffset = (animatedMargin.value - 1) * baseDistance;
    const marginOffsetX = marginOffset * cosAngle;
    const marginOffsetY = marginOffset * sinAngle;

    return {
      fill,
      opacity: 1,
      r,
      transform: `translate(${offsetX + marginOffsetX}, ${offsetY + marginOffsetY})`,
    };
  }, [ring, dotAngle, baseHue, baseDistance]);

  return <AnimatedCircle cx={x} cy={y} animatedProps={animatedProps} />;
});
