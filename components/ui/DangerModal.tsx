import React, { forwardRef } from 'react';
import { StyleSheet, View } from 'react-native';
import { Modalize } from 'react-native-modalize';
import { moderateScale } from '../../utils/scaling';
import { Modal } from './Modal';
import { Text } from './Text';
import { Button } from './Button';
import { Image } from 'expo-image';
import { useTranslation } from 'react-i18next';
interface Props {
  title: string;
  description: string;
  image?: string;
  isLoading: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export const DangerModal = forwardRef<Modalize, Props>(function DangerModal(
  { title, description, image, onConfirm, onCancel, isLoading },
  ref
) {
  const { t } = useTranslation();
  return (
    <Modal ref={ref}>
      <View style={styles.container}>
        {image ? (
          <Image source={image} style={styles.image} contentFit='contain' />
        ) : null}

        <Text variant='h1' style={styles.title} align='center'>
          {title}
        </Text>

        <Text variant='caption' style={styles.description} align='center'>
          {description}
        </Text>

        <View style={styles.buttonContainer}>
          <Button
            disabled={isLoading}
            loading={isLoading}
            title={t('common.delete')}
            onPress={onConfirm}
            style={styles.cancelButton}
            variant='destructive'
          />
          <Button
            title={t('common.cancel')}
            onPress={onCancel}
            style={styles.backButton}
            variant='secondary'
          />
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
  },
  image: {
    width: moderateScale(200),
    height: moderateScale(230),
  },
  title: {
    marginVertical: moderateScale(16),
  },
  description: {
    marginBottom: moderateScale(24),
    width: '70%',
  },
  buttonContainer: {
    width: '100%',
    gap: moderateScale(12),
  },
  cancelButton: {
    width: '100%',
  },
  backButton: {
    width: '100%',
  },
});
