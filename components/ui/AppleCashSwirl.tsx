import React, { useEffect } from 'react';
import { DeviceMotion } from 'expo-sensors';
import { moderateScale } from '@/utils/scaling';
import Svg from 'react-native-svg';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import { AnimatedDot } from './AnimatedDot';

// AppleCashSwirl Component
export function AppleCashSwirl() {
  // Optimize static configuration for better performance
  const numRings = 10;
  const maxDots = 70;
  //outer ring increment
  const baseRadiusValue = 30;
  //inner ring increment
  const ringIncrement = 9;
  const maxRadius = baseRadiusValue + (numRings - 1) * ringIncrement + 55;
  const centerX = moderateScale(maxRadius);
  const centerY = moderateScale(maxRadius);

  const animatedHue = useSharedValue(0);
  const animatedTranslation = useSharedValue(0);
  const animatedMargin = useSharedValue(1);
  const animatedSwirlRotation = useSharedValue(0);
  const randomOffset = React.useRef(Math.random() * 360).current;

  // Device motion updates for animated values with proper cleanup
  useEffect(() => {
    let isMounted = true;
    const subscription = DeviceMotion.addListener((data) => {
      if (!isMounted || !data.rotation) return;

      // Batch animation updates for better performance
      requestAnimationFrame(() => {
        if (!isMounted) return;
        const { alpha, beta, gamma } = data.rotation;
        animatedHue.value = (((alpha || 0) * 180) / Math.PI) % 360;
        const gammaDeg = ((gamma || 0) * 180) / Math.PI;
        animatedTranslation.value = gammaDeg / 50;
        const betaDeg = Math.abs(((beta || 0) * 180) / Math.PI);
        animatedMargin.value = 1 + betaDeg / 10;
        animatedSwirlRotation.value =
          ((((alpha || 0) * 180) / Math.PI) % 360) + randomOffset;
      });
    });

    DeviceMotion.setUpdateInterval(100);

    return () => {
      isMounted = false;
      subscription.remove();
      animatedHue.value = 0;
      animatedTranslation.value = 0;
      animatedMargin.value = 1;
      animatedSwirlRotation.value = 0;
      DeviceMotion.removeAllListeners();
    };
  }, []);

  // Define minimum and maximum dot radius
  const minDotRadius = 0;
  const maxDotRadius = 4;
  const mid = (numRings - 1) / 2;

  // Memoize the circles array with optimized calculations
  const circles = React.useMemo(() => {
    const arr: JSX.Element[] = [];
    for (let ring = 0; ring < numRings; ring++) {
      const radius = baseRadiusValue + ring * ringIncrement;
      const circlesCount = Math.round(
        1 + (maxDots - 1) * (ring / (numRings - 1))
      );
      const t = 1 - Math.abs(ring - mid) / mid;
      const dotRadius = minDotRadius + t * (maxDotRadius - minDotRadius);

      for (let i = 0; i < circlesCount; i++) {
        const angle = ((2 * Math.PI) / circlesCount) * i + ring * 0.15;
        const x = centerX + moderateScale(radius) * Math.cos(angle);
        const y = centerY + moderateScale(radius) * Math.sin(angle);
        const baseHue = (360 / numRings) * ring + (i / circlesCount) * 60;

        arr.push(
          <AnimatedDot
            key={`ring-${ring}-dot-${i}`}
            x={x}
            y={y}
            r={moderateScale(dotRadius)}
            ring={ring}
            dotAngle={angle}
            baseHue={baseHue}
            baseDistance={moderateScale(radius)}
            animatedHue={animatedHue}
            animatedTranslation={animatedTranslation}
            animatedMargin={animatedMargin}
          />
        );
      }
    }
    return arr;
  }, [
    numRings,
    maxDots,
    baseRadiusValue,
    ringIncrement,
    mid,
    centerX,
    centerY,
  ]);

  const swirlAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${animatedSwirlRotation.value}deg` }],
  }));

  const svgSize = moderateScale(maxRadius * 2);

  return (
    <Animated.View style={swirlAnimatedStyle}>
      <Svg width={svgSize} height={svgSize}>
        {circles}
      </Svg>
    </Animated.View>
  );
}
