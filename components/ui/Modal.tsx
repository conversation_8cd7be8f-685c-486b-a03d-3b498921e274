import React, { forwardRef } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { Modalize, ModalizeProps } from 'react-native-modalize';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Portal } from 'react-native-portalize';

interface Props extends Partial<ModalizeProps> {
  children: React.ReactNode;
  contentContainerStyle?: ViewStyle;
  disableAdjustToContent?: boolean;
}

export const Modal = forwardRef<Modalize, Props>(function Modal(
  {
    children,
    contentContainerStyle,
    disableAdjustToContent = false,
    ...modalProps
  },
  ref
) {
  return (
    <Portal>
      <Modalize
        ref={ref}
        adjustToContentHeight={!disableAdjustToContent}
        handlePosition='inside'
        modalStyle={[styles.modal]}
        handleStyle={styles.handle}
        overlayStyle={styles.overlay}
        closeOnOverlayTap
        withHandle
        {...modalProps}
      >
        <View style={[styles.content, contentContainerStyle]}>{children}</View>
      </Modalize>
    </Portal>
  );
});

const styles = StyleSheet.create({
  modal: {
    backgroundColor: colors.base[100],
    borderTopLeftRadius: moderateScale(24),
    borderTopRightRadius: moderateScale(24),
    paddingTop: moderateScale(24),
  },
  handle: {
    backgroundColor: colors.primary[200],
    width: moderateScale(40),
  },
  overlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  content: {
    paddingBottom: verticalScale(34),
  },
});
