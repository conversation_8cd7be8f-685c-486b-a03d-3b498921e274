import React, { useRef, useState } from 'react';
import { colors } from '@/constants/Colors';
import { StyleSheet, View, ScrollView, TouchableOpacity } from 'react-native';
import { Text } from './Text';
import { moderateScale } from '@/utils/scaling';
import { Tag } from './Tag';
import { Button } from './Button';
import { SwipeButton } from './SwipeButton';
import {
  DealType,
  MyDealStatusEnum,
  MyDeal as GraphQLMyDeal,
  Deal as GraphQLDeal,
  useCancelDealMutation,
  useMyDealsQuery,
  useRedeemDealMutation,
} from '@/graphql/generated/graphql';
import { dealBackgrounds } from './types/deal';
import { DealPendingProgress } from './DealPendingProgress';
import { Image } from 'expo-image';
import { BookingDate } from './BookingDate';
import { DealInfoTooltip } from './DealInfoTooltip';
import { DiscoverOutline } from '@/components/icons/tabs/DiscoverOutline';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { showErrorToast, showSuccessToast } from '@/utils/Toast';
import { openLocationInGoogleMaps } from '@/utils/location';
import { DealReuse } from './DealReuse';
import { DangerModal } from './DangerModal';
import { Modalize } from 'react-native-modalize';
import { client } from '@/apollo/client';
import { useAuthStore } from '@/store/auth';
import {
  renderMaxSavingTag,
  renderReuseLimitDaysTag,
  renderServiceTypeTags,
} from '@/utils/serviceTypeUtils';

interface Props {
  myDeal?: GraphQLMyDeal;
  deal?: GraphQLDeal;
  placeId?: string; // Optional placeId just for cache invalidation
  isInDealsScreen?: boolean;
}

export function Deal({
  myDeal,
  deal,
  placeId,
  isInDealsScreen = false,
}: Props) {
  const { t } = useTranslation();
  const router = useRouter();
  const { shouldShowTrialModal } = useAuthStore();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [cancelDealMutation, { loading: isCancelling }] =
    useCancelDealMutation();
  const [redeemDealMutation, { loading: isRedeeming }] =
    useRedeemDealMutation();
  const { refetch: refetchMyDeals } = useMyDealsQuery();
  const cancelModalRef = useRef<Modalize>(null);

  // Return early if no valid props
  if (!myDeal && !deal) {
    return null;
  }

  // Extract dealData - the core deal information to display
  const dealData = myDeal ? myDeal.deal : deal!;

  // Extract additional properties
  const status = myDeal ? myDeal.status : null;
  const reserveSlot = myDeal ? myDeal.reserve_slot : null;
  const myDealId = myDeal ? myDeal.id : null;
  const reuseAfter = myDeal?.reuse_after;
  const partnerPlace = dealData.partner_place;

  const {
    id,
    title,
    description,
    deal_type,
    max_saving,
    reuse_limit_days,
    service_types,
  } = dealData;

  const getStatusTagColor = () => {
    return deal_type === DealType.TwoForOne
      ? colors.secondary[500]
      : colors.primary[950];
  };

  const handleOpenMap = () => {
    const { lat = 0, lng = 0 } = partnerPlace?.location || {};
    if (lat && lng && typeof lat === 'number' && typeof lng === 'number') {
      openLocationInGoogleMaps(lat, lng);
    }
  };

  const handleReserve = () => {
    // Check if trial modal should be shown first
    if (shouldShowTrialModal()) {
      router.push('/(modals)/trial');
      return;
    }

    router.push({
      pathname: '/deal',
      params: { dealId: id },
    });
  };

  const handleRedeem = async () => {
    //if id is not available, return
    if (!myDealId) return;
    try {
      const response = await redeemDealMutation({
        variables: {
          input: {
            myDealId,
          },
        },
      });

      if (response.data?.redeemDeal.myDeal) {
        router.push({
          pathname: '/redeem',
          params: {
            deal: JSON.stringify(response.data.redeemDeal.myDeal),
          },
        });
      }
    } catch (error) {
      showErrorToast(
        error instanceof Error ? error.message : t('common.errors.unknown')
      );
    }
  };

  const handleCancelPress = () => {
    cancelModalRef.current?.open();
  };

  const handleCancelConfirm = async () => {
    if (!myDealId) return;

    try {
      setIsRefreshing(true);

      const response = await cancelDealMutation({
        variables: {
          input: {
            myDealId,
          },
        },
      });

      if (response.data?.cancelDeal.message) {
        showSuccessToast(response.data.cancelDeal.message);
        // Refetch myDeals always
        await refetchMyDeals();

        // Refetch place data if we're in place screen
        if (!isInDealsScreen) {
          // Use placeId if provided, otherwise try to get it from partnerPlace
          const idToInvalidate = placeId || partnerPlace?.id;

          if (idToInvalidate) {
            try {
              client.cache.evict({
                id: client.cache.identify({
                  __typename: 'PartnerPlace',
                  id: idToInvalidate,
                }),
              });
              client.cache.gc();
            } catch (error) {
              console.log('Error evicting cache:', error);
            }
          }
        }

        cancelModalRef.current?.close();
      }
    } catch (error) {
      showErrorToast(
        error instanceof Error ? error.message : t('common.errors.unknown')
      );
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleModalClose = () => {
    cancelModalRef.current?.close();
  };

  const handleOpenPlaceInfo = () => {
    if (partnerPlace?.id) {
      router.push(`/places/${partnerPlace.id}`);
    }
  };

  // Check if deal can be reused (reuseAfter is in the past)
  const isReusable = () => {
    if (!reuseAfter) return false;

    try {
      const reuseAfterDate = new Date(reuseAfter);
      return reuseAfterDate < new Date();
    } catch (error) {
      return false;
    }
  };

  // Render the appropriate action block based on status
  const renderActionBlock = () => {
    // Case 1: Direct deal (not a myDeal) - show reserve button
    if (!myDeal && deal) {
      return (
        <Button
          title={t('deals.deal.reserveButton')}
          onPress={handleReserve}
          style={styles.button}
          variant='secondary'
        />
      );
    }

    // Case 2: Upcoming or Redeemable deals - show swipe button
    if (
      status &&
      (status === MyDealStatusEnum.Upcoming ||
        status === MyDealStatusEnum.Redeemable)
    ) {
      return (
        <>
          <SwipeButton
            onRedeem={handleRedeem}
            type={deal_type}
            disabled={status === MyDealStatusEnum.Upcoming || isRedeeming}
            isLoading={isRedeeming}
          />

          <View style={styles.actionsContainer}>
            <TouchableOpacity onPress={handleCancelPress}>
              <Text style={styles.actionText}>{t('common.cancel')}</Text>
            </TouchableOpacity>
          </View>
        </>
      );
    }

    // Case 3: Redeemed/NoShow that can't be reused yet
    if (
      status &&
      (status === MyDealStatusEnum.Redeemed ||
        status === MyDealStatusEnum.NoShow) &&
      myDeal &&
      !isReusable()
    ) {
      return <DealPendingProgress myDeal={myDeal} />;
    }

    // Case 4: Redeemed/NoShow that can be reused now
    if (
      status &&
      (status === MyDealStatusEnum.Redeemed ||
        status === MyDealStatusEnum.NoShow) &&
      myDeal &&
      isReusable()
    ) {
      return <DealReuse myDeal={myDeal} />;
    }

    // Default - no action block
    return null;
  };

  return (
    <>
      <View
        style={[
          styles.container,
          { backgroundColor: dealBackgrounds[deal_type] },
        ]}
      >
        <View style={styles.content}>
          <>
            <View style={styles.statusTagContainer}>
              {status && (
                <Tag
                  label={t(`deals.filters.${status.toLowerCase()}`)}
                  customStyle={{
                    backgroundColor: getStatusTagColor(),
                  }}
                  iconColor={colors.base[100]}
                  variant={
                    deal_type === DealType.TwoForOne ? 'secondary' : 'primary'
                  }
                />
              )}
            </View>
            <View style={styles.infoContainer}>
              <View style={styles.iconGroup}>
                {isInDealsScreen && (
                  <TouchableOpacity
                    onPress={handleOpenMap}
                    style={styles.mapIcon}
                  >
                    <DiscoverOutline color={colors.base[100]} />
                  </TouchableOpacity>
                )}
                <DealInfoTooltip
                  serviceTypes={service_types}
                  maxSaving={max_saving}
                  reuseLimitDays={reuse_limit_days}
                  iconColor={colors.base[100]}
                />
              </View>
            </View>
          </>

          <Text variant='h2' style={styles.title} numberOfLines={2}>
            {title}
          </Text>

          <Text
            variant='caption'
            style={styles.description}
            numberOfLines={2}
            color={colors.base[100]}
          >
            {description}
          </Text>
          {isInDealsScreen && (
            <TouchableOpacity activeOpacity={0.8} onPress={handleOpenPlaceInfo}>
              <View style={styles.partnerInfo}>
                <Image
                  source={{ uri: partnerPlace?.avatar?.full_url }}
                  style={styles.partnerImage}
                  contentFit='cover'
                />
                <Text
                  color={colors.base[100]}
                  variant='body'
                  numberOfLines={2}
                  style={styles.partnerName}
                >
                  {`${partnerPlace?.partner?.name} - ${partnerPlace?.name}`}
                </Text>
              </View>
            </TouchableOpacity>
          )}

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tagsContainer}
          >
            {renderServiceTypeTags({
              serviceTypes: service_types,
              variant: 'transparent',
            })}
            {renderMaxSavingTag(max_saving || 0, 'transparent')}
            {renderReuseLimitDaysTag(reuse_limit_days, 'transparent')}
          </ScrollView>

          <View>
            <View style={styles.dashedLine}>
              {[...Array(30)].map((_, index) => (
                <View key={index} style={styles.dash} />
              ))}
            </View>
            {/* Left circle cutout */}
            <View style={[styles.circle, styles.leftCircle]} />
            {/* Right circle cutout */}
            <View style={[styles.circle, styles.rightCircle]} />
          </View>

          {/* Show booking date for upcoming or redeemable deals */}
          {reserveSlot &&
            status &&
            (status === MyDealStatusEnum.Upcoming ||
              status === MyDealStatusEnum.Redeemable) && (
              <BookingDate reserveSlot={reserveSlot} type={deal_type} />
            )}

          {/* Render the appropriate action block */}
          {renderActionBlock()}
        </View>
      </View>

      <DangerModal
        ref={cancelModalRef}
        isLoading={isCancelling || isRefreshing}
        onConfirm={handleCancelConfirm}
        onCancel={handleModalClose}
        title={t('deals.cancelDealModal.title')}
        description={t('deals.cancelDealModal.description')}
        image={require('@/assets/images/cancelDeal.png')}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: moderateScale(24),
    marginBottom: moderateScale(16),
    overflow: 'hidden',
    position: 'relative',
  },
  content: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(20),
  },
  title: {
    marginBottom: moderateScale(8),
    color: colors.base[100],
  },
  description: {
    marginBottom: moderateScale(16),
    opacity: 0.7,
    //reserve 34px for the description to handle two lines case
    height: moderateScale(34),
  },
  tagsContainer: {
    gap: moderateScale(8),
    paddingBottom: moderateScale(20),
  },
  dashedLine: {
    position: 'relative',
    marginBottom: moderateScale(16),
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(7.1),
    overflow: 'hidden',
    height: moderateScale(20),
  },
  dash: {
    height: 1,
    backgroundColor: colors.base[100],
    width: moderateScale(7),
  },
  circle: {
    position: 'absolute',
    width: moderateScale(20),
    height: moderateScale(20),
    borderRadius: moderateScale(10),
    backgroundColor: colors.base[100],
  },
  leftCircle: {
    left: -moderateScale(26),
  },
  rightCircle: {
    right: -moderateScale(26),
  },
  button: {
    width: '100%',
    backgroundColor: colors.base[100],
  },
  statusTagContainer: {
    flexDirection: 'row',
    marginBottom: moderateScale(8),
  },
  partnerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(12),
    // marginTop: moderateScale(8),
    marginBottom: moderateScale(8),
  },
  partnerName: {
    maxWidth: '90%',
  },
  partnerImage: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderRadius: moderateScale(16),
  },

  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: moderateScale(18),
    marginTop: moderateScale(24),
    minHeight: moderateScale(30), // Add minimum height to prevent layout shift
  },
  actionText: {
    color: colors.base[100],
    fontSize: moderateScale(14),
    fontWeight: '700',
  },
  actionSeparator: {
    color: colors.base[100],
    opacity: 0.3,
  },
  infoContainer: {
    position: 'absolute',
    top: moderateScale(10),
    right: moderateScale(10),
    zIndex: 2,
  },
  iconGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: moderateScale(12),
    transform: [{ translateX: -moderateScale(20) }],
  },
  mapIcon: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
