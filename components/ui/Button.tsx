import React from 'react';
import { colors } from '@/constants/Colors';
import {
  TouchableOpacity,
  TouchableOpacityProps,
  StyleSheet,
  View,
  ActivityIndicator,
} from 'react-native';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Text } from './Text';

interface Props extends TouchableOpacityProps {
  variant?: 'primary' | 'secondary' | 'destructive' | 'outline';
  title: string;
  leftIcon?: React.ReactNode;
  loading?: boolean;
}

export function Button({
  variant = 'primary',
  title,
  leftIcon,
  style,
  disabled,
  loading = false,
  ...props
}: Props) {
  const isDisabled = disabled || loading;

  return (
    <TouchableOpacity
      disabled={isDisabled}
      activeOpacity={0.8}
      style={[
        styles.base,
        styles[variant],
        style,
        isDisabled && styles.disabled,
      ]}
      {...props}
    >
      <View style={styles.content}>
        {loading ? (
          <ActivityIndicator
            color={
              variant === 'secondary' || variant === 'outline' || isDisabled
                ? colors.primary[950]
                : colors.base[100]
            }
            style={styles.indicator}
          />
        ) : (
          <>
            {leftIcon && <View style={styles.icon}>{leftIcon}</View>}
            <Text
              style={[
                styles.text,
                styles[`${variant}Text`],
                isDisabled && styles.disabledText,
              ]}
              weight='bold'
            >
              {title}
            </Text>
          </>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  base: {
    borderRadius: moderateScale(32),
    alignItems: 'center',
    justifyContent: 'center',
    height: verticalScale(56),
  },
  primary: {
    backgroundColor: colors.primary[950],
  },
  secondary: {
    backgroundColor: 'transparent',
    borderWidth: moderateScale(1),
    borderColor: colors.darkGrey[100],
  },
  outline: {
    backgroundColor: colors.base[100],
    borderWidth: moderateScale(1),
    borderColor: colors.primary[950],
  },
  destructive: {
    backgroundColor: colors.info.red,
  },
  text: {
    fontSize: moderateScale(16),
    fontFamily: 'Urbanist',
  },
  primaryText: {
    color: colors.base[100],
  },
  secondaryText: {
    color: colors.base[950],
  },
  outlineText: {
    color: colors.primary[950],
  },
  destructiveText: {
    color: colors.base[100],
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: moderateScale(8),
  },
  indicator: {
    marginHorizontal: moderateScale(8),
  },
  disabled: {
    backgroundColor: colors.softGrey[900],
  },
  disabledText: {
    color: colors.darkGrey[500],
  },
});
