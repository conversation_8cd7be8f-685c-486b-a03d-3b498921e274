import React from 'react';
import { StyleSheet, View, Pressable } from 'react-native';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';

interface Props {
  onPress: () => void;
  absolute?: boolean;
  children: React.ReactNode;
  variant?: 'light' | 'dark' | 'white';
}

export function RightHeaderButton({
  onPress,
  absolute = false,
  children,
  variant,
}: Props) {
  return (
    <View style={[styles.container, absolute && styles.absolutePosition]}>
      <Pressable
        style={[styles.button, variant && styles[variant]]}
        onPress={onPress}
      >
        {children}
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    zIndex: 10,
  },
  absolutePosition: {
    position: 'absolute',
    top: verticalScale(60),
    right: moderateScale(16),
  },
  button: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: colors.softGrey[400],
    justifyContent: 'center',
    alignItems: 'center',
    // shadowColor: colors.base[950],
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.2,
    // shadowRadius: 4,
    // elevation: 4,
  },
  dark: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  light: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  white: {
    backgroundColor: colors.base[100],
  },
});
