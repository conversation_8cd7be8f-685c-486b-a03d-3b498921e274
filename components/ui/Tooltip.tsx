import React, { useRef } from 'react';
import {
  StyleSheet,
  ViewStyle,
  TextStyle,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Modalize } from 'react-native-modalize';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Modal } from './Modal';
import { Text } from './Text';

interface Props {
  // Allow either text or a render function for content
  text?: string;
  content?: React.ReactNode | ((closeModal: () => void) => React.ReactNode);
  children: React.ReactNode;
  buttonStyle?: ViewStyle;
  modalStyle?: ViewStyle;
  contentStyle?: ViewStyle;
  textStyle?: TextStyle;
  title?: string;
}

export function Tooltip({
  text,
  content,
  children,
  buttonStyle,
  contentStyle,
  textStyle,
  title,
}: Props) {
  const modalRef = useRef<Modalize>(null);

  const handlePress = () => {
    modalRef.current?.open();
  };

  const handleClose = () => {
    modalRef.current?.close();
  };

  const renderContent = () => {
    // If content is provided as a function, call it with close handler
    if (typeof content === 'function') {
      return content(handleClose);
    }

    // If content is provided as a component, render it directly
    if (content) {
      return content;
    }

    // Default text rendering
    return (
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[styles.content, contentStyle]}
        showsVerticalScrollIndicator={false}
      >
        {title && (
          <Text variant='h2' style={styles.title}>
            {title}
          </Text>
        )}
        <Text variant='body' style={[styles.text, textStyle]}>
          {text}
        </Text>
      </ScrollView>
    );
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.button, buttonStyle]}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        {children}
      </TouchableOpacity>

      <Modal ref={modalRef} contentContainerStyle={styles.modalContainer}>
        {renderContent()}
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  button: {
    flex: 1,
  },
  modalContainer: {
    marginVertical: verticalScale(16),
    paddingHorizontal: moderateScale(16),
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: moderateScale(24),
  },
  title: {
    marginBottom: moderateScale(16),
    fontSize: moderateScale(20),
  },
  text: {
    fontSize: moderateScale(16),
    lineHeight: moderateScale(24),
  },
});
