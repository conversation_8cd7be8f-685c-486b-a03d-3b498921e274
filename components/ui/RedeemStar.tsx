import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import Svg, { Path } from 'react-native-svg';
import { colors } from '@/constants/Colors';
const RedeemStar = ({
  color = colors.primary[200],
  style,
}: {
  color?: string;
  style?: ViewStyle;
}) => {
  return (
    <View style={style}>
      <Svg viewBox='0 0 200 200' style={styles.container}>
        <Path
          d='M14.5348 2.97593L13.6347 6.57624C13.54 7.00259 13.54 7.47631 13.6821 7.90267L14.8664 11.4556C15.4823 13.2558 13.8716 15.0085 12.0241 14.5348L8.42377 13.6347C7.99742 13.54 7.5237 13.54 7.09735 13.6821L3.5444 14.8664C1.74425 15.4823 -0.00853584 13.8716 0.46519 12.0241L1.36525 8.42376C1.46 7.99741 1.46001 7.52369 1.31789 7.09733L0.133576 3.5444C-0.482267 1.74424 1.1284 -0.00854164 2.97593 0.465183L6.57623 1.36526C7.00258 1.46001 7.4763 1.46001 7.90265 1.31789L11.4556 0.133576C13.2084 -0.482267 14.9612 1.1284 14.5348 2.97593Z'
          fill={color}
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  },
});

export default RedeemStar;
