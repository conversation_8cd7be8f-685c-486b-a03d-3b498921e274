import React, { useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Text } from '@/components/ui/Text';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import { ReviewFragment, useMeQuery } from '@/graphql/generated/graphql';
import { MenuView, type MenuComponentRef } from '@react-native-menu/menu';
import { useRouter } from 'expo-router';
import AntDesign from '@expo/vector-icons/AntDesign';
import { formatRelativeTime } from '@/utils/time';

interface ReviewProps {
  review: ReviewFragment;
  placeId?: string;
}

export function Review({ review, placeId }: ReviewProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const menuRef = useRef<MenuComponentRef>(null);
  const { data: userData } = useMeQuery();

  const isCurrentUserReview = userData?.me?.id === review.user.id;

  const actions = [
    ...(isCurrentUserReview
      ? [
          {
            id: 'edit',
            title: t('common.edit'),
            image: Platform.select({
              ios: 'pencil',
              android: 'ic_menu_edit',
            }),
            imageColor: colors.primary[950],
            titleColor: colors.primary[950],
          },
        ]
      : []),
    {
      id: 'report',
      title: t('common.report'),
      image: Platform.select({
        ios: 'exclamationmark.triangle',
        android: 'ic_menu_report_image',
      }),
      imageColor: colors.info.red,
      titleColor: colors.info.red,
    },
  ];

  const handlePressAction = (event: string) => {
    if (event === 'edit') {
      router.push({
        pathname: '/(modals)/review',
        params: {
          review: JSON.stringify(review),
          placeId: placeId,
          isEditing: 'true',
        },
      });
    } else if (event === 'report') {
      // Navigate to report screen with review context
      router.push({
        pathname: '/report',
        params: {
          reviewId: review.id,
          reviewText: review.review_text || '',
          userName: review.user.name,
        },
      });
    }
  };

  const renderStars = (rating: number) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <AntDesign
            key={star}
            name={star <= rating ? 'star' : 'staro'}
            size={moderateScale(16)}
            color={star <= rating ? colors.yellow[900] : colors.darkGrey[300]}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.userSection}>
          <View style={styles.userIcon}>
            <Text variant='body' weight='bold' color={colors.base[100]}>
              {review.user.name?.charAt(0).toUpperCase() || '?'}
            </Text>
          </View>
          <View style={styles.userInfo}>
            <Text
              variant='body'
              weight='bold'
              numberOfLines={1}
              style={styles.userName}
            >
              {review.user.name}
            </Text>
          </View>
        </View>

        <MenuView
          ref={menuRef}
          onPressAction={({ nativeEvent }) => {
            const { event } = nativeEvent;
            handlePressAction(event);
          }}
          actions={actions}
          themeVariant='light'
        >
          <TouchableOpacity
            style={styles.menuButton}
            onPress={
              Platform.OS === 'android'
                ? () => menuRef.current?.show()
                : () => {}
            }
          >
            <IconSymbol name='ellipsis' size={22} color={colors.base[950]} />
          </TouchableOpacity>
        </MenuView>
      </View>

      <View style={styles.starsAndDateContainer}>
        {renderStars(review.rating)}
        <Text variant='caption1' color={colors.darkGrey[700]}>
          {formatRelativeTime(review.created_at)}
        </Text>
      </View>

      {review.review_text && (
        <View style={styles.reviewTextContainer}>
          <Text
            variant='body'
            style={styles.reviewText}
            numberOfLines={8}
            color={colors.darkGrey[700]}
          >
            {review.review_text}
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.softGrey[400],
    borderRadius: moderateScale(24),
    padding: moderateScale(16),
    marginBottom: moderateScale(12),
    borderWidth: 1,
    borderColor: colors.softGrey[300],
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: moderateScale(8),
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userIcon: {
    width: moderateScale(32),
    height: moderateScale(32),
    borderRadius: moderateScale(24),
    backgroundColor: colors.primary[950],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: moderateScale(12),
  },
  userName: {
    fontFamily: 'Coolvetica',
  },
  userInfo: {
    flex: 1,
    gap: moderateScale(4),
  },
  starsContainer: {
    flexDirection: 'row',
    gap: moderateScale(2),
  },
  menuButton: {
    padding: moderateScale(4),
  },
  reviewText: {
    lineHeight: moderateScale(20),
  },
  starsAndDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(12),
  },

  reviewTextContainer: {
    marginTop: moderateScale(16),
  },
});
