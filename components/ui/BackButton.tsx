import React from 'react';
import { StyleSheet, View, Pressable } from 'react-native';
import { IconSymbol } from './IconSymbol';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';

interface Props {
  onPress: () => void;
  absolute?: boolean;
  transparent?: boolean;
}

export function BackButton({
  onPress,
  absolute = false,
  transparent = false,
}: Props) {
  return (
    <View style={[styles.container, absolute && styles.absolutePosition]}>
      <Pressable
        style={[styles.button, transparent && styles.transparent]}
        onPress={onPress}
      >
        <IconSymbol
          name='chevron.left'
          size={22}
          color={transparent ? colors.base[100] : colors.base[950]}
        />
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    zIndex: 10,
  },
  absolutePosition: {
    position: 'absolute',
    top: verticalScale(60),
    left: moderateScale(16),
  },
  button: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    backgroundColor: colors.softGrey[400],
    justifyContent: 'center',
    alignItems: 'center',
    // shadowColor: colors.base[950],
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.2,
    // shadowRadius: 4,
    // elevation: 4,
  },
  transparent: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
});
