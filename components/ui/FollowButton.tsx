import React from 'react';
import { StyleSheet, Pressable, ActivityIndicator, View } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { PlatformIcon } from './PlatformIcon';
import { useTranslation } from 'react-i18next';

interface FollowButtonProps {
  isFollowing: boolean;
  onPress: () => void;
  loading?: boolean;
  style?: any;
}

export const FollowButton = ({
  isFollowing,
  onPress,
  loading = false,
  style,
}: FollowButtonProps) => {
  const { t } = useTranslation();

  // Derive dynamic styles based on state
  const followButtonStyle = {
    backgroundColor: isFollowing ? colors.base[100] : colors.primary[950],
    borderColor: colors.primary[950],
  };
  const followButtonTextStyle = {
    color: isFollowing ? colors.primary[950] : colors.base[100],
  };
  const loadingIndicatorColor = isFollowing
    ? colors.primary[950]
    : colors.base[100];

  return (
    <Pressable
      style={[styles.followButton, followButtonStyle, style]}
      onPress={onPress}
      disabled={loading}
    >
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size={16} color={loadingIndicatorColor} />
        </View>
      ) : (
        <>
          {!isFollowing && (
            <PlatformIcon
              name={'plus'}
              size={16}
              color={followButtonTextStyle.color}
            />
          )}
          <Text
            variant='body'
            style={[styles.followButtonText, followButtonTextStyle]}
          >
            {isFollowing ? t('place.following') : t('place.follow')}
          </Text>
        </>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  followButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: verticalScale(8),
    paddingHorizontal: moderateScale(16),
    borderRadius: moderateScale(20),
    borderWidth: 1,
    alignSelf: 'center',
    gap: moderateScale(6),
    minWidth: moderateScale(100),
    height: verticalScale(40),
  },
  followButtonText: {
    fontWeight: '400',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
