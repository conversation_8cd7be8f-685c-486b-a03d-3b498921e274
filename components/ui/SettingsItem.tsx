import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from './Text';
import { PlatformIcon, PlatformIconName } from '@/components/ui/PlatformIcon';

interface Props {
  icon: PlatformIconName;
  label: string;
  rightElement?: React.ReactNode;
  onPress?: () => void;
}

export function SettingsItem({ icon, label, rightElement, onPress }: Props) {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.leftContent}>
        <PlatformIcon name={icon} size={20} color={colors.primary[950]} />
        <Text style={styles.label}>{label}</Text>
      </View>
      {rightElement || (
        <PlatformIcon
          name='chevron.right'
          size={16}
          color={colors.primary[950]}
        />
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: moderateScale(12),
    borderRadius: moderateScale(20),
    backgroundColor: colors.base[100],
    marginVertical: moderateScale(3),
    padding: moderateScale(16),
    height: moderateScale(50),

    // borderWidth: 1,
    // borderColor: colors.darkGrey[100],
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(12),
  },
  label: {
    color: colors.darkGrey[900],
    fontSize: moderateScale(16),
    fontWeight: '500',
  },
});
