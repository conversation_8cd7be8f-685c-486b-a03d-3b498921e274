import React, {
  forwardRef,
  useState,
  useImperativeHandle,
  useRef,
} from 'react';
import { StyleSheet, View, Dimensions, Modal as RNModal } from 'react-native';
import { Text } from './Text';
import { Button } from './Button';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import { Image } from 'expo-image';
import Carousel, {
  ICarouselInstance,
  Pagination,
} from 'react-native-reanimated-carousel';
import { useSharedValue } from 'react-native-reanimated';
import { ONBOARDING_STEPS } from '@/constants/Onboarding';
import * as Haptics from 'expo-haptics';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface OnboardingModalRef {
  open: () => void;
  close: () => void;
}

interface Props {
  onComplete: () => void;
  onStepChange: (step: number) => void;
}

export const OnboardingModal = forwardRef<OnboardingModalRef, Props>(
  function OnboardingModal({ onComplete, onStepChange }, ref) {
    const { t } = useTranslation();
    const [currentStep, setCurrentStep] = useState(0);
    const [isVisible, setIsVisible] = useState(false);
    const carouselRef = useRef<ICarouselInstance>(null);
    const progressValue = useSharedValue(0);

    useImperativeHandle(ref, () => ({
      open: () => {
        setIsVisible(true);
        // Initialize with step 0 when opening
        onStepChange(0);
      },
      close: () => {
        setIsVisible(false);
      },
    }));

    const isLastStep = currentStep === ONBOARDING_STEPS.length - 1;

    const handleNext = () => {
      // Add haptic feedback for Next button press
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      if (isLastStep) {
        // Complete onboarding
        onComplete();
        setIsVisible(false);
      } else {
        // Move to next step
        const nextStep = currentStep + 1;
        setCurrentStep(nextStep);
        progressValue.value = nextStep;
        carouselRef.current?.scrollTo({ index: nextStep, animated: true });
        // Notify parent about step change
        onStepChange(nextStep);
      }
    };

    const handleStepChange = (step: number) => {
      setCurrentStep(step);
      progressValue.value = step;
      carouselRef.current?.scrollTo({ index: step, animated: true });
      // Notify parent about step change
      onStepChange(step);
    };

    // Update progress value when step changes
    React.useEffect(() => {
      progressValue.value = currentStep;
    }, [currentStep, progressValue]);

    const renderStepItem = ({
      item,
    }: {
      item: (typeof ONBOARDING_STEPS)[0];
    }) => {
      const stepData = t(`onboardingModal.steps.${item.key}`, {
        returnObjects: true,
      }) as {
        title: string;
        description: string;
      };

      return (
        <View style={styles.stepContent}>
          {/* Image */}
          <View style={styles.imageContainer}>
            <Image
              source={item.image}
              style={styles.image}
              contentFit='contain'
            />
          </View>

          {/* Title */}
          <Text
            variant='h1'
            style={styles.title}
            align='center'
            numberOfLines={2}
          >
            {stepData.title}
          </Text>

          {/* Description */}
          <Text
            variant='caption'
            align='center'
            numberOfLines={2}
            color={colors.darkGrey[700]}
            style={styles.description}
          >
            {stepData.description}
          </Text>
        </View>
      );
    };

    const renderDots = () => {
      return (
        <Pagination.Custom
          progress={progressValue}
          data={ONBOARDING_STEPS}
          dotStyle={styles.dot}
          activeDotStyle={styles.activeDot}
          containerStyle={styles.dotsContainer}
          horizontal={true}
          onPress={(index) => handleStepChange(index)}
        />
      );
    };

    return (
      <RNModal
        visible={isVisible}
        transparent={true}
        animationType='fade'
        onRequestClose={() => setIsVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {/* Carousel */}
            <Carousel
              ref={carouselRef}
              data={ONBOARDING_STEPS}
              renderItem={renderStepItem}
              width={SCREEN_WIDTH - moderateScale(48)}
              height={verticalScale(370)}
              onSnapToItem={(index) => {
                setCurrentStep(index);
                progressValue.value = index;
                onStepChange(index);
              }}
              scrollAnimationDuration={300}
            />

            {/* Dots */}
            {renderDots()}

            {/* Button */}
            <Button
              variant='primary'
              title={
                isLastStep
                  ? t('onboardingModal.done')
                  : t('onboardingModal.next')
              }
              onPress={handleNext}
              style={styles.button}
            />
          </View>
        </View>
      </RNModal>
    );
  }
);

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: verticalScale(90), // Leave space for tabs at bottom
    backgroundColor: colors.blackTransparent.black07,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: colors.base[100],
    borderRadius: moderateScale(24),
    marginHorizontal: moderateScale(24),
    width: SCREEN_WIDTH - moderateScale(48),
    height: verticalScale(500),
    paddingHorizontal: moderateScale(20),
    paddingTop: moderateScale(0),
    paddingBottom: moderateScale(30),
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: verticalScale(90), // Leave space for tabs at bottom
  },
  stepContent: {
    alignItems: 'center',
  },
  imageContainer: {
    marginTop: verticalScale(24),
    width: moderateScale(300),
    height: verticalScale(220),
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.secondary[500],
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.4,
    shadowRadius: 25,
    elevation: 5,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  title: {
    marginBottom: verticalScale(16),
    width: '90%',
  },
  description: {
    width: '96%',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: verticalScale(8),
  },
  dot: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: moderateScale(4),
    backgroundColor: colors.primary[300],
    marginHorizontal: moderateScale(2),
  },
  activeDot: {
    backgroundColor: colors.primary[950],
    width: moderateScale(24),
    borderRadius: moderateScale(12),
  },
  button: {
    width: '100%',
  },
});
