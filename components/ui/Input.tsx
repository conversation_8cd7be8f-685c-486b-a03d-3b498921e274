import { colors } from '@/constants/Colors';
import { TextInput, TextInputProps, StyleSheet, View } from 'react-native';
import { moderateScale } from '@/utils/scaling';
import { Text } from './Text';
import { useState } from 'react';

interface Props extends TextInputProps {
  label?: string;
  error?: string;
  leftComponent?: React.ReactNode;
  rightComponent?: React.ReactNode;
}

export function Input({
  label,
  error,
  style,
  leftComponent,
  rightComponent,
  ...props
}: Props) {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label} variant='caption'>
          {label}
        </Text>
      )}
      <View
        style={[
          styles.inputContainer,
          error && styles.inputError,
          isFocused && styles.inputFocused,
        ]}
      >
        {leftComponent}
        <TextInput
          style={[
            styles.input,
            leftComponent ? styles.inputWithLeft : null,
            rightComponent ? styles.inputWithRight : null,
          ]}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
        {rightComponent}
      </View>
      {error && <Text style={styles.error}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: moderateScale(4),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: moderateScale(20),
    backgroundColor: colors.softGrey[400],
    height: moderateScale(56),
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  input: {
    flex: 1,
    padding: moderateScale(12),
    fontSize: moderateScale(16),
    color: colors.darkGrey[950],
    fontFamily: 'Urbanist',
  },
  inputWithLeft: {
    paddingLeft: 10,
  },
  inputWithRight: {
    paddingRight: 10,
  },
  inputFocused: {
    borderWidth: 1,
    borderColor: colors.primary[950],
  },
  inputError: {
    borderColor: colors.info.red,
  },
  label: {
    marginBottom: moderateScale(4),
  },
  error: {
    fontSize: moderateScale(12),
    color: colors.info.red,
  },
});
