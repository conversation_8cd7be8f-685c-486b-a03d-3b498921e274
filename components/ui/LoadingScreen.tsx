import React from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { verticalScale } from '@/utils/scaling';
import { Screen } from './Screen';
import { BackButton } from './BackButton';

interface LoadingScreenProps {
  message?: string;
  onBack?: () => void;
}

export function LoadingScreen({ message, onBack }: LoadingScreenProps) {
  return (
    <Screen>
      {onBack && <BackButton onPress={onBack} absolute />}
      <View style={styles.loadingContainer}>
        <ActivityIndicator size='large' color={colors.primary[500]} />
        {message && (
          <Text variant='body' style={styles.loadingText}>
            {message}
          </Text>
        )}
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.base[100],
  },
  loadingText: {
    marginTop: verticalScale(16),
  },
});
