import React, { forwardRef } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Modalize } from 'react-native-modalize';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { Modal } from './Modal';

export interface OptionItem {
  id: string;
  label: string;
  onPress: () => void;
  icon?: React.ReactNode;
  destructive?: boolean;
}

interface Props {
  options: OptionItem[];
  title?: string;
}

export const OptionsModal = forwardRef<Modalize, Props>(function OptionsModal(
  { options, title },
  ref
) {
  return (
    <Modal ref={ref}>
      <View style={styles.container}>
        {title && (
          <View style={styles.titleContainer}>
            <Text variant='caption' weight='bold' color={colors.base[950]}>
              {title}
            </Text>
          </View>
        )}
        <View style={styles.optionsContainer}>
          {options.map((option, index) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.option,
                index === options.length - 1 && styles.lastOption,
              ]}
              onPress={option.onPress}
            >
              {option.icon && (
                <View style={styles.iconContainer}>{option.icon}</View>
              )}
              <Text
                variant='body'
                color={option.destructive ? colors.info.red : colors.base[950]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: moderateScale(12),
  },
  titleContainer: {
    paddingHorizontal: moderateScale(12),
    marginBottom: moderateScale(16),
  },
  optionsContainer: {
    gap: moderateScale(4),
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: moderateScale(12),
    paddingHorizontal: moderateScale(12),
    borderRadius: moderateScale(12),
  },
  lastOption: {
    marginBottom: 0,
  },
  iconContainer: {
    marginRight: moderateScale(12),
    width: moderateScale(24),
  },
});
