import { colors } from '@/constants/Colors';
import { ActivityIndicator, StyleSheet, View, ViewProps } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text } from './Text';
interface Props extends ViewProps {
  withSafeArea?: boolean;
  isLoading?: boolean;
  error?: Error;
}

export function Screen({
  withSafeArea = true,
  isLoading = false,
  error,
  style,
  children,
  ...props
}: Props) {
  const Container = withSafeArea ? SafeAreaView : View;

  return (
    <Container style={[styles.container, style]} {...props}>
      {children}
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size='large' color={colors.primary[950]} />
        </View>
      )}
      {error && (
        <View style={styles.errorOverlay}>
          <Text>Error: {error.message}</Text>
        </View>
      )}
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.base[100],
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  errorOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
});
