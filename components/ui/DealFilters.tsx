import { StyleSheet, View, TouchableOpacity, ScrollView } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
import { MyDealStatusEnum } from '@/graphql/generated/graphql';

type FilterType = MyDealStatusEnum;

interface Props {
  activeFilter: FilterType;
  onFilterChange: (filter: FilterType) => void;
  counts: {
    redeemable: number;
    upcoming: number;
    redeemed: number;
  };
}

export function DealFilters({ activeFilter, onFilterChange, counts }: Props) {
  const { t } = useTranslation();

  const FilterButton = ({
    type,
    label,
    count,
  }: {
    type: FilterType;
    label: string;
    count: number;
  }) => (
    <TouchableOpacity
      onPress={() => onFilterChange(type)}
      style={[
        styles.filterButton,
        activeFilter === type && styles.filterButtonActive,
      ]}
    >
      <Text
        style={[
          styles.filterText,
          activeFilter === type && styles.filterTextActive,
        ]}
      >
        {label} ({count})
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.container}
    >
      <FilterButton
        type={MyDealStatusEnum.Redeemable}
        label={t(`deals.filters.redeemable`)}
        count={counts.redeemable}
      />
      <FilterButton
        type={MyDealStatusEnum.Upcoming}
        label={t(`deals.filters.upcoming`)}
        count={counts.upcoming}
      />
      <FilterButton
        type={MyDealStatusEnum.Redeemed}
        label={t(`deals.filters.redeemed`)}
        count={counts.redeemed}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 0,
  },
  filtersContainer: {
    flexDirection: 'row',
    gap: moderateScale(12),
    marginTop: moderateScale(16),
  },
  filterButton: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(8),
    borderRadius: moderateScale(20),
    backgroundColor: colors.softGrey[400],
    borderWidth: 1,
    borderColor: colors.base[950],
    marginHorizontal: moderateScale(4),
  },
  filterButtonActive: {
    backgroundColor: colors.base[950],
  },
  filterText: {
    color: colors.base[950],
    fontSize: moderateScale(14),
  },
  filterTextActive: {
    color: colors.base[100],
    fontWeight: 'bold',
  },
});
