import { colors } from '@/constants/Colors';
import { StyleSheet, View, Image } from 'react-native';
import { Text } from './Text';
import { moderateScale } from '@/utils/scaling';
import { useTranslation } from 'react-i18next';
interface Props {
  number: number;
  variant?: 'light' | 'dark' | 'primary' | 'secondary' | 'transparent';
}

export function CurrencyTag({ number, variant = 'light' }: Props) {
  const { t } = useTranslation();
  return (
    <View style={[styles.container, styles[variant]]}>
      <Text
        variant='caption'
        color={variant === 'light' ? colors.base[950] : colors.base[100]}
        style={styles.text}
      >
        {t('common.save')}
      </Text>

      <Image
        source={require('@/assets/images/dirham.avif')}
        style={[styles.image]}
        resizeMode='contain'
        tintColor={variant === 'light' ? colors.base[950] : colors.base[100]}
      />

      <Text
        variant='caption'
        color={variant === 'light' ? colors.base[950] : colors.base[100]}
      >
        {number}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateScale(4),
    borderRadius: moderateScale(20),
    gap: moderateScale(4),
  },
  light: {
    backgroundColor: colors.softGrey[400],
  },
  dark: {
    backgroundColor: colors.darkGrey[900],
  },
  primary: {
    backgroundColor: colors.primary[950],
  },
  secondary: {
    backgroundColor: colors.secondary[500],
  },
  transparent: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
  image: {
    width: moderateScale(14),
    height: moderateScale(14),
  },
  text: {
    marginRight: moderateScale(4),
  },
});
