import React, {
  forwardRef,
  useMemo,
  useState,
  useEffect,
  useCallback,
} from 'react';
import { StyleSheet, TouchableOpacity, View, TextInput } from 'react-native';
import { Modalize } from 'react-native-modalize';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale, verticalScale } from '@/utils/scaling';
import { Modal } from './Modal';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { ScrollView } from 'react-native';
import { Button } from './Button';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import { useTranslation } from 'react-i18next';
import { DistanceFilter } from './DistanceFilter';
import { IconSymbol } from './IconSymbol';
import { ReviewsCountSlider } from './ReviewsCountSlider';
import { DEFAULT_RADIUS } from '@/components/home/<USER>';

interface FacetValue {
  value: string;
  isSelected: boolean;
  originalValue?: string;
  indexName?: string;
  stars?: boolean[];
}

interface RangeValue {
  min: number;
  max: number;
}

interface FilterGroup {
  title: string;
  attribute: string;
  values?: FacetValue[];
  isSort?: boolean;
  type?: 'range' | 'singleRange';
  range?: RangeValue;
  currentRefinement?: RangeValue;
  refine?: (value: RangeValue | [number, number]) => void;
}

interface Props {
  filters: FilterGroup[];
  onFilterToggle: (
    attribute: string,
    value: string,
    indexName?: string
  ) => void;
  onClearAll: () => void;
  onShowResults: () => void;
  totalResults: number;
  searchLocation?: (query: string) => void;
  toggleShowMore?: () => void;
  canToggleShowMore?: boolean;
  isShowingMore?: boolean;
  radius?: number;
  setRadius?: (radius: number) => void;
  resetRadius?: () => void;
  hasDistanceChanged?: boolean;
  shouldDisableGeoFeatures?: boolean;
  ensureLocationPermission?: () => Promise<boolean>;
}

// Type guard for FilterGroup with values
function hasValues(
  group: FilterGroup
): group is FilterGroup & { values: FacetValue[] } {
  return group.values !== undefined;
}

// Helper function to check if range values are valid
function isValidRange(range: RangeValue) {
  return (
    typeof range.min === 'number' &&
    typeof range.max === 'number' &&
    !isNaN(range.min) &&
    !isNaN(range.max) &&
    isFinite(range.min) &&
    isFinite(range.max) &&
    range.min !== range.max
  );
}

// Group Title component
const GroupTitle: React.FC<{ title: string }> = ({ title }) => (
  <Text
    variant='h3'
    weight='bold'
    color={colors.base[950]}
    style={styles.groupTitle}
  >
    {title}
  </Text>
);

// Filter Option component
interface FilterOptionProps {
  item: FacetValue;
  attribute: string;
  onPress: () => void;
  renderCustomContent?: () => React.ReactNode;
}

const FilterOption: React.FC<FilterOptionProps> = ({
  item,
  attribute,
  onPress,
  renderCustomContent,
}) => (
  <TouchableOpacity
    key={`${attribute}-${item.value}`}
    style={[styles.option, item.isSelected && styles.selectedOption]}
    onPress={onPress}
  >
    {renderCustomContent ? (
      renderCustomContent()
    ) : (
      <Text
        variant='caption'
        color={item.isSelected ? colors.primary[950] : colors.base[950]}
        weight={item.isSelected ? 'bold' : 'regular'}
      >
        {item.value}
      </Text>
    )}
  </TouchableOpacity>
);

// Selected Filter Chip component
interface SelectedFilterProps {
  filter: {
    value: string;
    attribute: string;
    originalValue?: string;
    indexName?: string;
  };
  onPress: () => void;
}

const SelectedFilterChip: React.FC<SelectedFilterProps> = ({
  filter,
  onPress,
}) => (
  <TouchableOpacity
    key={`${filter.attribute}-${filter.value}-selected`}
    style={[styles.option, styles.selectedFilterChip]}
    onPress={onPress}
  >
    {filter.attribute === 'places.rates.google' && (
      <FontAwesome6
        name='star'
        size={moderateScale(14)}
        color={colors.primary[950]}
        solid
      />
    )}
    <Text variant='caption' weight='bold' color={colors.primary[950]}>
      {filter.value}
    </Text>
    <FontAwesome6
      name='xmark'
      size={moderateScale(12)}
      color={colors.primary[950]}
    />
  </TouchableOpacity>
);

// Selected Filters Section component
interface SelectedFiltersSectionProps {
  selectedFilters: {
    value: string;
    attribute: string;
    title: string;
    originalValue?: string;
    indexName?: string;
  }[];
  onFilterToggle: (
    attribute: string,
    value: string,
    indexName?: string
  ) => void;
}

const SelectedFiltersSection: React.FC<SelectedFiltersSectionProps> = ({
  selectedFilters,
  onFilterToggle,
}) => {
  if (selectedFilters.length === 0) return null;

  return (
    <View style={styles.selectedFiltersSection}>
      <View style={styles.selectedFiltersContainer}>
        {selectedFilters.map((filter) => (
          <SelectedFilterChip
            key={`${filter.attribute}-${filter.value}-selected`}
            filter={filter}
            onPress={() =>
              onFilterToggle(
                filter.attribute,
                filter.originalValue || filter.value,
                filter.indexName
              )
            }
          />
        ))}
      </View>
    </View>
  );
};

// Sort Group component
interface SortGroupProps {
  group: FilterGroup;
  onFilterToggle: (
    attribute: string,
    value: string,
    indexName?: string
  ) => void;
}

const SortGroup: React.FC<SortGroupProps> = ({ group, onFilterToggle }) => {
  return (
    <View
      key={group.attribute}
      style={[styles.filterGroup, { marginBottom: moderateScale(16) }]}
    >
      <GroupTitle title={group.title} />
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={[styles.optionsContainer, styles.sortOptionsContainer]}>
          {group.values?.map((item) => (
            <TouchableOpacity
              key={`${group.attribute}-${item.value}`}
              style={[styles.option, item.isSelected && styles.selectedOption]}
              onPress={() =>
                onFilterToggle(group.attribute, item.value, item.indexName)
              }
            >
              <Text
                variant='caption'
                color={item.isSelected ? colors.primary[950] : colors.base[950]}
                weight={item.isSelected ? 'bold' : 'regular'}
              >
                {item.value}
              </Text>
              {/* sort icon */}
              <IconSymbol
                name='arrow.up.arrow.down'
                size={moderateScale(12)}
                color={item.isSelected ? colors.primary[950] : colors.base[950]}
              />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

// Rating Group component
interface RatingGroupProps {
  group: FilterGroup;
  onFilterToggle: (
    attribute: string,
    value: string,
    indexName?: string
  ) => void;
}

const RatingGroup: React.FC<RatingGroupProps> = ({ group, onFilterToggle }) => {
  if (group.attribute !== 'places.rates.google' || !hasValues(group))
    return null;

  // Log for debugging
  // console.log('Rating filter group:', group);
  // console.log('Rating filter values:', group.values);

  return (
    <View key={group.attribute} style={styles.filterGroup}>
      <GroupTitle title={group.title} />
      <View style={styles.optionsContainer}>
        {group.values.map((item) => (
          <TouchableOpacity
            key={`${group.attribute}-${item.value}`}
            style={[
              styles.option,
              styles.ratingOptionButton,
              item.isSelected && styles.selectedOption,
            ]}
            onPress={() =>
              onFilterToggle(group.attribute, item.originalValue || item.value)
            }
          >
            <View style={styles.ratingOption}>
              <FontAwesome6
                name='star'
                size={moderateScale(14)}
                color={
                  item.isSelected ? colors.primary[950] : colors.yellow[900]
                }
                solid
              />
              <Text
                variant='caption'
                color={item.isSelected ? colors.primary[950] : colors.base[950]}
                weight={item.isSelected ? 'bold' : 'regular'}
              >
                {item.value}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

// City Group component
interface CityGroupProps {
  group: FilterGroup;
  onFilterToggle: (
    attribute: string,
    value: string,
    indexName?: string
  ) => void;
  searchLocation?: (query: string) => void;
  toggleShowMore?: () => void;
  canToggleShowMore?: boolean;
  isShowingMore?: boolean;
}

const CityGroup: React.FC<CityGroupProps> = ({
  group,
  onFilterToggle,
  searchLocation,
  toggleShowMore,
  canToggleShowMore,
  isShowingMore,
}) => {
  const { t } = useTranslation();
  const [locationSearch, setLocationSearch] = useState('');

  // Handle city search with debounce using useCallback to prevent recreating the function on every render
  const debouncedSearch = useCallback(
    (query: string) => {
      if (searchLocation) {
        searchLocation(query);
      }
    },
    [searchLocation]
  );

  // Handle text input change
  const handleSearchChange = useCallback(
    (text: string) => {
      setLocationSearch(text);
      // If text is cleared, immediately trigger search to reset results
      if (text === '') {
        debouncedSearch('');
      }
    },
    [debouncedSearch]
  );

  // Use debounced effect for search
  useEffect(() => {
    // Skip the effect if the search is empty (handled by handleSearchChange)
    if (locationSearch === '') {
      return;
    }

    const timer = setTimeout(() => {
      debouncedSearch(locationSearch);
    }, 300);

    return () => clearTimeout(timer);
  }, [locationSearch, debouncedSearch]);

  if (group.attribute !== 'places.area' || !hasValues(group)) return null;

  return (
    <View key={group.attribute} style={styles.filterGroup}>
      <GroupTitle title={group.title} />

      <View style={styles.searchContainer}>
        <FontAwesome6
          name='magnifying-glass'
          size={moderateScale(16)}
          color={colors.darkGrey[500]}
        />
        <TextInput
          placeholder={t('filters.searchLocation')}
          value={locationSearch}
          onChangeText={handleSearchChange}
          style={styles.searchInput}
          placeholderTextColor={colors.darkGrey[500]}
        />
        {locationSearch !== '' && (
          <TouchableOpacity
            onPress={() => handleSearchChange('')}
            style={styles.clearSearchButton}
          >
            <FontAwesome6
              name='xmark'
              size={moderateScale(16)}
              color={colors.darkGrey[500]}
            />
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.citiesContainer}>
        {group.values.map((item) => (
          <TouchableOpacity
            key={`${group.attribute}-${item.value}`}
            style={styles.cityItem}
            onPress={() => {
              onFilterToggle(group.attribute, item.value);
            }}
          >
            <View style={styles.cityInfo}>
              <Text
                variant='caption'
                color={item.isSelected ? colors.primary[950] : colors.base[950]}
                weight={item.isSelected ? 'bold' : 'regular'}
              >
                {item.value || item.originalValue}
              </Text>
            </View>
            <View
              style={[
                styles.checkbox,
                item.isSelected && styles.checkboxSelected,
              ]}
            >
              {item.isSelected && (
                <FontAwesome6
                  name='check'
                  size={moderateScale(10)}
                  color={colors.base[100]}
                />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {canToggleShowMore && (
        <TouchableOpacity
          style={styles.showMoreButton}
          onPress={toggleShowMore}
        >
          <Text weight='bold' variant='body' color={colors.primary[950]}>
            {isShowingMore ? t('filters.showLess') : t('filters.showMore')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

// Other Filter Group component
interface OtherFilterGroupProps {
  group: FilterGroup;
  onFilterToggle: (
    attribute: string,
    value: string,
    indexName?: string
  ) => void;
}

const OtherFilterGroup: React.FC<OtherFilterGroupProps> = ({
  group,
  onFilterToggle,
}) => {
  if (
    !hasValues(group) ||
    group.attribute === 'places.area' ||
    group.attribute === 'sort' ||
    group.attribute === 'places.rates.google'
  )
    return null;

  return (
    <View key={group.attribute} style={styles.filterGroup}>
      <GroupTitle title={group.title} />
      <View style={styles.optionsContainer}>
        {group.values.map((item) => (
          <FilterOption
            key={`${group.attribute}-${item.value}`}
            item={item}
            attribute={group.attribute}
            onPress={() => {
              onFilterToggle(group.attribute, item.originalValue || item.value);
            }}
          />
        ))}
      </View>
    </View>
  );
};

// Price Range Group component
const PriceRangeGroup: React.FC<{
  group: FilterGroup;
  onPriceRangeChange?: (hasChanged: boolean) => void;
  isCleared?: boolean;
}> = ({ group, onPriceRangeChange, isCleared }) => {
  const { t } = useTranslation();
  const [localRange, setLocalRange] = useState<RangeValue>({
    min: group.currentRefinement?.min ?? group.range?.min ?? 0,
    max: group.currentRefinement?.max ?? group.range?.max ?? 100,
  });

  // Reset local range when isCleared changes
  useEffect(() => {
    if (isCleared && group.range && isValidRange(group.range) && group.refine) {
      // Reset to initial range values
      setLocalRange({
        min: group.range.min,
        max: group.range.max,
      });
      // Call refine with initial range values to reset the refinement
      group.refine({ min: group.range.min, max: group.range.max });
      onPriceRangeChange?.(false);
    }
  }, [isCleared, group.range, group.refine]);

  // Update local range when the range bounds or current refinement changes
  useEffect(() => {
    if (group.range && isValidRange(group.range)) {
      const newRange = {
        min: group.currentRefinement?.min ?? group.range.min,
        max: group.currentRefinement?.max ?? group.range.max,
      };
      setLocalRange(newRange);

      // Update the hasChanged state based on whether the range is different from initial
      const hasChanged = group.currentRefinement
        ? group.currentRefinement.min !== group.range.min ||
          group.currentRefinement.max !== group.range.max
        : false;
      onPriceRangeChange?.(hasChanged);
    }
  }, [group.range?.min, group.range?.max, group.currentRefinement]);

  const handleValuesChange = (values: number[]) => {
    setLocalRange({ min: values[0], max: values[1] });
    // Check if the range has changed from the initial range
    const hasChanged = group.range
      ? values[0] !== group.range.min || values[1] !== group.range.max
      : false;
    onPriceRangeChange?.(hasChanged);
  };

  const handleValuesChangeFinish = (values: number[]) => {
    if (group.refine) {
      group.refine({ min: values[0], max: values[1] });
    }
  };

  if (
    group.attribute !== 'places.price_per_person' ||
    group.type !== 'range' ||
    !group.range ||
    !isValidRange(group.range)
  )
    return null;

  return (
    <View key={group.attribute} style={styles.filterGroup}>
      <GroupTitle title={group.title} />
      <View style={styles.priceRangeContainer}>
        <View style={styles.priceLabels}>
          <Text variant='caption'>
            {Math.round(localRange.min)} {t('common.currency')}
          </Text>
          <Text variant='caption'>
            {Math.round(localRange.max)} {t('common.currency')}
          </Text>
        </View>
        <MultiSlider
          values={[localRange.min, localRange.max]}
          min={group.range.min}
          max={group.range.max}
          step={Math.max(
            1,
            Math.floor((group.range.max - group.range.min) / 100)
          )}
          allowOverlap={false}
          snapped
          selectedStyle={{ backgroundColor: colors.primary[950] }}
          unselectedStyle={{ backgroundColor: colors.primary[300] }}
          containerStyle={styles.sliderContainer}
          trackStyle={styles.sliderTrack}
          markerStyle={styles.sliderMarker}
          onValuesChange={handleValuesChange}
          onValuesChangeFinish={handleValuesChangeFinish}
          sliderLength={moderateScale(320)}
        />
      </View>
    </View>
  );
};

// Main FilterModal component
export const FilterModal = forwardRef<Modalize, Props>(function FilterModal(
  {
    filters,
    onFilterToggle,
    onClearAll,
    onShowResults,
    totalResults,
    searchLocation,
    toggleShowMore,
    canToggleShowMore,
    isShowingMore,
    radius,
    setRadius,
    resetRadius,
    hasDistanceChanged,
    shouldDisableGeoFeatures,
    ensureLocationPermission,
  },
  ref
) {
  const { t } = useTranslation();
  const [hasPriceRangeChanged, setHasPriceRangeChanged] = useState(false);
  const [hasReviewsCountChanged, setHasReviewsCountChanged] = useState(false);
  const [isCleared, setIsCleared] = useState(false);
  const [minReviews, setMinReviews] = useState(0);

  // Enhanced setRadius that requests location permission
  const handleSetRadius = useCallback(
    async (newRadius: number) => {
      if (setRadius) {
        // If user is setting a distance filter and we have ensureLocationPermission, request it
        if (
          newRadius !== (radius || DEFAULT_RADIUS) &&
          ensureLocationPermission
        ) {
          const permissionGranted = await ensureLocationPermission();
          // Only set radius if permission was granted
          if (permissionGranted) {
            setRadius(newRadius);
          }
          // If permission denied, don't set the radius - user can't use distance filtering
        } else {
          // No permission check needed (e.g., resetting to default) or no ensureLocationPermission function
          setRadius(newRadius);
        }
      }
    },
    [setRadius, radius, ensureLocationPermission]
  );

  // Find the reviews count filter group to get min/max values
  const reviewsCountGroup = useMemo(() => {
    return filters.find(
      (group) =>
        group.attribute === 'places.rates.reviews_count' &&
        group.type === 'singleRange'
    );
  }, [filters]);

  // Update minReviews when the filter changes
  useEffect(() => {
    if (
      reviewsCountGroup?.currentRefinement &&
      typeof reviewsCountGroup.currentRefinement.min === 'number'
    ) {
      setMinReviews(reviewsCountGroup.currentRefinement.min);
    } else if (
      reviewsCountGroup?.range &&
      typeof reviewsCountGroup.range.min === 'number'
    ) {
      setMinReviews(reviewsCountGroup.range.min);
    }
  }, [reviewsCountGroup]);

  // Handle reviews count change
  const handleReviewsCountChange = useCallback(
    (value: number) => {
      if (reviewsCountGroup && reviewsCountGroup.refine) {
        const max = reviewsCountGroup.range?.max || 1000;
        reviewsCountGroup.refine({ min: value, max });
        setHasReviewsCountChanged(value > (reviewsCountGroup.range?.min || 0));
      }
    },
    [reviewsCountGroup]
  );

  // Get all selected filters
  const selectedFilters = useMemo(() => {
    return filters.flatMap((group) => {
      if (!hasValues(group) || group.isSort) return [];
      return group.values
        .filter((item) => item.isSelected)
        .map((item) => ({
          value: item.value,
          attribute: group.attribute,
          title: group.title,
          originalValue: item.originalValue,
          indexName: item.indexName,
        }));
    });
  }, [filters]);

  const hasSelectedFilters =
    selectedFilters.length > 0 ||
    hasPriceRangeChanged ||
    hasReviewsCountChanged ||
    hasDistanceChanged;

  const handleClearAll = useCallback(() => {
    onClearAll();
    setHasPriceRangeChanged(false);
    setHasReviewsCountChanged(false);
    setIsCleared(true);
    setMinReviews(reviewsCountGroup?.range?.min || 0);

    // Reset distance filter if available
    if (resetRadius) {
      resetRadius();
    }

    // Reset the cleared state after a short delay to allow the price range to reset
    setTimeout(() => {
      setIsCleared(false);
    }, 100);
  }, [onClearAll, resetRadius, reviewsCountGroup]);

  return (
    <Modal ref={ref} panGestureEnabled={false}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text variant='h2'>{t('filters.title')}</Text>
          {hasSelectedFilters && (
            <TouchableOpacity
              onPress={handleClearAll}
              style={styles.clearButton}
            >
              <Text variant='caption' weight='bold' color={colors.base[100]}>
                {t('filters.clearAll')}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Selected Filters Section */}
          <SelectedFiltersSection
            selectedFilters={selectedFilters}
            onFilterToggle={onFilterToggle}
          />

          {/* Sort Group */}
          <SortGroup
            key={`sort-${filters[0].attribute}`}
            group={filters[0]}
            onFilterToggle={onFilterToggle}
          />

          {/* Distance Filter */}
          {radius !== undefined &&
            setRadius !== undefined &&
            !shouldDisableGeoFeatures && (
              <DistanceFilter
                radius={radius}
                setRadius={handleSetRadius}
                title={t('filters.groups.distance')}
              />
            )}

          {/* Rating Group */}
          {filters.map((group) => (
            <RatingGroup
              key={`rating-${group.attribute}`}
              group={group}
              onFilterToggle={onFilterToggle}
            />
          ))}

          {/* Reviews Count Slider */}
          {reviewsCountGroup && (
            <ReviewsCountSlider
              minReviews={minReviews}
              setMinReviews={handleReviewsCountChange}
              min={reviewsCountGroup.range?.min || 0}
              max={reviewsCountGroup.range?.max || 1000}
              title={reviewsCountGroup.title}
              isCleared={isCleared}
            />
          )}

          {/* Other Filter Groups */}
          {filters.map((group) => (
            <OtherFilterGroup
              key={`other-${group.attribute}`}
              group={group}
              onFilterToggle={onFilterToggle}
            />
          ))}

          {/* Cities Filter Group */}
          {filters.map((group) => (
            <CityGroup
              key={`city-${group.attribute}`}
              group={group}
              onFilterToggle={onFilterToggle}
              searchLocation={searchLocation}
              toggleShowMore={toggleShowMore}
              canToggleShowMore={canToggleShowMore}
              isShowingMore={isShowingMore}
            />
          ))}

          {/* Price Range Group */}
          {filters.map((group) => (
            <PriceRangeGroup
              key={`price-${group.attribute}`}
              group={group}
              onPriceRangeChange={setHasPriceRangeChanged}
              isCleared={isCleared}
            />
          ))}
        </ScrollView>

        {/* Show Results Button */}
        <View style={styles.footer}>
          <Button
            title={
              totalResults > 0
                ? `${t('filters.showResults')} (${totalResults})`
                : t('filters.noResults')
            }
            onPress={totalResults > 0 ? onShowResults : undefined}
            disabled={totalResults === 0}
          />
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: moderateScale(16),
    alignSelf: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: moderateScale(16),
    marginVertical: moderateScale(16),
  },
  clearButton: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.base[950],
    width: moderateScale(100),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
  },
  content: {
    flex: 1,
    height: verticalScale(500),
  },
  selectedFiltersSection: {
    marginBottom: moderateScale(24),
  },

  selectedFiltersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: moderateScale(8),
  },
  selectedFilterChip: {
    backgroundColor: colors.primary[200],
    borderColor: colors.primary[950],
  },
  filterGroup: {
    marginBottom: moderateScale(24),
    backgroundColor: colors.softGrey[700],
    padding: moderateScale(16),
    borderRadius: moderateScale(16),
    minWidth: '100%',
  },
  groupTitle: {
    marginBottom: moderateScale(12),
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(12),
    backgroundColor: colors.base[100],
    borderWidth: 1,
    borderColor: colors.darkGrey[300],
    marginBottom: moderateScale(12),
    height: moderateScale(56),
    borderRadius: moderateScale(72),
  },
  searchInput: {
    flex: 1,
    marginLeft: moderateScale(8),
    color: colors.base[950],
    fontSize: moderateScale(16),
    fontFamily: 'Urbanist',
  },
  citiesContainer: {
    gap: moderateScale(8),
  },
  cityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: moderateScale(4),
  },
  cityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
  },
  checkbox: {
    width: moderateScale(15),
    height: moderateScale(15),
    borderRadius: moderateScale(3),
    borderWidth: 1,
    borderColor: colors.darkGrey[300],
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    backgroundColor: colors.primary[950],
    borderColor: colors.primary[950],
  },
  showMoreButton: {
    alignSelf: 'center',
    width: '40%',
    alignItems: 'center',
    paddingVertical: moderateScale(12),
    marginTop: moderateScale(8),
    borderWidth: 1,
    borderColor: colors.primary[950],
    borderRadius: moderateScale(72),
    backgroundColor: colors.base[100],
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: moderateScale(8),
  },
  option: {
    height: moderateScale(40),
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    borderRadius: moderateScale(24),
    borderWidth: 1,
    borderColor: colors.darkGrey[300],
    gap: moderateScale(4),
  },
  selectedOption: {
    backgroundColor: colors.primary[200],
    borderColor: colors.primary[950],
  },
  footer: {
    paddingVertical: moderateScale(16),
    borderTopWidth: 1,
    borderTopColor: colors.primary[200],
  },
  sortOptionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'nowrap',
    gap: moderateScale(8),
  },
  ratingOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
  },
  priceRangeContainer: {
    marginTop: moderateScale(16),
  },
  priceLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: moderateScale(8),
  },
  // Removed the unused `reviewsCountLabel` style definition.
  sliderContainer: {
    height: moderateScale(10),
    marginBottom: moderateScale(16),
    alignItems: 'center',
  },
  sliderTrack: {
    height: moderateScale(8),
    borderRadius: moderateScale(8),
  },
  sliderMarker: {
    position: 'relative',
    top: moderateScale(4),
    height: moderateScale(16),
    width: moderateScale(16),
    borderRadius: moderateScale(8),
    backgroundColor: colors.base[100],
    borderWidth: moderateScale(2),
    borderColor: colors.primary[700],
    shadowColor: colors.primary[950],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  clearSearchButton: {
    padding: moderateScale(4),
  },
  ratingOptionButton: {
    minWidth: moderateScale(100),
    justifyContent: 'center',
  },
});
