import { StyleSheet, View } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { moderateScale } from '@/utils/scaling';
import { MyDeal as GraphQLMyDeal } from '@/graphql/generated/graphql';
import { dealBackgrounds } from './types/deal';
import { calculateDealProgress, formatDealRemainingTime } from '@/utils/time';
import { useTranslation } from 'react-i18next';

interface Props {
  myDeal: GraphQLMyDeal;
}

export function DealPendingProgress({ myDeal }: Props) {
  const { t } = useTranslation();
  const { deal, reuse_after, reserve_slot } = myDeal;
  const reservedSlotDate = reserve_slot?.slot.from;
  const { deal_type: type } = deal;

  // Return early if required data is missing
  if (!reservedSlotDate || !reuse_after) {
    return (
      <View style={styles.container}>
        <View style={styles.progressContainer}>
          <View style={[styles.progressFill, { width: '0%' }]} />
          <View style={styles.progressRemaining} />
          <Text
            variant='body'
            style={[styles.expiryText, { color: dealBackgrounds[type] }]}
          >
            {t('common.notFound')}
          </Text>
        </View>
      </View>
    );
  }

  // Parse dates
  const reservedDate = new Date(reservedSlotDate);
  const reuseDate = new Date(reuse_after);

  // Calculate progress percentage (only what we need)
  const { progressPercentage } = calculateDealProgress(reservedDate, reuseDate);

  // Get formatted time text using date-fns built-in strings
  const timeText = formatDealRemainingTime(reuseDate);

  return (
    <View style={styles.container}>
      <View style={styles.progressContainer}>
        <View
          style={[styles.progressFill, { width: `${progressPercentage}%` }]}
        />
        <View style={styles.progressRemaining} />
        <Text
          variant='body'
          style={[styles.expiryText, { color: dealBackgrounds[type] }]}
        >
          {timeText}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressContainer: {
    flexDirection: 'row',
    height: moderateScale(56),
    width: '100%',
    borderRadius: moderateScale(28),
    overflow: 'hidden',
    backgroundColor: `${colors.base[100]}50`,
  },
  progressFill: {
    backgroundColor: colors.base[100],
    justifyContent: 'center',
  },
  progressRemaining: {
    width: '20%',
  },
  expiryText: {
    position: 'absolute',
    fontWeight: 'bold',
    width: '100%',
    textAlign: 'center',
    alignSelf: 'center',
  },
});
