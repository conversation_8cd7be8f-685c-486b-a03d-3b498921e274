import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from './Text';
import { colors } from '@/constants/Colors';
import { verticalScale, moderateScale } from '@/utils/scaling';
import { Screen } from './Screen';
import { BackButton } from './BackButton';
import { IconSymbol } from './IconSymbol';
import { Button } from './Button';

interface ErrorScreenProps {
  message: string;
  onBack?: () => void;
  showBackButton?: boolean;
}

export function ErrorScreen({
  message,
  onBack,
  showBackButton = true,
}: ErrorScreenProps) {
  return (
    <Screen>
      {onBack && <BackButton onPress={onBack} absolute transparent />}
      <View style={styles.errorContainer}>
        <IconSymbol
          name='exclamationmark.triangle'
          size={40}
          color={colors.base[950]}
        />
        <Text
          variant='body'
          weight='bold'
          align='center'
          style={styles.errorText}
        >
          {message}
        </Text>
        {showBackButton && onBack && (
          <Button
            title='Go Back'
            onPress={onBack}
            style={styles.errorButton}
            variant='primary'
          />
        )}
      </View>
    </Screen>
  );
}

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(24),
  },
  errorText: {
    marginTop: verticalScale(16),
  },
  errorButton: {
    width: '60%',
    marginTop: verticalScale(24),
  },
});
